/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/wallet/notice";
exports.ids = ["pages/wallet/notice"];
exports.modules = {

/***/ "../../packages/assets/images/setting.png":
/*!************************************************!*\
  !*** ../../packages/assets/images/setting.png ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/setting.1f39b21b.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fsetting.1f39b21b.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9zZXR0aW5nLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxnTUFBZ00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3NldHRpbmcucG5nPzkyYjUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL3NldHRpbmcuMWYzOWIyMWIucG5nXCIsXCJoZWlnaHRcIjoyNCxcIndpZHRoXCI6MjQsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGc2V0dGluZy4xZjM5YjIxYi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/setting.png\n");

/***/ }),

/***/ "../../packages/assets/images/wallet/notice.png":
/*!******************************************************!*\
  !*** ../../packages/assets/images/wallet/notice.png ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/notice.7bc1217f.png\",\"height\":224,\"width\":224,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnotice.7bc1217f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvbm90aWNlLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxnTUFBZ00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9ub3RpY2UucG5nPzI4OGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL25vdGljZS43YmMxMjE3Zi5wbmdcIixcImhlaWdodFwiOjIyNCxcIndpZHRoXCI6MjI0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm5vdGljZS43YmMxMjE3Zi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/notice.png\n");

/***/ }),

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fwallet%2Fnotice&preferredRegion=&absolutePagePath=.%2Fpages%2Fwallet%2Fnotice.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fwallet%2Fnotice&preferredRegion=&absolutePagePath=.%2Fpages%2Fwallet%2Fnotice.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"../../node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/wallet/notice.tsx */ \"./pages/wallet/notice.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__, private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/wallet/notice\",\n        pathname: \"/wallet/notice\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_wallet_notice_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fwallet%2Fnotice&preferredRegion=&absolutePagePath=.%2Fpages%2Fwallet%2Fnotice.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/core/reset.css */ \"../../node_modules/@tamagui/core/reset.css\");\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/font-inter/css/400.css */ \"../../node_modules/@tamagui/font-inter/css/400.css\");\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/font-inter/css/700.css */ \"../../node_modules/@tamagui/font-inter/css/700.css\");\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var raf_polyfill__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! raf/polyfill */ \"raf/polyfill\");\n/* harmony import */ var raf_polyfill__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(raf_polyfill__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! app/provider/NextTamaguiProvider */ \"../../packages/app/provider/NextTamaguiProvider.tsx\");\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/script */ \"../../node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([app_stores_walletStore__WEBPACK_IMPORTED_MODULE_9__, _my_ui__WEBPACK_IMPORTED_MODULE_10__, app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_11__]);\n([app_stores_walletStore__WEBPACK_IMPORTED_MODULE_9__, _my_ui__WEBPACK_IMPORTED_MODULE_10__, app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n\nif (false) {}\nfunction MyApp({ Component, pageProps }) {\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_9__.useWalletStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        walletStore.getVault().then((_vault)=>{\n            // 如果没有密码\n            if (!_vault) {\n                router.push(\"/wallet/password\");\n            }\n        });\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_5___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Coinbase v2\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Coinbase v2\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        dangerouslySetInnerHTML: {\n                            // the first time this runs you'll get the full CSS including all themes\n                            // after that, it will only return CSS generated since the last call\n                            __html: _my_ui__WEBPACK_IMPORTED_MODULE_10__.config.getNewCSS()\n                        }\n                    }, \"tamagui-new-css\", false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        dangerouslySetInnerHTML: {\n                            __html: _my_ui__WEBPACK_IMPORTED_MODULE_10__.config.getCSS({\n                                // if you are using \"outputCSS\" option, you should use this \"exclude\"\n                                // if not, then you can leave the option out\n                                exclude:  false ? 0 : null\n                            })\n                        }\n                    }, \"tamagui-css\", false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        id: \"t_unmounted\",\n                        strategy: \"beforeInteractive\",\n                        children: `document.documentElement.classList.add('t_unmounted')`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_provider_NextTamaguiProvider__WEBPACK_IMPORTED_MODULE_11__.NextTamaguiProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_app.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWdDO0FBQ1E7QUFDQTtBQUNuQjtBQUdPO0FBRTBDO0FBQ3ZDO0FBQ0M7QUFDdUI7QUFDdEI7QUFDWTtBQUU3QyxJQUFJTyxLQUF5QixFQUFjLEVBRTFDO0FBRUQsU0FBU0UsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBa0I7SUFDckQsTUFBTUMsU0FBU04sNERBQVNBO0lBQ3hCLE1BQU1PLGNBQWNULHNFQUFjQTtJQUNsQ0MsZ0RBQVNBLENBQUM7UUFDUlEsWUFBWUMsUUFBUSxHQUNqQkMsSUFBSSxDQUFDQyxDQUFBQTtZQUNKLFNBQVM7WUFDVCxJQUFJLENBQUNBLFFBQVE7Z0JBQ1hKLE9BQU9LLElBQUksQ0FBQztZQUNkO1FBQ0Y7SUFDSixHQUFHLEVBQUU7SUFFTCxxQkFDRTs7MEJBQ0UsOERBQUNqQixrREFBSUE7O2tDQUNILDhEQUFDa0I7a0NBQU07Ozs7OztrQ0FDUCw4REFBQ0M7d0JBQUtDLE1BQUs7d0JBQWNDLFNBQVE7Ozs7OztrQ0FDakMsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBQ3RCLDhEQUFDQzt3QkFFQ0MseUJBQXlCOzRCQUN2Qix3RUFBd0U7NEJBQ3hFLG9FQUFvRTs0QkFDcEVDLFFBQVF6QiwyQ0FBTUEsQ0FBQzBCLFNBQVM7d0JBQzFCO3VCQUxJOzs7OztrQ0FRTiw4REFBQ0g7d0JBRUNDLHlCQUF5Qjs0QkFDdkJDLFFBQVF6QiwyQ0FBTUEsQ0FBQzJCLE1BQU0sQ0FBQztnQ0FDcEIscUVBQXFFO2dDQUNyRSw0Q0FBNEM7Z0NBQzVDQyxTQUFTdkIsTUFBeUIsR0FBZSxJQUFrQjs0QkFDckU7d0JBQ0Y7dUJBUEk7Ozs7O2tDQVNOLDhEQUFDSixvREFBTUE7d0JBQUM0QixJQUFHO3dCQUFjQyxVQUFTO2tDQUMvQixDQUFDLHFEQUFxRCxDQUFDOzs7Ozs7Ozs7Ozs7MEJBUzVELDhEQUFDL0Isa0ZBQW1CQTswQkFDbEIsNEVBQUNTO29CQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7QUFJaEM7QUFFQSxpRUFBZUYsS0FBS0EsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4vcGFnZXMvX2FwcC50c3g/MmZiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJ0B0YW1hZ3VpL2NvcmUvcmVzZXQuY3NzJ1xuaW1wb3J0ICdAdGFtYWd1aS9mb250LWludGVyL2Nzcy80MDAuY3NzJ1xuaW1wb3J0ICdAdGFtYWd1aS9mb250LWludGVyL2Nzcy83MDAuY3NzJ1xuaW1wb3J0ICdyYWYvcG9seWZpbGwnXG5cbmltcG9ydCB0eXBlIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJ1xuaW1wb3J0IHR5cGUgeyBTb2xpdG9BcHBQcm9wcyB9IGZyb20gJ3NvbGl0bydcbmltcG9ydCB7IE5leHRUYW1hZ3VpUHJvdmlkZXIgfSBmcm9tICdhcHAvcHJvdmlkZXIvTmV4dFRhbWFndWlQcm92aWRlcidcbmltcG9ydCB7IGNvbmZpZyB9IGZyb20gJ0BteS91aSdcbmltcG9ydCBTY3JpcHQgZnJvbSAnbmV4dC9zY3JpcHQnXG5pbXBvcnQgeyB1c2VXYWxsZXRTdG9yZSB9IGZyb20gJ2FwcC9zdG9yZXMvd2FsbGV0U3RvcmUnXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ3NvbGl0by9uYXZpZ2F0aW9uJ1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICByZXF1aXJlKCcuLi9wdWJsaWMvdGFtYWd1aS5jc3MnKVxufVxuXG5mdW5jdGlvbiBNeUFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IFNvbGl0b0FwcFByb3BzKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IHdhbGxldFN0b3JlID0gdXNlV2FsbGV0U3RvcmUoKVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHdhbGxldFN0b3JlLmdldFZhdWx0KClcbiAgICAgIC50aGVuKF92YXVsdCA9PiB7XG4gICAgICAgIC8vIOWmguaenOayoeacieWvhueggVxuICAgICAgICBpZiAoIV92YXVsdCkge1xuICAgICAgICAgIHJvdXRlci5wdXNoKCcvd2FsbGV0L3Bhc3N3b3JkJylcbiAgICAgICAgfVxuICAgICAgfSlcbiAgfSwgW10pXG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPEhlYWQ+XG4gICAgICAgIDx0aXRsZT5Db2luYmFzZSB2MjwvdGl0bGU+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJDb2luYmFzZSB2MlwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImljb25cIiBocmVmPVwiL2Zhdmljb24uaWNvXCIgLz5cbiAgICAgICAgPHN0eWxlXG4gICAgICAgICAga2V5PVwidGFtYWd1aS1uZXctY3NzXCJcbiAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgICAgLy8gdGhlIGZpcnN0IHRpbWUgdGhpcyBydW5zIHlvdSdsbCBnZXQgdGhlIGZ1bGwgQ1NTIGluY2x1ZGluZyBhbGwgdGhlbWVzXG4gICAgICAgICAgICAvLyBhZnRlciB0aGF0LCBpdCB3aWxsIG9ubHkgcmV0dXJuIENTUyBnZW5lcmF0ZWQgc2luY2UgdGhlIGxhc3QgY2FsbFxuICAgICAgICAgICAgX19odG1sOiBjb25maWcuZ2V0TmV3Q1NTKCksXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cblxuICAgICAgICA8c3R5bGVcbiAgICAgICAgICBrZXk9XCJ0YW1hZ3VpLWNzc1wiXG4gICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcbiAgICAgICAgICAgIF9faHRtbDogY29uZmlnLmdldENTUyh7XG4gICAgICAgICAgICAgIC8vIGlmIHlvdSBhcmUgdXNpbmcgXCJvdXRwdXRDU1NcIiBvcHRpb24sIHlvdSBzaG91bGQgdXNlIHRoaXMgXCJleGNsdWRlXCJcbiAgICAgICAgICAgICAgLy8gaWYgbm90LCB0aGVuIHlvdSBjYW4gbGVhdmUgdGhlIG9wdGlvbiBvdXRcbiAgICAgICAgICAgICAgZXhjbHVkZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJyA/ICdkZXNpZ24tc3lzdGVtJyA6IG51bGwsXG4gICAgICAgICAgICB9KSxcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuICAgICAgICA8U2NyaXB0IGlkPVwidF91bm1vdW50ZWRcIiBzdHJhdGVneT1cImJlZm9yZUludGVyYWN0aXZlXCI+XG4gICAgICAgICAge2Bkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmFkZCgndF91bm1vdW50ZWQnKWB9XG4gICAgICAgIDwvU2NyaXB0PlxuICAgICAgICB7LyogPHNjcmlwdFxuICAgICAgICAgIGtleT1cInRfdW5tb3VudGVkXCJcbiAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICAgICAgX19odG1sOiBgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsYXNzTGlzdC5hZGQoJ3RfdW5tb3VudGVkJylgLFxuICAgICAgICAgIH19XG4gICAgICAgIC8+ICovfVxuICAgICAgPC9IZWFkPlxuICAgICAgPE5leHRUYW1hZ3VpUHJvdmlkZXI+XG4gICAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgIDwvTmV4dFRhbWFndWlQcm92aWRlcj5cbiAgICA8Lz5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBNeUFwcFxuIl0sIm5hbWVzIjpbIkhlYWQiLCJOZXh0VGFtYWd1aVByb3ZpZGVyIiwiY29uZmlnIiwiU2NyaXB0IiwidXNlV2FsbGV0U3RvcmUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJwcm9jZXNzIiwicmVxdWlyZSIsIk15QXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwicm91dGVyIiwid2FsbGV0U3RvcmUiLCJnZXRWYXVsdCIsInRoZW4iLCJfdmF1bHQiLCJwdXNoIiwidGl0bGUiLCJtZXRhIiwibmFtZSIsImNvbnRlbnQiLCJsaW5rIiwicmVsIiwiaHJlZiIsInN0eWxlIiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiLCJnZXROZXdDU1MiLCJnZXRDU1MiLCJleGNsdWRlIiwiaWQiLCJzdHJhdGVneSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/document */ \"../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_5__]);\n_my_ui__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nclass Document extends (next_document__WEBPACK_IMPORTED_MODULE_3___default()) {\n    static async getInitialProps(ctx) {\n        react_native__WEBPACK_IMPORTED_MODULE_4__.AppRegistry.registerComponent(\"Main\", ()=>next_document__WEBPACK_IMPORTED_MODULE_3__.Main);\n        const page = await ctx.renderPage();\n        // @ts-ignore\n        const { getStyleElement } = react_native__WEBPACK_IMPORTED_MODULE_4__.AppRegistry.getApplication(\"Main\");\n        /**\n     * Note: be sure to keep tamagui styles after react-native-web styles like it is here!\n     * So Tamagui styles can override the react-native-web styles.\n     */ const styles = [\n            getStyleElement(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: _my_ui__WEBPACK_IMPORTED_MODULE_5__.config.getCSS({\n                        exclude:  true ? null : 0\n                    })\n                }\n            }, \"tamagui-css\", false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_document.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3480a98ff4a8efc3\",\n                children: 'html{font-family:\"Inter\"}'\n            }, void 0, false, void 0, this)\n        ];\n        return {\n            ...page,\n            styles: react__WEBPACK_IMPORTED_MODULE_2__.Children.toArray(styles)\n        };\n    }\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_3__.Html, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_3__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            httpEquiv: \"X-UA-Compatible\",\n                            content: \"IE=edge\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_document.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_document.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_document.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_3__.Main, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_document.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_3__.NextScript, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_document.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_document.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/_document.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this);\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./pages/wallet/notice.tsx":
/*!*********************************!*\
  !*** ./pages/wallet/notice.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_notice_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/notice-screen */ \"../../packages/app/features/wallet/notice-screen.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([app_features_wallet_notice_screen__WEBPACK_IMPORTED_MODULE_1__]);\napp_features_wallet_notice_screen__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_notice_screen__WEBPACK_IMPORTED_MODULE_1__.NoticeScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/notice.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvbm90aWNlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUNnRTtBQUVqRCxTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QsMkVBQVlBOzs7OztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4vcGFnZXMvd2FsbGV0L25vdGljZS50c3g/ZGFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IE5vdGljZVNjcmVlbiB9IGZyb20gJ2FwcC9mZWF0dXJlcy93YWxsZXQvbm90aWNlLXNjcmVlbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAgcmV0dXJuIDxOb3RpY2VTY3JlZW4gLz5cbn0iXSwibmFtZXMiOlsiTm90aWNlU2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/notice.tsx\n");

/***/ }),

/***/ "../../packages/app/features/wallet/notice-screen.tsx":
/*!************************************************************!*\
  !*** ../../packages/app/features/wallet/notice-screen.tsx ***!
  \************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoticeScreen: () => (/* binding */ NoticeScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_notice_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/notice.png */ \"../../packages/assets/images/wallet/notice.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_5__, tamagui__WEBPACK_IMPORTED_MODULE_7__]);\n([_my_ui__WEBPACK_IMPORTED_MODULE_5__, tamagui__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction NoticeScreen() {\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const tabList = [\n        \"全部\",\n        \"交换\",\n        \"赚取\",\n        \"社交媒体\",\n        \"管理\",\n        \"监听\"\n    ];\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const dataList = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"交易资产\",\n            isSelected: false\n        },\n        {\n            id: 3,\n            name: \"SushiSwap\",\n            desc: \"交易资产\",\n            isSelected: false\n        }\n    ];\n    const onMnemonicClick = ()=>{\n        router.push(\"/wallet/password\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n        \"data-at\": \"notice-screen.tsx:43\",\n        \"data-in\": \"NoticeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        px: 16,\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                \"data-at\": \"notice-screen.tsx:44\",\n                \"data-in\": \"NoticeScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.NavBar, {\n                        title: \"通知\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_6__.Pressable, {\n                        onPress: ()=>router.push(\"/wallet/noticeSetting\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                            \"data-at\": \"notice-screen.tsx:47\",\n                            \"data-in\": \"NoticeScreen\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                            width: 16,\n                            height: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                \"data-at\": \"notice-screen.tsx:51\",\n                \"data-in\": \"NoticeScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                justifyContent: \"center\",\n                mt: 50,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                    \"data-at\": \"notice-screen.tsx:52\",\n                    \"data-in\": \"NoticeScreen\",\n                    \"data-is\": \"Image\",\n                    source: _assets_images_wallet_notice_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                    width: 173,\n                    height: 142\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                \"data-at\": \"notice-screen.tsx:54\",\n                \"data-in\": \"NoticeScreen\",\n                \"data-is\": \"YStack\",\n                pl: 16,\n                justifyContent: \"center\",\n                mt: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                        \"data-at\": \"notice-screen.tsx:55\",\n                        \"data-in\": \"NoticeScreen\",\n                        \"data-is\": \"Text\",\n                        color: \"$white1\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        textAlign: \"center\",\n                        mb: 10,\n                        children: \"欢迎!\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                        \"data-at\": \"notice-screen.tsx:56\",\n                        \"data-in\": \"NoticeScreen\",\n                        \"data-is\": \"Text\",\n                        color: \"$accent11\",\n                        width: 280,\n                        textAlign: \"center\",\n                        margin: \"auto\",\n                        children: \"探索加密货币世界以开始获取更新。\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n        lineNumber: 32,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/notice-screen.tsx\n");

/***/ }),

/***/ "../../packages/app/i18n/index.ts":
/*!****************************************!*\
  !*** ../../packages/app/i18n/index.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SUPPORTED_LANGUAGES: () => (/* binding */ SUPPORTED_LANGUAGES),\n/* harmony export */   initializeI18n: () => (/* binding */ initializeI18n),\n/* harmony export */   useI18nStore: () => (/* binding */ useI18nStore),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__]);\nzustand__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n// 支持的语言列表\nconst SUPPORTED_LANGUAGES = [\n    {\n        code: \"zh\",\n        name: \"Chinese\",\n        nativeName: \"简体中文\"\n    },\n    {\n        code: \"en\",\n        name: \"English\",\n        nativeName: \"English\"\n    }\n];\nconst STORAGE_KEY = \"APP_LANGUAGE\";\n// 获取嵌套对象的值\nconst getNestedValue = (obj, path)=>{\n    return path.split(\".\").reduce((current, key)=>current?.[key], obj) || path;\n};\nconst useI18nStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        currentLanguage: \"zh\",\n        translations: {},\n        isLoading: false,\n        setLanguage: async (language)=>{\n            set({\n                isLoading: true\n            });\n            try {\n                await get().loadLanguage(language);\n                set({\n                    currentLanguage: language\n                });\n                await app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].setItem(STORAGE_KEY, language);\n            } catch (error) {\n                console.error(\"Failed to set language:\", error);\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        loadLanguage: async (language)=>{\n            try {\n                // 动态导入语言文件\n                const translations = await __webpack_require__(\"../../packages/app/i18n/locales lazy recursive ^\\\\.\\\\/.*$\")(`./${language}`);\n                set({\n                    translations: translations.default\n                });\n            } catch (error) {\n                console.error(`Failed to load language ${language}:`, error);\n                // 如果加载失败，使用默认中文\n                if (language !== \"zh\") {\n                    const defaultTranslations = await __webpack_require__.e(/*! import() */ \"packages_app_i18n_locales_zh_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./locales/zh */ \"../../packages/app/i18n/locales/zh.ts\"));\n                    set({\n                        translations: defaultTranslations.default\n                    });\n                }\n            }\n        },\n        t: (key)=>{\n            const translations = get().translations;\n            return getNestedValue(translations, key);\n        },\n        getCurrentLanguage: ()=>get().currentLanguage,\n        getSupportedLanguages: ()=>SUPPORTED_LANGUAGES\n    }));\n// 初始化多语言系统\nconst initializeI18n = async ()=>{\n    console.log(\"Initializing i18n system...\");\n    const store = useI18nStore.getState();\n    try {\n        // 从存储中获取保存的语言设置\n        const savedLanguage = await app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getItem(STORAGE_KEY);\n        const language = savedLanguage || \"zh\";\n        console.log(\"Loading language:\", language);\n        await store.loadLanguage(language);\n        useI18nStore.setState({\n            currentLanguage: language\n        });\n        console.log(\"i18n initialized successfully with language:\", language);\n    } catch (error) {\n        console.error(\"Failed to initialize i18n:\", error);\n        // 使用默认语言\n        await store.loadLanguage(\"zh\");\n    }\n};\n// Hook for easy access to translation function\nconst useTranslation = ()=>{\n    const { t, currentLanguage, setLanguage, getSupportedLanguages } = useI18nStore();\n    return {\n        t,\n        currentLanguage,\n        setLanguage,\n        getSupportedLanguages\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/i18n/index.ts\n");

/***/ }),

/***/ "../../packages/app/provider/NextTamaguiProvider.tsx":
/*!***********************************************************!*\
  !*** ../../packages/app/provider/NextTamaguiProvider.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NextTamaguiProvider: () => (/* binding */ NextTamaguiProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/core/reset.css */ \"../../node_modules/@tamagui/core/reset.css\");\n/* harmony import */ var _tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tamagui_core_reset_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/font-inter/css/400.css */ \"../../node_modules/@tamagui/font-inter/css/400.css\");\n/* harmony import */ var _tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tamagui_font_inter_css_400_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/font-inter/css/700.css */ \"../../node_modules/@tamagui/font-inter/css/700.css\");\n/* harmony import */ var _tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_tamagui_font_inter_css_700_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tamagui/polyfill-dev */ \"../../node_modules/@tamagui/polyfill-dev/index.js\");\n/* harmony import */ var _tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tamagui_polyfill_dev__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tamagui/next-theme */ \"../../node_modules/@tamagui/next-theme/dist/cjs/index.js\");\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var app_provider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! app/provider */ \"../../packages/app/provider/index.tsx\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_8__, app_provider__WEBPACK_IMPORTED_MODULE_9__]);\n([_my_ui__WEBPACK_IMPORTED_MODULE_8__, app_provider__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ NextTamaguiProvider auto */ \n\n\n\n\n\n\n\n\n\nconst NextTamaguiProvider = ({ children })=>{\n    const [theme, setTheme] = (0,_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__.useRootTheme)();\n    (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useServerInsertedHTML)(()=>{\n        // @ts-ignore\n        const rnwStyle = react_native__WEBPACK_IMPORTED_MODULE_7__.StyleSheet.getSheet();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: \"/tamagui.css\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        __html: rnwStyle.textContent\n                    },\n                    id: rnwStyle.id\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        // the first time this runs you'll get the full CSS including all themes\n                        // after that, it will only return CSS generated since the last call\n                        __html: _my_ui__WEBPACK_IMPORTED_MODULE_8__.config.getNewCSS()\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        __html: _my_ui__WEBPACK_IMPORTED_MODULE_8__.config.getCSS({\n                            exclude:  false ? 0 : null\n                        })\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        // avoid flash of animated things on enter:\n                        __html: `document.documentElement.classList.add('t_unmounted')`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/NextTamaguiProvider.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_6__.NextThemeProvider, {\n        skipNextHead: true,\n        defaultTheme: \"dark\",\n        onChangeTheme: (next)=>{\n            setTheme(next);\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_provider__WEBPACK_IMPORTED_MODULE_9__.Provider, {\n            disableRootThemeClass: true,\n            defaultTheme: theme || \"dark\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/NextTamaguiProvider.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/NextTamaguiProvider.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/provider/NextTamaguiProvider.tsx\n");

/***/ }),

/***/ "../../packages/app/provider/ToastViewport.web.tsx":
/*!*********************************************************!*\
  !*** ../../packages/app/provider/ToastViewport.web.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_my_ui__WEBPACK_IMPORTED_MODULE_1__]);\n_my_ui__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst ToastViewport = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {\n        left: 0,\n        right: 0,\n        top: 10\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/ToastViewport.web.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL3Byb3ZpZGVyL1RvYXN0Vmlld3BvcnQud2ViLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF5RDtBQUVsRCxNQUFNQSxnQkFBZ0I7SUFDM0IscUJBQ0UsOERBQUNDLGlEQUFlQTtRQUNkQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsS0FBSzs7Ozs7O0FBR1gsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3BhY2thZ2VzL2FwcC9wcm92aWRlci9Ub2FzdFZpZXdwb3J0LndlYi50c3g/ZWM1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUb2FzdFZpZXdwb3J0IGFzIFRvYXN0Vmlld3BvcnRPZyB9IGZyb20gJ0BteS91aSdcblxuZXhwb3J0IGNvbnN0IFRvYXN0Vmlld3BvcnQgPSAoKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPFRvYXN0Vmlld3BvcnRPZ1xuICAgICAgbGVmdD17MH1cbiAgICAgIHJpZ2h0PXswfVxuICAgICAgdG9wPXsxMH1cbiAgICAvPlxuICApXG59XG4iXSwibmFtZXMiOlsiVG9hc3RWaWV3cG9ydCIsIlRvYXN0Vmlld3BvcnRPZyIsImxlZnQiLCJyaWdodCIsInRvcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/app/provider/ToastViewport.web.tsx\n");

/***/ }),

/***/ "../../packages/app/provider/index.tsx":
/*!*********************************************!*\
  !*** ../../packages/app/provider/index.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var _ToastViewport__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ToastViewport */ \"../../packages/app/provider/ToastViewport.web.tsx\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([app_i18n__WEBPACK_IMPORTED_MODULE_3__, _my_ui__WEBPACK_IMPORTED_MODULE_4__, _ToastViewport__WEBPACK_IMPORTED_MODULE_5__]);\n([app_i18n__WEBPACK_IMPORTED_MODULE_3__, _my_ui__WEBPACK_IMPORTED_MODULE_4__, _ToastViewport__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction Provider({ children, defaultTheme = \"dark\", ...rest }) {\n    const colorScheme = (0,react_native__WEBPACK_IMPORTED_MODULE_2__.useColorScheme)();\n    const theme = defaultTheme || (colorScheme === \"dark\" ? \"dark\" : \"light\");\n    // 初始化多语言系统\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,app_i18n__WEBPACK_IMPORTED_MODULE_3__.initializeI18n)();\n    // 暂时注释掉网络健康检查，避免频繁网络请求\n    // startHealthCheck(300000)\n    // return () => {\n    //   // 组件卸载时停止健康检查\n    //   import('app/utils/networkManager').then(({ stopHealthCheck }) => {\n    //     stopHealthCheck()\n    //   })\n    // }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.TamaguiProvider, {\n        config: _my_ui__WEBPACK_IMPORTED_MODULE_4__.config,\n        defaultTheme: theme,\n        ...rest,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n            swipeDirection: \"horizontal\",\n            duration: 6000,\n            native: _my_ui__WEBPACK_IMPORTED_MODULE_4__.isWeb ? [] : [\n                \"mobile\"\n            ],\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.CustomToast, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/index.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToastViewport__WEBPACK_IMPORTED_MODULE_5__.ToastViewport, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/index.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/index.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/provider/index.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/provider/index.tsx\n");

/***/ }),

/***/ "../../packages/app/stores/utils.ts":
/*!******************************************!*\
  !*** ../../packages/app/stores/utils.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearBalanceCache: () => (/* binding */ clearBalanceCache),\n/* harmony export */   fetchAllBalances: () => (/* binding */ fetchAllBalances),\n/* harmony export */   getCacheStatus: () => (/* binding */ getCacheStatus)\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @solana/web3.js */ \"@solana/web3.js\");\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_solana_web3_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! app/utils/networkManager */ \"../../packages/app/utils/networkManager.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([ethers__WEBPACK_IMPORTED_MODULE_0__]);\nethers__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// 请求缓存和防抖\nconst balanceCache = new Map();\nconst CACHE_DURATION = 30000 // 30秒缓存\n;\nconst requestQueue = new Map();\n// 带超时的请求函数\nconst fetchWithTimeout = async (url, timeout = 5000)=>{\n    const controller = new AbortController();\n    const timeoutId = setTimeout(()=>controller.abort(), timeout);\n    try {\n        const response = await fetch(url, {\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        return response;\n    } catch (error) {\n        clearTimeout(timeoutId);\n        throw error;\n    }\n};\n// EVM 公链通用 - 添加缓存和错误处理\nconst getEvmBalance = async (address, chain)=>{\n    const rpc = app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.networkManager.getAvailableRpc(chain);\n    const cacheKey = `${chain}-${address}`;\n    // 检查缓存\n    const cached = balanceCache.get(cacheKey);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n        return cached.balance;\n    }\n    // 检查是否已有相同请求在进行\n    if (requestQueue.has(cacheKey)) {\n        return requestQueue.get(cacheKey);\n    }\n    const request = (0,app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.requestWithRetry)(async ()=>{\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_0__.ethers.JsonRpcProvider(rpc, undefined, {\n            staticNetwork: true,\n            batchMaxCount: 1\n        });\n        // 设置超时\n        const timeoutPromise = new Promise((_, reject)=>{\n            setTimeout(()=>reject(new Error(\"Request timeout\")), 5000);\n        });\n        const balancePromise = provider.getBalance(address);\n        const bal = await Promise.race([\n            balancePromise,\n            timeoutPromise\n        ]);\n        const balance = ethers__WEBPACK_IMPORTED_MODULE_0__.ethers.formatEther(bal);\n        // 缓存结果\n        balanceCache.set(cacheKey, {\n            balance,\n            timestamp: Date.now()\n        });\n        return balance;\n    }, rpc).catch((error)=>{\n        console.warn(`Failed to fetch ${chain.toUpperCase()} balance for ${address}:`, error.message);\n        return \"0\";\n    }).finally(()=>{\n        requestQueue.delete(cacheKey);\n    });\n    requestQueue.set(cacheKey, request);\n    return request;\n};\nconst getSolBalance = async (address)=>{\n    const rpc = app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.networkManager.getAvailableRpc(\"solana\");\n    const cacheKey = `solana-${address}`;\n    // 检查缓存\n    const cached = balanceCache.get(cacheKey);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n        return cached.balance;\n    }\n    // 检查是否已有相同请求在进行\n    if (requestQueue.has(cacheKey)) {\n        return requestQueue.get(cacheKey);\n    }\n    const request = (0,app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.requestWithRetry)(async ()=>{\n        const connection = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_1__.Connection(rpc, {\n            commitment: \"confirmed\",\n            confirmTransactionInitialTimeout: 5000\n        });\n        const timeoutPromise = new Promise((_, reject)=>{\n            setTimeout(()=>reject(new Error(\"Request timeout\")), 5000);\n        });\n        const balancePromise = connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_1__.PublicKey(address));\n        const lamports = await Promise.race([\n            balancePromise,\n            timeoutPromise\n        ]);\n        const balance = (lamports / 1e9).toFixed(6);\n        // 缓存结果\n        balanceCache.set(cacheKey, {\n            balance,\n            timestamp: Date.now()\n        });\n        return balance;\n    }, rpc).catch((error)=>{\n        console.warn(`Failed to fetch Solana balance for ${address}:`, error.message);\n        return \"0\";\n    }).finally(()=>{\n        requestQueue.delete(cacheKey);\n    });\n    requestQueue.set(cacheKey, request);\n    return request;\n};\n// 可选：BTC 查询（注意你当前 BTC 地址是 EVM 格式）\nconst getBtcBalance = async (address)=>{\n    const cacheKey = `btc-${address}`;\n    // 检查缓存\n    const cached = balanceCache.get(cacheKey);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n        return cached.balance;\n    }\n    // 检查是否已有相同请求在进行\n    if (requestQueue.has(cacheKey)) {\n        return requestQueue.get(cacheKey);\n    }\n    const request = (async ()=>{\n        try {\n            const res = await fetchWithTimeout(`https://blockstream.info/api/address/${address}`);\n            if (!res.ok) throw new Error(`HTTP ${res.status}`);\n            const data = await res.json();\n            const btc = (data.chain_stats.funded_txo_sum - data.chain_stats.spent_txo_sum) / 1e8;\n            const balance = btc.toFixed(8);\n            // 缓存结果\n            balanceCache.set(cacheKey, {\n                balance,\n                timestamp: Date.now()\n            });\n            return balance;\n        } catch (error) {\n            console.warn(`Failed to fetch BTC balance for ${address}:`, error.message);\n            return \"0\";\n        } finally{\n            requestQueue.delete(cacheKey);\n        }\n    })();\n    requestQueue.set(cacheKey, request);\n    return request;\n};\nconst getBtcTestnetBalance = async (address)=>{\n    const cacheKey = `btc-testnet-${address}`;\n    // 检查缓存\n    const cached = balanceCache.get(cacheKey);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n        return cached.balance;\n    }\n    // 检查是否已有相同请求在进行\n    if (requestQueue.has(cacheKey)) {\n        return requestQueue.get(cacheKey);\n    }\n    const request = (async ()=>{\n        try {\n            const res = await fetchWithTimeout(`https://blockstream.info/testnet/api/address/${address}`);\n            if (!res.ok) throw new Error(`HTTP ${res.status}`);\n            const data = await res.json();\n            const btc = (data.chain_stats.funded_txo_sum - data.chain_stats.spent_txo_sum) / 1e8;\n            const balance = btc.toFixed(8);\n            // 缓存结果\n            balanceCache.set(cacheKey, {\n                balance,\n                timestamp: Date.now()\n            });\n            return balance;\n        } catch (error) {\n            console.warn(`Failed to fetch BTC testnet balance for ${address}:`, error.message);\n            return \"0\";\n        } finally{\n            requestQueue.delete(cacheKey);\n        }\n    })();\n    requestQueue.set(cacheKey, request);\n    return request;\n};\nconst getTestnetBalance = async (address)=>{\n    const cacheKey = `btc-mempool-${address}`;\n    // 检查缓存\n    const cached = balanceCache.get(cacheKey);\n    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n        return cached.balance;\n    }\n    // 检查是否已有相同请求在进行\n    if (requestQueue.has(cacheKey)) {\n        return requestQueue.get(cacheKey);\n    }\n    const request = (async ()=>{\n        try {\n            const res = await fetchWithTimeout(`https://mempool.space/testnet/api/address/${address}`);\n            if (!res.ok) throw new Error(`HTTP ${res.status}`);\n            const data = await res.json();\n            const balance = (data.chain_stats.funded_txo_sum - data.chain_stats.spent_txo_sum + data.mempool_stats.funded_txo_sum - data.mempool_stats.spent_txo_sum) / 1e8;\n            const balanceStr = balance.toFixed(8);\n            // 缓存结果\n            balanceCache.set(cacheKey, {\n                balance: balanceStr,\n                timestamp: Date.now()\n            });\n            return balanceStr;\n        } catch (error) {\n            console.warn(`Failed to fetch BTC mempool balance for ${address}:`, error.message);\n            return \"0\";\n        } finally{\n            requestQueue.delete(cacheKey);\n        }\n    })();\n    requestQueue.set(cacheKey, request);\n    return request;\n};\n// 核心函数：从 walletList 中查询余额并更新 Zustand 状态\nconst fetchAllBalances = async (walletList)=>{\n    const balanceMap = {};\n    if (!walletList || walletList.length === 0) {\n        return balanceMap;\n    }\n    console.log(\"开始获取余额...\");\n    for (const wallet of walletList){\n        for (const account of wallet.accounts){\n            const accountId = account.accountId;\n            // 使用 Promise.allSettled 而不是 Promise.all，避免一个失败导致全部失败\n            const results = await Promise.allSettled([\n                account.eth?.address ? getEvmBalance(account.eth.address, \"eth\") : Promise.resolve(\"0\"),\n                account.bsc?.address ? getEvmBalance(account.bsc.address, \"bsc\") : Promise.resolve(\"0\"),\n                account.solana?.address ? getSolBalance(account.solana.address) : Promise.resolve(\"0\"),\n                account.btc?.address ? getTestnetBalance(account.btc.address) : Promise.resolve(\"0\")\n            ]);\n            const [ethResult, bscResult, solResult, btcResult] = results;\n            balanceMap[accountId] = {\n                eth: ethResult.status === \"fulfilled\" ? ethResult.value : \"0\",\n                bsc: bscResult.status === \"fulfilled\" ? bscResult.value : \"0\",\n                solana: solResult.status === \"fulfilled\" ? solResult.value : \"0\",\n                btc: btcResult.status === \"fulfilled\" ? btcResult.value : \"0\"\n            };\n            // 记录失败的请求\n            results.forEach((result, index)=>{\n                if (result.status === \"rejected\") {\n                    const chains = [\n                        \"ETH\",\n                        \"BSC\",\n                        \"Solana\",\n                        \"BTC\"\n                    ];\n                    console.warn(`${chains[index]} balance fetch failed for account ${accountId}:`, result.reason);\n                }\n            });\n        }\n    }\n    console.log(\"余额获取完成:\", balanceMap);\n    return balanceMap;\n};\n// 清理缓存的工具函数\nconst clearBalanceCache = ()=>{\n    balanceCache.clear();\n    console.log(\"余额缓存已清理\");\n};\n// 获取缓存状态的工具函数\nconst getCacheStatus = ()=>{\n    return {\n        cacheSize: balanceCache.size,\n        activeRequests: requestQueue.size,\n        cacheEntries: Array.from(balanceCache.entries()).map(([key, value])=>({\n                key,\n                age: Date.now() - value.timestamp,\n                balance: value.balance\n            }))\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL3N0b3Jlcy91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQStCO0FBQ3dCO0FBRW9CO0FBRTNFLFVBQVU7QUFDVixNQUFNSyxlQUFlLElBQUlDO0FBQ3pCLE1BQU1DLGlCQUFpQixNQUFNLFFBQVE7O0FBQ3JDLE1BQU1DLGVBQWUsSUFBSUY7QUFFekIsV0FBVztBQUNYLE1BQU1HLG1CQUFtQixPQUFPQyxLQUFhQyxVQUFVLElBQUk7SUFDekQsTUFBTUMsYUFBYSxJQUFJQztJQUN2QixNQUFNQyxZQUFZQyxXQUFXLElBQU1ILFdBQVdJLEtBQUssSUFBSUw7SUFFdkQsSUFBSTtRQUNGLE1BQU1NLFdBQVcsTUFBTUMsTUFBTVIsS0FBSztZQUFFUyxRQUFRUCxXQUFXTyxNQUFNO1FBQUM7UUFDOURDLGFBQWFOO1FBQ2IsT0FBT0c7SUFDVCxFQUFFLE9BQU9JLE9BQU87UUFDZEQsYUFBYU47UUFDYixNQUFNTztJQUNSO0FBQ0Y7QUFFQSx1QkFBdUI7QUFDdkIsTUFBTUMsZ0JBQWdCLE9BQU9DLFNBQWlCQztJQUM1QyxNQUFNQyxNQUFNdEIsb0VBQWNBLENBQUN1QixlQUFlLENBQUNGO0lBQzNDLE1BQU1HLFdBQVcsQ0FBQyxFQUFFSCxNQUFNLENBQUMsRUFBRUQsUUFBUSxDQUFDO0lBRXRDLE9BQU87SUFDUCxNQUFNSyxTQUFTdkIsYUFBYXdCLEdBQUcsQ0FBQ0Y7SUFDaEMsSUFBSUMsVUFBVUUsS0FBS0MsR0FBRyxLQUFLSCxPQUFPSSxTQUFTLEdBQUd6QixnQkFBZ0I7UUFDNUQsT0FBT3FCLE9BQU9LLE9BQU87SUFDdkI7SUFFQSxnQkFBZ0I7SUFDaEIsSUFBSXpCLGFBQWEwQixHQUFHLENBQUNQLFdBQVc7UUFDOUIsT0FBT25CLGFBQWFxQixHQUFHLENBQUNGO0lBQzFCO0lBRUEsTUFBTVEsVUFBVS9CLDBFQUFnQkEsQ0FBQztRQUMvQixNQUFNZ0MsV0FBVyxJQUFJcEMsMENBQU1BLENBQUNxQyxlQUFlLENBQUNaLEtBQUthLFdBQVc7WUFDMURDLGVBQWU7WUFDZkMsZUFBZTtRQUNqQjtRQUVBLE9BQU87UUFDUCxNQUFNQyxpQkFBaUIsSUFBSUMsUUFBZSxDQUFDQyxHQUFHQztZQUM1QzdCLFdBQVcsSUFBTTZCLE9BQU8sSUFBSUMsTUFBTSxxQkFBcUI7UUFDekQ7UUFFQSxNQUFNQyxpQkFBaUJWLFNBQVNXLFVBQVUsQ0FBQ3hCO1FBQzNDLE1BQU15QixNQUFNLE1BQU1OLFFBQVFPLElBQUksQ0FBQztZQUFDSDtZQUFnQkw7U0FBZTtRQUMvRCxNQUFNUixVQUFVakMsMENBQU1BLENBQUNrRCxXQUFXLENBQUNGO1FBRW5DLE9BQU87UUFDUDNDLGFBQWE4QyxHQUFHLENBQUN4QixVQUFVO1lBQUVNO1lBQVNELFdBQVdGLEtBQUtDLEdBQUc7UUFBRztRQUM1RCxPQUFPRTtJQUNULEdBQUdSLEtBQ0EyQixLQUFLLENBQUMsQ0FBQy9CO1FBQ05nQyxRQUFRQyxJQUFJLENBQUMsQ0FBQyxnQkFBZ0IsRUFBRTlCLE1BQU0rQixXQUFXLEdBQUcsYUFBYSxFQUFFaEMsUUFBUSxDQUFDLENBQUMsRUFBRUYsTUFBTW1DLE9BQU87UUFDNUYsT0FBTztJQUNULEdBQ0NDLE9BQU8sQ0FBQztRQUNQakQsYUFBYWtELE1BQU0sQ0FBQy9CO0lBQ3RCO0lBRUZuQixhQUFhMkMsR0FBRyxDQUFDeEIsVUFBVVE7SUFDM0IsT0FBT0E7QUFDVDtBQUVBLE1BQU13QixnQkFBZ0IsT0FBT3BDO0lBQzNCLE1BQU1FLE1BQU10QixvRUFBY0EsQ0FBQ3VCLGVBQWUsQ0FBQztJQUMzQyxNQUFNQyxXQUFXLENBQUMsT0FBTyxFQUFFSixRQUFRLENBQUM7SUFFcEMsT0FBTztJQUNQLE1BQU1LLFNBQVN2QixhQUFhd0IsR0FBRyxDQUFDRjtJQUNoQyxJQUFJQyxVQUFVRSxLQUFLQyxHQUFHLEtBQUtILE9BQU9JLFNBQVMsR0FBR3pCLGdCQUFnQjtRQUM1RCxPQUFPcUIsT0FBT0ssT0FBTztJQUN2QjtJQUVBLGdCQUFnQjtJQUNoQixJQUFJekIsYUFBYTBCLEdBQUcsQ0FBQ1AsV0FBVztRQUM5QixPQUFPbkIsYUFBYXFCLEdBQUcsQ0FBQ0Y7SUFDMUI7SUFFQSxNQUFNUSxVQUFVL0IsMEVBQWdCQSxDQUFDO1FBQy9CLE1BQU13RCxhQUFhLElBQUkzRCx1REFBVUEsQ0FBQ3dCLEtBQUs7WUFDckNvQyxZQUFZO1lBQ1pDLGtDQUFrQztRQUNwQztRQUVBLE1BQU1yQixpQkFBaUIsSUFBSUMsUUFBZSxDQUFDQyxHQUFHQztZQUM1QzdCLFdBQVcsSUFBTTZCLE9BQU8sSUFBSUMsTUFBTSxxQkFBcUI7UUFDekQ7UUFFQSxNQUFNQyxpQkFBaUJjLFdBQVdiLFVBQVUsQ0FBQyxJQUFJN0Msc0RBQVNBLENBQUNxQjtRQUMzRCxNQUFNd0MsV0FBVyxNQUFNckIsUUFBUU8sSUFBSSxDQUFDO1lBQUNIO1lBQWdCTDtTQUFlO1FBQ3BFLE1BQU1SLFVBQVUsQ0FBQzhCLFdBQVcsR0FBRSxFQUFHQyxPQUFPLENBQUM7UUFFekMsT0FBTztRQUNQM0QsYUFBYThDLEdBQUcsQ0FBQ3hCLFVBQVU7WUFBRU07WUFBU0QsV0FBV0YsS0FBS0MsR0FBRztRQUFHO1FBQzVELE9BQU9FO0lBQ1QsR0FBR1IsS0FDQTJCLEtBQUssQ0FBQyxDQUFDL0I7UUFDTmdDLFFBQVFDLElBQUksQ0FBQyxDQUFDLG1DQUFtQyxFQUFFL0IsUUFBUSxDQUFDLENBQUMsRUFBRUYsTUFBTW1DLE9BQU87UUFDNUUsT0FBTztJQUNULEdBQ0NDLE9BQU8sQ0FBQztRQUNQakQsYUFBYWtELE1BQU0sQ0FBQy9CO0lBQ3RCO0lBRUZuQixhQUFhMkMsR0FBRyxDQUFDeEIsVUFBVVE7SUFDM0IsT0FBT0E7QUFDVDtBQUVBLGtDQUFrQztBQUNsQyxNQUFNOEIsZ0JBQWdCLE9BQU8xQztJQUMzQixNQUFNSSxXQUFXLENBQUMsSUFBSSxFQUFFSixRQUFRLENBQUM7SUFFakMsT0FBTztJQUNQLE1BQU1LLFNBQVN2QixhQUFhd0IsR0FBRyxDQUFDRjtJQUNoQyxJQUFJQyxVQUFVRSxLQUFLQyxHQUFHLEtBQUtILE9BQU9JLFNBQVMsR0FBR3pCLGdCQUFnQjtRQUM1RCxPQUFPcUIsT0FBT0ssT0FBTztJQUN2QjtJQUVBLGdCQUFnQjtJQUNoQixJQUFJekIsYUFBYTBCLEdBQUcsQ0FBQ1AsV0FBVztRQUM5QixPQUFPbkIsYUFBYXFCLEdBQUcsQ0FBQ0Y7SUFDMUI7SUFFQSxNQUFNUSxVQUFVLENBQUM7UUFDZixJQUFJO1lBQ0YsTUFBTStCLE1BQU0sTUFBTXpELGlCQUFpQixDQUFDLHFDQUFxQyxFQUFFYyxRQUFRLENBQUM7WUFDcEYsSUFBSSxDQUFDMkMsSUFBSUMsRUFBRSxFQUFFLE1BQU0sSUFBSXRCLE1BQU0sQ0FBQyxLQUFLLEVBQUVxQixJQUFJRSxNQUFNLENBQUMsQ0FBQztZQUVqRCxNQUFNQyxPQUFPLE1BQU1ILElBQUlJLElBQUk7WUFDM0IsTUFBTUMsTUFBTSxDQUFDRixLQUFLRyxXQUFXLENBQUNDLGNBQWMsR0FBR0osS0FBS0csV0FBVyxDQUFDRSxhQUFhLElBQUk7WUFDakYsTUFBTXpDLFVBQVVzQyxJQUFJUCxPQUFPLENBQUM7WUFFNUIsT0FBTztZQUNQM0QsYUFBYThDLEdBQUcsQ0FBQ3hCLFVBQVU7Z0JBQUVNO2dCQUFTRCxXQUFXRixLQUFLQyxHQUFHO1lBQUc7WUFDNUQsT0FBT0U7UUFDVCxFQUFFLE9BQU9aLE9BQU87WUFDZGdDLFFBQVFDLElBQUksQ0FBQyxDQUFDLGdDQUFnQyxFQUFFL0IsUUFBUSxDQUFDLENBQUMsRUFBRUYsTUFBTW1DLE9BQU87WUFDekUsT0FBTztRQUNULFNBQVU7WUFDUmhELGFBQWFrRCxNQUFNLENBQUMvQjtRQUN0QjtJQUNGO0lBRUFuQixhQUFhMkMsR0FBRyxDQUFDeEIsVUFBVVE7SUFDM0IsT0FBT0E7QUFDVDtBQUVBLE1BQU13Qyx1QkFBdUIsT0FBT3BEO0lBQ2xDLE1BQU1JLFdBQVcsQ0FBQyxZQUFZLEVBQUVKLFFBQVEsQ0FBQztJQUV6QyxPQUFPO0lBQ1AsTUFBTUssU0FBU3ZCLGFBQWF3QixHQUFHLENBQUNGO0lBQ2hDLElBQUlDLFVBQVVFLEtBQUtDLEdBQUcsS0FBS0gsT0FBT0ksU0FBUyxHQUFHekIsZ0JBQWdCO1FBQzVELE9BQU9xQixPQUFPSyxPQUFPO0lBQ3ZCO0lBRUEsZ0JBQWdCO0lBQ2hCLElBQUl6QixhQUFhMEIsR0FBRyxDQUFDUCxXQUFXO1FBQzlCLE9BQU9uQixhQUFhcUIsR0FBRyxDQUFDRjtJQUMxQjtJQUVBLE1BQU1RLFVBQVUsQ0FBQztRQUNmLElBQUk7WUFDRixNQUFNK0IsTUFBTSxNQUFNekQsaUJBQWlCLENBQUMsNkNBQTZDLEVBQUVjLFFBQVEsQ0FBQztZQUM1RixJQUFJLENBQUMyQyxJQUFJQyxFQUFFLEVBQUUsTUFBTSxJQUFJdEIsTUFBTSxDQUFDLEtBQUssRUFBRXFCLElBQUlFLE1BQU0sQ0FBQyxDQUFDO1lBRWpELE1BQU1DLE9BQU8sTUFBTUgsSUFBSUksSUFBSTtZQUMzQixNQUFNQyxNQUFNLENBQUNGLEtBQUtHLFdBQVcsQ0FBQ0MsY0FBYyxHQUFHSixLQUFLRyxXQUFXLENBQUNFLGFBQWEsSUFBSTtZQUNqRixNQUFNekMsVUFBVXNDLElBQUlQLE9BQU8sQ0FBQztZQUU1QixPQUFPO1lBQ1AzRCxhQUFhOEMsR0FBRyxDQUFDeEIsVUFBVTtnQkFBRU07Z0JBQVNELFdBQVdGLEtBQUtDLEdBQUc7WUFBRztZQUM1RCxPQUFPRTtRQUNULEVBQUUsT0FBT1osT0FBTztZQUNkZ0MsUUFBUUMsSUFBSSxDQUFDLENBQUMsd0NBQXdDLEVBQUUvQixRQUFRLENBQUMsQ0FBQyxFQUFFRixNQUFNbUMsT0FBTztZQUNqRixPQUFPO1FBQ1QsU0FBVTtZQUNSaEQsYUFBYWtELE1BQU0sQ0FBQy9CO1FBQ3RCO0lBQ0Y7SUFFQW5CLGFBQWEyQyxHQUFHLENBQUN4QixVQUFVUTtJQUMzQixPQUFPQTtBQUNUO0FBRUEsTUFBTXlDLG9CQUFvQixPQUFPckQ7SUFDL0IsTUFBTUksV0FBVyxDQUFDLFlBQVksRUFBRUosUUFBUSxDQUFDO0lBRXpDLE9BQU87SUFDUCxNQUFNSyxTQUFTdkIsYUFBYXdCLEdBQUcsQ0FBQ0Y7SUFDaEMsSUFBSUMsVUFBVUUsS0FBS0MsR0FBRyxLQUFLSCxPQUFPSSxTQUFTLEdBQUd6QixnQkFBZ0I7UUFDNUQsT0FBT3FCLE9BQU9LLE9BQU87SUFDdkI7SUFFQSxnQkFBZ0I7SUFDaEIsSUFBSXpCLGFBQWEwQixHQUFHLENBQUNQLFdBQVc7UUFDOUIsT0FBT25CLGFBQWFxQixHQUFHLENBQUNGO0lBQzFCO0lBRUEsTUFBTVEsVUFBVSxDQUFDO1FBQ2YsSUFBSTtZQUNGLE1BQU0rQixNQUFNLE1BQU16RCxpQkFBaUIsQ0FBQywwQ0FBMEMsRUFBRWMsUUFBUSxDQUFDO1lBQ3pGLElBQUksQ0FBQzJDLElBQUlDLEVBQUUsRUFBRSxNQUFNLElBQUl0QixNQUFNLENBQUMsS0FBSyxFQUFFcUIsSUFBSUUsTUFBTSxDQUFDLENBQUM7WUFFakQsTUFBTUMsT0FBTyxNQUFNSCxJQUFJSSxJQUFJO1lBQzNCLE1BQU1yQyxVQUNKLENBQUNvQyxLQUFLRyxXQUFXLENBQUNDLGNBQWMsR0FDOUJKLEtBQUtHLFdBQVcsQ0FBQ0UsYUFBYSxHQUM5QkwsS0FBS1EsYUFBYSxDQUFDSixjQUFjLEdBQ2pDSixLQUFLUSxhQUFhLENBQUNILGFBQWEsSUFDbEM7WUFFRixNQUFNSSxhQUFhN0MsUUFBUStCLE9BQU8sQ0FBQztZQUVuQyxPQUFPO1lBQ1AzRCxhQUFhOEMsR0FBRyxDQUFDeEIsVUFBVTtnQkFBRU0sU0FBUzZDO2dCQUFZOUMsV0FBV0YsS0FBS0MsR0FBRztZQUFHO1lBQ3hFLE9BQU8rQztRQUNULEVBQUUsT0FBT3pELE9BQU87WUFDZGdDLFFBQVFDLElBQUksQ0FBQyxDQUFDLHdDQUF3QyxFQUFFL0IsUUFBUSxDQUFDLENBQUMsRUFBRUYsTUFBTW1DLE9BQU87WUFDakYsT0FBTztRQUNULFNBQVU7WUFDUmhELGFBQWFrRCxNQUFNLENBQUMvQjtRQUN0QjtJQUNGO0lBRUFuQixhQUFhMkMsR0FBRyxDQUFDeEIsVUFBVVE7SUFDM0IsT0FBT0E7QUFDVDtBQUVBLHdDQUF3QztBQUNqQyxNQUFNNEMsbUJBQW1CLE9BQU9DO0lBQ3JDLE1BQU1DLGFBQWEsQ0FBQztJQUVwQixJQUFJLENBQUNELGNBQWNBLFdBQVdFLE1BQU0sS0FBSyxHQUFHO1FBQzFDLE9BQU9EO0lBQ1Q7SUFFQTVCLFFBQVE4QixHQUFHLENBQUM7SUFFWixLQUFLLE1BQU1DLFVBQVVKLFdBQVk7UUFDL0IsS0FBSyxNQUFNSyxXQUFXRCxPQUFPRSxRQUFRLENBQUU7WUFDckMsTUFBTUMsWUFBWUYsUUFBUUUsU0FBUztZQUVuQyxxREFBcUQ7WUFDckQsTUFBTUMsVUFBVSxNQUFNOUMsUUFBUStDLFVBQVUsQ0FBQztnQkFDdkNKLFFBQVFLLEdBQUcsRUFBRW5FLFVBQVVELGNBQWMrRCxRQUFRSyxHQUFHLENBQUNuRSxPQUFPLEVBQUUsU0FBU21CLFFBQVFpRCxPQUFPLENBQUM7Z0JBQ25GTixRQUFRTyxHQUFHLEVBQUVyRSxVQUFVRCxjQUFjK0QsUUFBUU8sR0FBRyxDQUFDckUsT0FBTyxFQUFFLFNBQVNtQixRQUFRaUQsT0FBTyxDQUFDO2dCQUNuRk4sUUFBUVEsTUFBTSxFQUFFdEUsVUFBVW9DLGNBQWMwQixRQUFRUSxNQUFNLENBQUN0RSxPQUFPLElBQUltQixRQUFRaUQsT0FBTyxDQUFDO2dCQUNsRk4sUUFBUWQsR0FBRyxFQUFFaEQsVUFBVXFELGtCQUFrQlMsUUFBUWQsR0FBRyxDQUFDaEQsT0FBTyxJQUFJbUIsUUFBUWlELE9BQU8sQ0FBQzthQUNqRjtZQUVELE1BQU0sQ0FBQ0csV0FBV0MsV0FBV0MsV0FBV0MsVUFBVSxHQUFHVDtZQUVyRFAsVUFBVSxDQUFDTSxVQUFVLEdBQUc7Z0JBQ3RCRyxLQUFLSSxVQUFVMUIsTUFBTSxLQUFLLGNBQWMwQixVQUFVSSxLQUFLLEdBQUc7Z0JBQzFETixLQUFLRyxVQUFVM0IsTUFBTSxLQUFLLGNBQWMyQixVQUFVRyxLQUFLLEdBQUc7Z0JBQzFETCxRQUFRRyxVQUFVNUIsTUFBTSxLQUFLLGNBQWM0QixVQUFVRSxLQUFLLEdBQUc7Z0JBQzdEM0IsS0FBSzBCLFVBQVU3QixNQUFNLEtBQUssY0FBYzZCLFVBQVVDLEtBQUssR0FBRztZQUM1RDtZQUVBLFVBQVU7WUFDVlYsUUFBUVcsT0FBTyxDQUFDLENBQUNDLFFBQVFDO2dCQUN2QixJQUFJRCxPQUFPaEMsTUFBTSxLQUFLLFlBQVk7b0JBQ2hDLE1BQU1rQyxTQUFTO3dCQUFDO3dCQUFPO3dCQUFPO3dCQUFVO3FCQUFNO29CQUM5Q2pELFFBQVFDLElBQUksQ0FDVixDQUFDLEVBQUVnRCxNQUFNLENBQUNELE1BQU0sQ0FBQyxrQ0FBa0MsRUFBRWQsVUFBVSxDQUFDLENBQUMsRUFDakVhLE9BQU9HLE1BQU07Z0JBRWpCO1lBQ0Y7UUFDRjtJQUNGO0lBRUFsRCxRQUFROEIsR0FBRyxDQUFDLFdBQVdGO0lBQ3ZCLE9BQU9BO0FBQ1QsRUFBQztBQUVELFlBQVk7QUFDTCxNQUFNdUIsb0JBQW9CO0lBQy9CbkcsYUFBYW9HLEtBQUs7SUFDbEJwRCxRQUFROEIsR0FBRyxDQUFDO0FBQ2QsRUFBQztBQUVELGNBQWM7QUFDUCxNQUFNdUIsaUJBQWlCO0lBQzVCLE9BQU87UUFDTEMsV0FBV3RHLGFBQWF1RyxJQUFJO1FBQzVCQyxnQkFBZ0JyRyxhQUFhb0csSUFBSTtRQUNqQ0UsY0FBY0MsTUFBTUMsSUFBSSxDQUFDM0csYUFBYTRHLE9BQU8sSUFBSUMsR0FBRyxDQUFDLENBQUMsQ0FBQ0MsS0FBS2pCLE1BQU0sR0FBTTtnQkFDdEVpQjtnQkFDQUMsS0FBS3RGLEtBQUtDLEdBQUcsS0FBS21FLE1BQU1sRSxTQUFTO2dCQUNqQ0MsU0FBU2lFLE1BQU1qRSxPQUFPO1lBQ3hCO0lBQ0Y7QUFDRixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vcGFja2FnZXMvYXBwL3N0b3Jlcy91dGlscy50cz8zNjc5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGV0aGVycyB9IGZyb20gJ2V0aGVycydcbmltcG9ydCB7IENvbm5lY3Rpb24sIFB1YmxpY0tleSB9IGZyb20gJ0Bzb2xhbmEvd2ViMy5qcydcbmltcG9ydCB7IEJTQ19SUEMsIEVUSF9SUEMsIFNPTEFOQV9SUEMgfSBmcm9tICdhcHAvdXRpbHMvY29uc3RhbnRzJ1xuaW1wb3J0IHsgbmV0d29ya01hbmFnZXIsIHJlcXVlc3RXaXRoUmV0cnkgfSBmcm9tICdhcHAvdXRpbHMvbmV0d29ya01hbmFnZXInXG5cbi8vIOivt+axgue8k+WtmOWSjOmYsuaKllxuY29uc3QgYmFsYW5jZUNhY2hlID0gbmV3IE1hcDxzdHJpbmcsIHsgYmFsYW5jZTogc3RyaW5nOyB0aW1lc3RhbXA6IG51bWJlciB9PigpXG5jb25zdCBDQUNIRV9EVVJBVElPTiA9IDMwMDAwIC8vIDMw56eS57yT5a2YXG5jb25zdCByZXF1ZXN0UXVldWUgPSBuZXcgTWFwPHN0cmluZywgUHJvbWlzZTxzdHJpbmc+PigpXG5cbi8vIOW4pui2heaXtueahOivt+axguWHveaVsFxuY29uc3QgZmV0Y2hXaXRoVGltZW91dCA9IGFzeW5jICh1cmw6IHN0cmluZywgdGltZW91dCA9IDUwMDApOiBQcm9taXNlPFJlc3BvbnNlPiA9PiB7XG4gIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKClcbiAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiBjb250cm9sbGVyLmFib3J0KCksIHRpbWVvdXQpXG5cbiAgdHJ5IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwgeyBzaWduYWw6IGNvbnRyb2xsZXIuc2lnbmFsIH0pXG4gICAgY2xlYXJUaW1lb3V0KHRpbWVvdXRJZClcbiAgICByZXR1cm4gcmVzcG9uc2VcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjbGVhclRpbWVvdXQodGltZW91dElkKVxuICAgIHRocm93IGVycm9yXG4gIH1cbn1cblxuLy8gRVZNIOWFrOmTvumAmueUqCAtIOa3u+WKoOe8k+WtmOWSjOmUmeivr+WkhOeQhlxuY29uc3QgZ2V0RXZtQmFsYW5jZSA9IGFzeW5jIChhZGRyZXNzOiBzdHJpbmcsIGNoYWluOiAnZXRoJyB8ICdic2MnKSA9PiB7XG4gIGNvbnN0IHJwYyA9IG5ldHdvcmtNYW5hZ2VyLmdldEF2YWlsYWJsZVJwYyhjaGFpbilcbiAgY29uc3QgY2FjaGVLZXkgPSBgJHtjaGFpbn0tJHthZGRyZXNzfWBcblxuICAvLyDmo4Dmn6XnvJPlrZhcbiAgY29uc3QgY2FjaGVkID0gYmFsYW5jZUNhY2hlLmdldChjYWNoZUtleSlcbiAgaWYgKGNhY2hlZCAmJiBEYXRlLm5vdygpIC0gY2FjaGVkLnRpbWVzdGFtcCA8IENBQ0hFX0RVUkFUSU9OKSB7XG4gICAgcmV0dXJuIGNhY2hlZC5iYWxhbmNlXG4gIH1cblxuICAvLyDmo4Dmn6XmmK/lkKblt7LmnInnm7jlkIzor7fmsYLlnKjov5vooYxcbiAgaWYgKHJlcXVlc3RRdWV1ZS5oYXMoY2FjaGVLZXkpKSB7XG4gICAgcmV0dXJuIHJlcXVlc3RRdWV1ZS5nZXQoY2FjaGVLZXkpIVxuICB9XG5cbiAgY29uc3QgcmVxdWVzdCA9IHJlcXVlc3RXaXRoUmV0cnkoYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IGV0aGVycy5Kc29uUnBjUHJvdmlkZXIocnBjLCB1bmRlZmluZWQsIHtcbiAgICAgIHN0YXRpY05ldHdvcms6IHRydWUsXG4gICAgICBiYXRjaE1heENvdW50OiAxLFxuICAgIH0pXG5cbiAgICAvLyDorr7nva7otoXml7ZcbiAgICBjb25zdCB0aW1lb3V0UHJvbWlzZSA9IG5ldyBQcm9taXNlPG5ldmVyPigoXywgcmVqZWN0KSA9PiB7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHJlamVjdChuZXcgRXJyb3IoJ1JlcXVlc3QgdGltZW91dCcpKSwgNTAwMClcbiAgICB9KVxuXG4gICAgY29uc3QgYmFsYW5jZVByb21pc2UgPSBwcm92aWRlci5nZXRCYWxhbmNlKGFkZHJlc3MpXG4gICAgY29uc3QgYmFsID0gYXdhaXQgUHJvbWlzZS5yYWNlKFtiYWxhbmNlUHJvbWlzZSwgdGltZW91dFByb21pc2VdKVxuICAgIGNvbnN0IGJhbGFuY2UgPSBldGhlcnMuZm9ybWF0RXRoZXIoYmFsKVxuXG4gICAgLy8g57yT5a2Y57uT5p6cXG4gICAgYmFsYW5jZUNhY2hlLnNldChjYWNoZUtleSwgeyBiYWxhbmNlLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfSlcbiAgICByZXR1cm4gYmFsYW5jZVxuICB9LCBycGMpXG4gICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgY29uc29sZS53YXJuKGBGYWlsZWQgdG8gZmV0Y2ggJHtjaGFpbi50b1VwcGVyQ2FzZSgpfSBiYWxhbmNlIGZvciAke2FkZHJlc3N9OmAsIGVycm9yLm1lc3NhZ2UpXG4gICAgICByZXR1cm4gJzAnXG4gICAgfSlcbiAgICAuZmluYWxseSgoKSA9PiB7XG4gICAgICByZXF1ZXN0UXVldWUuZGVsZXRlKGNhY2hlS2V5KVxuICAgIH0pXG5cbiAgcmVxdWVzdFF1ZXVlLnNldChjYWNoZUtleSwgcmVxdWVzdClcbiAgcmV0dXJuIHJlcXVlc3Rcbn1cblxuY29uc3QgZ2V0U29sQmFsYW5jZSA9IGFzeW5jIChhZGRyZXNzOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgcnBjID0gbmV0d29ya01hbmFnZXIuZ2V0QXZhaWxhYmxlUnBjKCdzb2xhbmEnKVxuICBjb25zdCBjYWNoZUtleSA9IGBzb2xhbmEtJHthZGRyZXNzfWBcblxuICAvLyDmo4Dmn6XnvJPlrZhcbiAgY29uc3QgY2FjaGVkID0gYmFsYW5jZUNhY2hlLmdldChjYWNoZUtleSlcbiAgaWYgKGNhY2hlZCAmJiBEYXRlLm5vdygpIC0gY2FjaGVkLnRpbWVzdGFtcCA8IENBQ0hFX0RVUkFUSU9OKSB7XG4gICAgcmV0dXJuIGNhY2hlZC5iYWxhbmNlXG4gIH1cblxuICAvLyDmo4Dmn6XmmK/lkKblt7LmnInnm7jlkIzor7fmsYLlnKjov5vooYxcbiAgaWYgKHJlcXVlc3RRdWV1ZS5oYXMoY2FjaGVLZXkpKSB7XG4gICAgcmV0dXJuIHJlcXVlc3RRdWV1ZS5nZXQoY2FjaGVLZXkpIVxuICB9XG5cbiAgY29uc3QgcmVxdWVzdCA9IHJlcXVlc3RXaXRoUmV0cnkoYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IGNvbm5lY3Rpb24gPSBuZXcgQ29ubmVjdGlvbihycGMsIHtcbiAgICAgIGNvbW1pdG1lbnQ6ICdjb25maXJtZWQnLFxuICAgICAgY29uZmlybVRyYW5zYWN0aW9uSW5pdGlhbFRpbWVvdXQ6IDUwMDAsXG4gICAgfSlcblxuICAgIGNvbnN0IHRpbWVvdXRQcm9taXNlID0gbmV3IFByb21pc2U8bmV2ZXI+KChfLCByZWplY3QpID0+IHtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVqZWN0KG5ldyBFcnJvcignUmVxdWVzdCB0aW1lb3V0JykpLCA1MDAwKVxuICAgIH0pXG5cbiAgICBjb25zdCBiYWxhbmNlUHJvbWlzZSA9IGNvbm5lY3Rpb24uZ2V0QmFsYW5jZShuZXcgUHVibGljS2V5KGFkZHJlc3MpKVxuICAgIGNvbnN0IGxhbXBvcnRzID0gYXdhaXQgUHJvbWlzZS5yYWNlKFtiYWxhbmNlUHJvbWlzZSwgdGltZW91dFByb21pc2VdKVxuICAgIGNvbnN0IGJhbGFuY2UgPSAobGFtcG9ydHMgLyAxZTkpLnRvRml4ZWQoNilcblxuICAgIC8vIOe8k+WtmOe7k+aenFxuICAgIGJhbGFuY2VDYWNoZS5zZXQoY2FjaGVLZXksIHsgYmFsYW5jZSwgdGltZXN0YW1wOiBEYXRlLm5vdygpIH0pXG4gICAgcmV0dXJuIGJhbGFuY2VcbiAgfSwgcnBjKVxuICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIGZldGNoIFNvbGFuYSBiYWxhbmNlIGZvciAke2FkZHJlc3N9OmAsIGVycm9yLm1lc3NhZ2UpXG4gICAgICByZXR1cm4gJzAnXG4gICAgfSlcbiAgICAuZmluYWxseSgoKSA9PiB7XG4gICAgICByZXF1ZXN0UXVldWUuZGVsZXRlKGNhY2hlS2V5KVxuICAgIH0pXG5cbiAgcmVxdWVzdFF1ZXVlLnNldChjYWNoZUtleSwgcmVxdWVzdClcbiAgcmV0dXJuIHJlcXVlc3Rcbn1cblxuLy8g5Y+v6YCJ77yaQlRDIOafpeivou+8iOazqOaEj+S9oOW9k+WJjSBCVEMg5Zyw5Z2A5pivIEVWTSDmoLzlvI/vvIlcbmNvbnN0IGdldEJ0Y0JhbGFuY2UgPSBhc3luYyAoYWRkcmVzczogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IGNhY2hlS2V5ID0gYGJ0Yy0ke2FkZHJlc3N9YFxuXG4gIC8vIOajgOafpee8k+WtmFxuICBjb25zdCBjYWNoZWQgPSBiYWxhbmNlQ2FjaGUuZ2V0KGNhY2hlS2V5KVxuICBpZiAoY2FjaGVkICYmIERhdGUubm93KCkgLSBjYWNoZWQudGltZXN0YW1wIDwgQ0FDSEVfRFVSQVRJT04pIHtcbiAgICByZXR1cm4gY2FjaGVkLmJhbGFuY2VcbiAgfVxuXG4gIC8vIOajgOafpeaYr+WQpuW3suacieebuOWQjOivt+axguWcqOi/m+ihjFxuICBpZiAocmVxdWVzdFF1ZXVlLmhhcyhjYWNoZUtleSkpIHtcbiAgICByZXR1cm4gcmVxdWVzdFF1ZXVlLmdldChjYWNoZUtleSkhXG4gIH1cblxuICBjb25zdCByZXF1ZXN0ID0gKGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzID0gYXdhaXQgZmV0Y2hXaXRoVGltZW91dChgaHR0cHM6Ly9ibG9ja3N0cmVhbS5pbmZvL2FwaS9hZGRyZXNzLyR7YWRkcmVzc31gKVxuICAgICAgaWYgKCFyZXMub2spIHRocm93IG5ldyBFcnJvcihgSFRUUCAke3Jlcy5zdGF0dXN9YClcblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlcy5qc29uKClcbiAgICAgIGNvbnN0IGJ0YyA9IChkYXRhLmNoYWluX3N0YXRzLmZ1bmRlZF90eG9fc3VtIC0gZGF0YS5jaGFpbl9zdGF0cy5zcGVudF90eG9fc3VtKSAvIDFlOFxuICAgICAgY29uc3QgYmFsYW5jZSA9IGJ0Yy50b0ZpeGVkKDgpXG5cbiAgICAgIC8vIOe8k+WtmOe7k+aenFxuICAgICAgYmFsYW5jZUNhY2hlLnNldChjYWNoZUtleSwgeyBiYWxhbmNlLCB0aW1lc3RhbXA6IERhdGUubm93KCkgfSlcbiAgICAgIHJldHVybiBiYWxhbmNlXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIGZldGNoIEJUQyBiYWxhbmNlIGZvciAke2FkZHJlc3N9OmAsIGVycm9yLm1lc3NhZ2UpXG4gICAgICByZXR1cm4gJzAnXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHJlcXVlc3RRdWV1ZS5kZWxldGUoY2FjaGVLZXkpXG4gICAgfVxuICB9KSgpXG5cbiAgcmVxdWVzdFF1ZXVlLnNldChjYWNoZUtleSwgcmVxdWVzdClcbiAgcmV0dXJuIHJlcXVlc3Rcbn1cblxuY29uc3QgZ2V0QnRjVGVzdG5ldEJhbGFuY2UgPSBhc3luYyAoYWRkcmVzczogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IGNhY2hlS2V5ID0gYGJ0Yy10ZXN0bmV0LSR7YWRkcmVzc31gXG5cbiAgLy8g5qOA5p+l57yT5a2YXG4gIGNvbnN0IGNhY2hlZCA9IGJhbGFuY2VDYWNoZS5nZXQoY2FjaGVLZXkpXG4gIGlmIChjYWNoZWQgJiYgRGF0ZS5ub3coKSAtIGNhY2hlZC50aW1lc3RhbXAgPCBDQUNIRV9EVVJBVElPTikge1xuICAgIHJldHVybiBjYWNoZWQuYmFsYW5jZVxuICB9XG5cbiAgLy8g5qOA5p+l5piv5ZCm5bey5pyJ55u45ZCM6K+35rGC5Zyo6L+b6KGMXG4gIGlmIChyZXF1ZXN0UXVldWUuaGFzKGNhY2hlS2V5KSkge1xuICAgIHJldHVybiByZXF1ZXN0UXVldWUuZ2V0KGNhY2hlS2V5KSFcbiAgfVxuXG4gIGNvbnN0IHJlcXVlc3QgPSAoYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaFdpdGhUaW1lb3V0KGBodHRwczovL2Jsb2Nrc3RyZWFtLmluZm8vdGVzdG5ldC9hcGkvYWRkcmVzcy8ke2FkZHJlc3N9YClcbiAgICAgIGlmICghcmVzLm9rKSB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgJHtyZXMuc3RhdHVzfWApXG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXMuanNvbigpXG4gICAgICBjb25zdCBidGMgPSAoZGF0YS5jaGFpbl9zdGF0cy5mdW5kZWRfdHhvX3N1bSAtIGRhdGEuY2hhaW5fc3RhdHMuc3BlbnRfdHhvX3N1bSkgLyAxZThcbiAgICAgIGNvbnN0IGJhbGFuY2UgPSBidGMudG9GaXhlZCg4KVxuXG4gICAgICAvLyDnvJPlrZjnu5PmnpxcbiAgICAgIGJhbGFuY2VDYWNoZS5zZXQoY2FjaGVLZXksIHsgYmFsYW5jZSwgdGltZXN0YW1wOiBEYXRlLm5vdygpIH0pXG4gICAgICByZXR1cm4gYmFsYW5jZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oYEZhaWxlZCB0byBmZXRjaCBCVEMgdGVzdG5ldCBiYWxhbmNlIGZvciAke2FkZHJlc3N9OmAsIGVycm9yLm1lc3NhZ2UpXG4gICAgICByZXR1cm4gJzAnXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHJlcXVlc3RRdWV1ZS5kZWxldGUoY2FjaGVLZXkpXG4gICAgfVxuICB9KSgpXG5cbiAgcmVxdWVzdFF1ZXVlLnNldChjYWNoZUtleSwgcmVxdWVzdClcbiAgcmV0dXJuIHJlcXVlc3Rcbn1cblxuY29uc3QgZ2V0VGVzdG5ldEJhbGFuY2UgPSBhc3luYyAoYWRkcmVzczogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IGNhY2hlS2V5ID0gYGJ0Yy1tZW1wb29sLSR7YWRkcmVzc31gXG5cbiAgLy8g5qOA5p+l57yT5a2YXG4gIGNvbnN0IGNhY2hlZCA9IGJhbGFuY2VDYWNoZS5nZXQoY2FjaGVLZXkpXG4gIGlmIChjYWNoZWQgJiYgRGF0ZS5ub3coKSAtIGNhY2hlZC50aW1lc3RhbXAgPCBDQUNIRV9EVVJBVElPTikge1xuICAgIHJldHVybiBjYWNoZWQuYmFsYW5jZVxuICB9XG5cbiAgLy8g5qOA5p+l5piv5ZCm5bey5pyJ55u45ZCM6K+35rGC5Zyo6L+b6KGMXG4gIGlmIChyZXF1ZXN0UXVldWUuaGFzKGNhY2hlS2V5KSkge1xuICAgIHJldHVybiByZXF1ZXN0UXVldWUuZ2V0KGNhY2hlS2V5KSFcbiAgfVxuXG4gIGNvbnN0IHJlcXVlc3QgPSAoYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXMgPSBhd2FpdCBmZXRjaFdpdGhUaW1lb3V0KGBodHRwczovL21lbXBvb2wuc3BhY2UvdGVzdG5ldC9hcGkvYWRkcmVzcy8ke2FkZHJlc3N9YClcbiAgICAgIGlmICghcmVzLm9rKSB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgJHtyZXMuc3RhdHVzfWApXG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXMuanNvbigpXG4gICAgICBjb25zdCBiYWxhbmNlID1cbiAgICAgICAgKGRhdGEuY2hhaW5fc3RhdHMuZnVuZGVkX3R4b19zdW0gLVxuICAgICAgICAgIGRhdGEuY2hhaW5fc3RhdHMuc3BlbnRfdHhvX3N1bSArXG4gICAgICAgICAgZGF0YS5tZW1wb29sX3N0YXRzLmZ1bmRlZF90eG9fc3VtIC1cbiAgICAgICAgICBkYXRhLm1lbXBvb2xfc3RhdHMuc3BlbnRfdHhvX3N1bSkgL1xuICAgICAgICAxZThcblxuICAgICAgY29uc3QgYmFsYW5jZVN0ciA9IGJhbGFuY2UudG9GaXhlZCg4KVxuXG4gICAgICAvLyDnvJPlrZjnu5PmnpxcbiAgICAgIGJhbGFuY2VDYWNoZS5zZXQoY2FjaGVLZXksIHsgYmFsYW5jZTogYmFsYW5jZVN0ciwgdGltZXN0YW1wOiBEYXRlLm5vdygpIH0pXG4gICAgICByZXR1cm4gYmFsYW5jZVN0clxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oYEZhaWxlZCB0byBmZXRjaCBCVEMgbWVtcG9vbCBiYWxhbmNlIGZvciAke2FkZHJlc3N9OmAsIGVycm9yLm1lc3NhZ2UpXG4gICAgICByZXR1cm4gJzAnXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHJlcXVlc3RRdWV1ZS5kZWxldGUoY2FjaGVLZXkpXG4gICAgfVxuICB9KSgpXG5cbiAgcmVxdWVzdFF1ZXVlLnNldChjYWNoZUtleSwgcmVxdWVzdClcbiAgcmV0dXJuIHJlcXVlc3Rcbn1cblxuLy8g5qC45b+D5Ye95pWw77ya5LuOIHdhbGxldExpc3Qg5Lit5p+l6K+i5L2Z6aKd5bm25pu05pawIFp1c3RhbmQg54q25oCBXG5leHBvcnQgY29uc3QgZmV0Y2hBbGxCYWxhbmNlcyA9IGFzeW5jICh3YWxsZXRMaXN0KSA9PiB7XG4gIGNvbnN0IGJhbGFuY2VNYXAgPSB7fVxuXG4gIGlmICghd2FsbGV0TGlzdCB8fCB3YWxsZXRMaXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBiYWxhbmNlTWFwXG4gIH1cblxuICBjb25zb2xlLmxvZygn5byA5aeL6I635Y+W5L2Z6aKdLi4uJylcblxuICBmb3IgKGNvbnN0IHdhbGxldCBvZiB3YWxsZXRMaXN0KSB7XG4gICAgZm9yIChjb25zdCBhY2NvdW50IG9mIHdhbGxldC5hY2NvdW50cykge1xuICAgICAgY29uc3QgYWNjb3VudElkID0gYWNjb3VudC5hY2NvdW50SWRcblxuICAgICAgLy8g5L2/55SoIFByb21pc2UuYWxsU2V0dGxlZCDogIzkuI3mmK8gUHJvbWlzZS5hbGzvvIzpgb/lhY3kuIDkuKrlpLHotKXlr7zoh7Tlhajpg6jlpLHotKVcbiAgICAgIGNvbnN0IHJlc3VsdHMgPSBhd2FpdCBQcm9taXNlLmFsbFNldHRsZWQoW1xuICAgICAgICBhY2NvdW50LmV0aD8uYWRkcmVzcyA/IGdldEV2bUJhbGFuY2UoYWNjb3VudC5ldGguYWRkcmVzcywgJ2V0aCcpIDogUHJvbWlzZS5yZXNvbHZlKCcwJyksXG4gICAgICAgIGFjY291bnQuYnNjPy5hZGRyZXNzID8gZ2V0RXZtQmFsYW5jZShhY2NvdW50LmJzYy5hZGRyZXNzLCAnYnNjJykgOiBQcm9taXNlLnJlc29sdmUoJzAnKSxcbiAgICAgICAgYWNjb3VudC5zb2xhbmE/LmFkZHJlc3MgPyBnZXRTb2xCYWxhbmNlKGFjY291bnQuc29sYW5hLmFkZHJlc3MpIDogUHJvbWlzZS5yZXNvbHZlKCcwJyksXG4gICAgICAgIGFjY291bnQuYnRjPy5hZGRyZXNzID8gZ2V0VGVzdG5ldEJhbGFuY2UoYWNjb3VudC5idGMuYWRkcmVzcykgOiBQcm9taXNlLnJlc29sdmUoJzAnKSxcbiAgICAgIF0pXG5cbiAgICAgIGNvbnN0IFtldGhSZXN1bHQsIGJzY1Jlc3VsdCwgc29sUmVzdWx0LCBidGNSZXN1bHRdID0gcmVzdWx0c1xuXG4gICAgICBiYWxhbmNlTWFwW2FjY291bnRJZF0gPSB7XG4gICAgICAgIGV0aDogZXRoUmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyBldGhSZXN1bHQudmFsdWUgOiAnMCcsXG4gICAgICAgIGJzYzogYnNjUmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyBic2NSZXN1bHQudmFsdWUgOiAnMCcsXG4gICAgICAgIHNvbGFuYTogc29sUmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyBzb2xSZXN1bHQudmFsdWUgOiAnMCcsXG4gICAgICAgIGJ0YzogYnRjUmVzdWx0LnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyBidGNSZXN1bHQudmFsdWUgOiAnMCcsXG4gICAgICB9XG5cbiAgICAgIC8vIOiusOW9leWksei0peeahOivt+axglxuICAgICAgcmVzdWx0cy5mb3JFYWNoKChyZXN1bHQsIGluZGV4KSA9PiB7XG4gICAgICAgIGlmIChyZXN1bHQuc3RhdHVzID09PSAncmVqZWN0ZWQnKSB7XG4gICAgICAgICAgY29uc3QgY2hhaW5zID0gWydFVEgnLCAnQlNDJywgJ1NvbGFuYScsICdCVEMnXVxuICAgICAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgICAgIGAke2NoYWluc1tpbmRleF19IGJhbGFuY2UgZmV0Y2ggZmFpbGVkIGZvciBhY2NvdW50ICR7YWNjb3VudElkfTpgLFxuICAgICAgICAgICAgcmVzdWx0LnJlYXNvblxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgfSlcbiAgICB9XG4gIH1cblxuICBjb25zb2xlLmxvZygn5L2Z6aKd6I635Y+W5a6M5oiQOicsIGJhbGFuY2VNYXApXG4gIHJldHVybiBiYWxhbmNlTWFwXG59XG5cbi8vIOa4heeQhue8k+WtmOeahOW3peWFt+WHveaVsFxuZXhwb3J0IGNvbnN0IGNsZWFyQmFsYW5jZUNhY2hlID0gKCkgPT4ge1xuICBiYWxhbmNlQ2FjaGUuY2xlYXIoKVxuICBjb25zb2xlLmxvZygn5L2Z6aKd57yT5a2Y5bey5riF55CGJylcbn1cblxuLy8g6I635Y+W57yT5a2Y54q25oCB55qE5bel5YW35Ye95pWwXG5leHBvcnQgY29uc3QgZ2V0Q2FjaGVTdGF0dXMgPSAoKSA9PiB7XG4gIHJldHVybiB7XG4gICAgY2FjaGVTaXplOiBiYWxhbmNlQ2FjaGUuc2l6ZSxcbiAgICBhY3RpdmVSZXF1ZXN0czogcmVxdWVzdFF1ZXVlLnNpemUsXG4gICAgY2FjaGVFbnRyaWVzOiBBcnJheS5mcm9tKGJhbGFuY2VDYWNoZS5lbnRyaWVzKCkpLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoe1xuICAgICAga2V5LFxuICAgICAgYWdlOiBEYXRlLm5vdygpIC0gdmFsdWUudGltZXN0YW1wLFxuICAgICAgYmFsYW5jZTogdmFsdWUuYmFsYW5jZSxcbiAgICB9KSksXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJldGhlcnMiLCJDb25uZWN0aW9uIiwiUHVibGljS2V5IiwibmV0d29ya01hbmFnZXIiLCJyZXF1ZXN0V2l0aFJldHJ5IiwiYmFsYW5jZUNhY2hlIiwiTWFwIiwiQ0FDSEVfRFVSQVRJT04iLCJyZXF1ZXN0UXVldWUiLCJmZXRjaFdpdGhUaW1lb3V0IiwidXJsIiwidGltZW91dCIsImNvbnRyb2xsZXIiLCJBYm9ydENvbnRyb2xsZXIiLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiYWJvcnQiLCJyZXNwb25zZSIsImZldGNoIiwic2lnbmFsIiwiY2xlYXJUaW1lb3V0IiwiZXJyb3IiLCJnZXRFdm1CYWxhbmNlIiwiYWRkcmVzcyIsImNoYWluIiwicnBjIiwiZ2V0QXZhaWxhYmxlUnBjIiwiY2FjaGVLZXkiLCJjYWNoZWQiLCJnZXQiLCJEYXRlIiwibm93IiwidGltZXN0YW1wIiwiYmFsYW5jZSIsImhhcyIsInJlcXVlc3QiLCJwcm92aWRlciIsIkpzb25ScGNQcm92aWRlciIsInVuZGVmaW5lZCIsInN0YXRpY05ldHdvcmsiLCJiYXRjaE1heENvdW50IiwidGltZW91dFByb21pc2UiLCJQcm9taXNlIiwiXyIsInJlamVjdCIsIkVycm9yIiwiYmFsYW5jZVByb21pc2UiLCJnZXRCYWxhbmNlIiwiYmFsIiwicmFjZSIsImZvcm1hdEV0aGVyIiwic2V0IiwiY2F0Y2giLCJjb25zb2xlIiwid2FybiIsInRvVXBwZXJDYXNlIiwibWVzc2FnZSIsImZpbmFsbHkiLCJkZWxldGUiLCJnZXRTb2xCYWxhbmNlIiwiY29ubmVjdGlvbiIsImNvbW1pdG1lbnQiLCJjb25maXJtVHJhbnNhY3Rpb25Jbml0aWFsVGltZW91dCIsImxhbXBvcnRzIiwidG9GaXhlZCIsImdldEJ0Y0JhbGFuY2UiLCJyZXMiLCJvayIsInN0YXR1cyIsImRhdGEiLCJqc29uIiwiYnRjIiwiY2hhaW5fc3RhdHMiLCJmdW5kZWRfdHhvX3N1bSIsInNwZW50X3R4b19zdW0iLCJnZXRCdGNUZXN0bmV0QmFsYW5jZSIsImdldFRlc3RuZXRCYWxhbmNlIiwibWVtcG9vbF9zdGF0cyIsImJhbGFuY2VTdHIiLCJmZXRjaEFsbEJhbGFuY2VzIiwid2FsbGV0TGlzdCIsImJhbGFuY2VNYXAiLCJsZW5ndGgiLCJsb2ciLCJ3YWxsZXQiLCJhY2NvdW50IiwiYWNjb3VudHMiLCJhY2NvdW50SWQiLCJyZXN1bHRzIiwiYWxsU2V0dGxlZCIsImV0aCIsInJlc29sdmUiLCJic2MiLCJzb2xhbmEiLCJldGhSZXN1bHQiLCJic2NSZXN1bHQiLCJzb2xSZXN1bHQiLCJidGNSZXN1bHQiLCJ2YWx1ZSIsImZvckVhY2giLCJyZXN1bHQiLCJpbmRleCIsImNoYWlucyIsInJlYXNvbiIsImNsZWFyQmFsYW5jZUNhY2hlIiwiY2xlYXIiLCJnZXRDYWNoZVN0YXR1cyIsImNhY2hlU2l6ZSIsInNpemUiLCJhY3RpdmVSZXF1ZXN0cyIsImNhY2hlRW50cmllcyIsIkFycmF5IiwiZnJvbSIsImVudHJpZXMiLCJtYXAiLCJrZXkiLCJhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/app/stores/utils.ts\n");

/***/ }),

/***/ "../../packages/app/stores/walletStore.ts":
/*!************************************************!*\
  !*** ../../packages/app/stores/walletStore.ts ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNewVault: () => (/* binding */ createNewVault),\n/* harmony export */   createWallet: () => (/* binding */ createWallet),\n/* harmony export */   createWalletNext: () => (/* binding */ createWalletNext),\n/* harmony export */   useWalletStore: () => (/* binding */ useWalletStore)\n/* harmony export */ });\n/* harmony import */ var app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! app/utils/bip39 */ \"../../packages/app/utils/bip39.ts\");\n/* harmony import */ var app_utils_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! app/utils/constants */ \"../../packages/app/utils/constants.ts\");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\n/* harmony import */ var bip39__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bip39 */ \"bip39\");\n/* harmony import */ var bip39__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bip39__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils */ \"../../packages/app/stores/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_1__, app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__, _utils__WEBPACK_IMPORTED_MODULE_5__]);\n([zustand__WEBPACK_IMPORTED_MODULE_1__, app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__, _utils__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nasync function createNewVault(passorwd) {\n    await app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].setItem(app_utils_constants__WEBPACK_IMPORTED_MODULE_3__.PASSWORD_VAULT, passorwd);\n}\nasync function createWallet(_mnemonic) {\n    const mnemonic = _mnemonic || (0,bip39__WEBPACK_IMPORTED_MODULE_0__.generateMnemonic)();\n    const accountBtc = await (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateWalletBtc)(mnemonic, 0);\n    const accountEth = await (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateWalletEth)(mnemonic, 0);\n    const accountBsc = await (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateWalletBsc)(mnemonic, 0);\n    const accountSolana = await (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateWalletSolana)(mnemonic, 0);\n    const _walletId = (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateUID)();\n    return {\n        mnemonic,\n        walletId: _walletId,\n        accounts: [\n            {\n                walletId: _walletId,\n                accountId: (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateUID)(),\n                btc: accountBtc,\n                eth: accountEth,\n                bsc: accountBsc,\n                solana: accountSolana\n            }\n        ]\n    };\n}\nasync function createWalletNext(mnemonic, index) {\n    const accountEth = await (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateWalletEth)(mnemonic, index);\n    const accountBsc = await (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateWalletBsc)(mnemonic, index);\n    const accountSolana = await (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateWalletSolana)(mnemonic, 0);\n    return {\n        mnemonic,\n        accounts: {\n            accountId: (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_4__.generateUID)(),\n            eth: accountEth,\n            bsc: accountBsc,\n            solana: accountSolana\n        }\n    };\n}\nconst useWalletStore = (0,zustand__WEBPACK_IMPORTED_MODULE_1__.create)((set, get)=>({\n        user: null,\n        lastestMnemonic: \"\",\n        walletList: [],\n        currentAccount: {\n            walletId: \"\",\n            accountId: \"\",\n            eth: null,\n            bsc: null,\n            solana: null\n        },\n        addAccountWalletId: \"\",\n        init: ()=>{\n            // 初始化钱包\n            get().getLocalWallet();\n        // 暂时注释掉自动余额查询，避免频繁网络请求\n        // setTimeout(() => {\n        //   get().fetchAllBalances()\n        // }, 2000) // 延迟2秒\n        },\n        getLocalWallet: ()=>{\n            try {\n                const _walletList = JSON.parse(localStorage.getItem(\"WALLET_LIST\") || \"[]\");\n                set({\n                    walletList: _walletList\n                });\n                // 尝试恢复上次选中的账户\n                const lastAccountId = localStorage.getItem(\"CURRENT_ACCOUNT_ID\");\n                let foundAccount = null;\n                if (lastAccountId && _walletList.length > 0) {\n                    // 查找上次选中的账户\n                    for (const wallet of _walletList){\n                        const account = wallet.accounts.find((acc)=>acc.accountId === lastAccountId);\n                        if (account) {\n                            foundAccount = account;\n                            break;\n                        }\n                    }\n                }\n                // 如果没有找到上次的账户，使用第一个账户\n                if (!foundAccount && _walletList[0] && _walletList[0].accounts[0]) {\n                    foundAccount = _walletList[0].accounts[0];\n                }\n                if (foundAccount) {\n                    set({\n                        currentAccount: foundAccount\n                    });\n                }\n                return _walletList;\n            } catch (error) {\n                return [];\n            }\n        },\n        setVault: async (vault)=>{\n            await app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].setItem(app_utils_constants__WEBPACK_IMPORTED_MODULE_3__.PASSWORD_VAULT, vault);\n        },\n        getVault: async ()=>{\n            return await app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].getItem(app_utils_constants__WEBPACK_IMPORTED_MODULE_3__.PASSWORD_VAULT) || \"\";\n        },\n        createNewVaultAndGetSeedPhrase: async (passorwd)=>{\n            const _password = passorwd || await get().getVault();\n            if (_password) {\n                await createNewVault(_password);\n                const wallet = await createWallet();\n                set({\n                    lastestMnemonic: wallet.mnemonic\n                });\n                const _walletList = [\n                    ...get().walletList,\n                    wallet\n                ];\n                set({\n                    walletList: _walletList\n                });\n                // 持久缓存\n                app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].setItem(\"WALLET_LIST\", JSON.stringify(_walletList));\n                return wallet.mnemonic;\n            }\n            return \"\";\n        },\n        importVaultAndGetSeedPhrase: async (mnemonic)=>{\n            // 要先查找钱包list里有没有相关的注记词，如果有，则不导入\n            const exists = get().walletList.some((wallet)=>wallet.mnemonic === mnemonic);\n            if (exists) {\n                // 已存在，不导入\n                return \"\";\n            }\n            const wallet = await createWallet(mnemonic);\n            set({\n                lastestMnemonic: wallet.mnemonic\n            });\n            const _walletList = [\n                ...get().walletList,\n                wallet\n            ];\n            set({\n                walletList: _walletList\n            });\n            // 持久缓存\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].setItem(\"WALLET_LIST\", JSON.stringify(_walletList));\n            return wallet.mnemonic;\n        },\n        setAddAccountWalletId: (walletId)=>{\n            set({\n                addAccountWalletId: walletId\n            });\n        },\n        createNextAccount: async ()=>{\n            const walletId = get().addAccountWalletId;\n            const walletList = get().walletList;\n            const walletIndex = walletList.findIndex((wallet)=>wallet.walletId === walletId);\n            const wallet = walletList[walletIndex];\n            if (wallet) {\n                const newAccount = await createWalletNext(wallet.mnemonic, wallet.accounts.length);\n                wallet.accounts = [\n                    ...wallet.accounts,\n                    newAccount.accounts\n                ];\n                walletList[walletIndex] = {\n                    ...wallet\n                };\n                set({\n                    walletList: walletList\n                });\n                app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].setItem(\"WALLET_LIST\", JSON.stringify(walletList));\n            }\n        },\n        fetchAllBalances: async ()=>{\n            try {\n                const walletList = get().walletList;\n                if (!walletList || walletList.length === 0) {\n                    console.log(\"没有钱包需要获取余额\");\n                    return;\n                }\n                console.log(\"开始获取钱包余额...\");\n                const balanceMap = await (0,_utils__WEBPACK_IMPORTED_MODULE_5__.fetchAllBalances)(walletList);\n                console.log(\"Balance map:\", balanceMap);\n                // 更新余额到状态\n                get().updateWalletBalances(balanceMap);\n                // 持久化到本地存储\n                app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].setItem(\"WALLET_LIST\", JSON.stringify(get().walletList));\n                console.log(\"余额更新完成\");\n            } catch (error) {\n                console.error(\"获取余额失败:\", error);\n            }\n        },\n        updateWalletBalances: (balances)=>set((state)=>{\n                const updated = state.walletList.map((wallet)=>({\n                        ...wallet,\n                        accounts: wallet.accounts.map((account)=>{\n                            const balanceData = balances[account.accountId];\n                            if (!balanceData) return account;\n                            // 合并 balance 字段进每条链\n                            const updatedAccount = {\n                                ...account\n                            };\n                            for (const chain of [\n                                \"eth\",\n                                \"bsc\",\n                                \"btc\",\n                                \"solana\"\n                            ]){\n                                if (balanceData[chain] && updatedAccount[chain]) {\n                                    updatedAccount[chain] = {\n                                        ...updatedAccount[chain],\n                                        balance: balanceData[chain]\n                                    };\n                                }\n                            }\n                            return updatedAccount;\n                        })\n                    }));\n                return {\n                    walletList: updated\n                };\n            }),\n        updateWalletName: (walletId, accountId, name)=>set((state)=>{\n                const updated = state.walletList.map((wallet)=>{\n                    if (wallet.walletId === walletId) {\n                        return {\n                            ...wallet,\n                            accounts: wallet.accounts.map((account)=>{\n                                if (account.accountId === accountId) {\n                                    return {\n                                        ...account,\n                                        name: name.trim()\n                                    };\n                                }\n                                return account;\n                            })\n                        };\n                    }\n                    return wallet;\n                });\n                // 如果是当前账户，也更新当前账户信息\n                let updatedCurrentAccount = state.currentAccount;\n                if (state.currentAccount.accountId === accountId) {\n                    updatedCurrentAccount = {\n                        ...state.currentAccount,\n                        name: name.trim()\n                    };\n                }\n                // 持久化到本地存储\n                app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].setItem(\"WALLET_LIST\", JSON.stringify(updated));\n                return {\n                    walletList: updated,\n                    currentAccount: updatedCurrentAccount\n                };\n            }),\n        setCurrentAccount: (account)=>set((state)=>{\n                // 持久化当前选中的账户ID到本地存储\n                app_utils_storage__WEBPACK_IMPORTED_MODULE_2__[\"default\"].setItem(\"CURRENT_ACCOUNT_ID\", account.accountId);\n                return {\n                    currentAccount: account\n                };\n            }),\n        getTotalBalance: ()=>{\n            const walletList = get().walletList;\n            let total = 0;\n            walletList.forEach((wallet)=>{\n                wallet.accounts.forEach((account)=>{\n                    const chains = [\n                        \"eth\",\n                        \"bsc\",\n                        \"btc\",\n                        \"solana\"\n                    ];\n                    chains.forEach((chain)=>{\n                        if (account[chain]?.balance) {\n                            total += parseFloat(account[chain].balance) || 0;\n                        }\n                    });\n                });\n            });\n            return total;\n        },\n        getCurrentAccountBalance: ()=>{\n            const currentAccount = get().currentAccount;\n            if (!currentAccount) return 0;\n            let total = 0;\n            const chains = [\n                \"eth\",\n                \"bsc\",\n                \"btc\",\n                \"solana\"\n            ];\n            chains.forEach((chain)=>{\n                if (currentAccount[chain]?.balance) {\n                    total += parseFloat(currentAccount[chain].balance) || 0;\n                }\n            });\n            return total;\n        }\n    }));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/stores/walletStore.ts\n");

/***/ }),

/***/ "../../packages/app/utils/bip39.ts":
/*!*****************************************!*\
  !*** ../../packages/app/utils/bip39.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   coinType: () => (/* binding */ coinType),\n/* harmony export */   generateMnemonic: () => (/* binding */ generateMnemonic),\n/* harmony export */   generateUID: () => (/* binding */ generateUID),\n/* harmony export */   generateWallet: () => (/* binding */ generateWallet),\n/* harmony export */   generateWalletBsc: () => (/* binding */ generateWalletBsc),\n/* harmony export */   generateWalletBtc: () => (/* binding */ generateWalletBtc),\n/* harmony export */   generateWalletEth: () => (/* binding */ generateWalletEth),\n/* harmony export */   generateWalletSolana: () => (/* binding */ generateWalletSolana)\n/* harmony export */ });\n/* harmony import */ var bip39__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bip39 */ \"bip39\");\n/* harmony import */ var bip39__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bip39__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"ethers\");\n/* harmony import */ var _scure_bip32__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @scure/bip32 */ \"@scure/bip32\");\n/* harmony import */ var bitcoinjs_lib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! bitcoinjs-lib */ \"bitcoinjs-lib\");\n/* harmony import */ var bitcoinjs_lib__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(bitcoinjs_lib__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _scure_bip39__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @scure/bip39 */ \"@scure/bip39\");\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @solana/web3.js */ \"@solana/web3.js\");\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_solana_web3_js__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var wif__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! wif */ \"wif\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([ethers__WEBPACK_IMPORTED_MODULE_1__, _scure_bip32__WEBPACK_IMPORTED_MODULE_2__, _scure_bip39__WEBPACK_IMPORTED_MODULE_4__, wif__WEBPACK_IMPORTED_MODULE_6__]);\n([ethers__WEBPACK_IMPORTED_MODULE_1__, _scure_bip32__WEBPACK_IMPORTED_MODULE_2__, _scure_bip39__WEBPACK_IMPORTED_MODULE_4__, wif__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction generateUID() {\n    return (0,ethers__WEBPACK_IMPORTED_MODULE_1__.uuidV4)((0,ethers__WEBPACK_IMPORTED_MODULE_1__.randomBytes)(16));\n}\nfunction generateMnemonic() {\n    return bip39__WEBPACK_IMPORTED_MODULE_0__.generateMnemonic(128);\n}\nconst coinType = {\n    btc: \"0\",\n    eth: \"60\",\n    bsc: \"60\",\n    solana: \"501\"\n};\nfunction generateWallet(mnemonic, coinType, index = 0) {\n    const path = `m/44'/${coinType}'/0'/0/${index}`;\n    // const path = `m/44'/501'/0'/0'`\n    console.log(path);\n    const wallet = ethers__WEBPACK_IMPORTED_MODULE_1__.HDNodeWallet.fromPhrase(mnemonic, path);\n    // const wallet = hdNode.derivePath(); // ETH/BSC 标准路径\n    console.log(\"ETH/BSC 地址:\", wallet.address);\n    console.log(\"私钥:\", wallet.privateKey);\n    return {\n        uid: generateUID(),\n        mnemonic,\n        address: wallet.address,\n        privateKey: wallet.privateKey\n    };\n}\nfunction generateWalletEth(mnemonic, index = 0) {\n    const account = generateWallet(mnemonic, coinType.eth, index);\n    return {\n        ...account,\n        accountType: \"eth\"\n    };\n}\nfunction generateWalletBsc(mnemonic, index = 0) {\n    const account = generateWallet(mnemonic, coinType.bsc, index);\n    return {\n        ...account,\n        accountType: \"bsc\"\n    };\n}\nasync function generateWalletBtc(mnemonic, index = 0) {\n    // const mnemonic = generateMnemonicBtc(wordlist)\n    const seed = await (0,_scure_bip39__WEBPACK_IMPORTED_MODULE_4__.mnemonicToSeed)(mnemonic);\n    const root = _scure_bip32__WEBPACK_IMPORTED_MODULE_2__.HDKey.fromMasterSeed(seed);\n    const path = `m/44'/0'/0'/0/${index}`;\n    const child = root.derive(path);\n    // const network = bitcoin.networks.bitcoin\n    const network = bitcoinjs_lib__WEBPACK_IMPORTED_MODULE_3__.networks.testnet;\n    // @ts-ignore\n    const { address } = bitcoinjs_lib__WEBPACK_IMPORTED_MODULE_3__.payments.p2pkh({\n        pubkey: Buffer.from(child.publicKey),\n        network: network\n    });\n    const wifKey = wif__WEBPACK_IMPORTED_MODULE_6__.encode({\n        // version: 0x80, // \n        version: 0xEF,\n        privateKey: Buffer.from(child.privateKey),\n        compressed: true\n    });\n    return {\n        uid: generateUID(),\n        mnemonic,\n        address,\n        privateKey: Buffer.from(child.privateKey).toString(\"hex\"),\n        wif: wifKey,\n        accountType: \"btc\"\n    };\n}\nasync function generateWalletSolana(mnemonic, index = 0) {\n    const seed = await (0,_scure_bip39__WEBPACK_IMPORTED_MODULE_4__.mnemonicToSeed)(mnemonic);\n    const root = _scure_bip32__WEBPACK_IMPORTED_MODULE_2__.HDKey.fromMasterSeed(seed);\n    const path = `m/44'/501'/0'/0'/${index}`;\n    const child = root.derive(path);\n    // Solana 只用前32字节作为私钥\n    const keypair = _solana_web3_js__WEBPACK_IMPORTED_MODULE_5__.Keypair.fromSeed(child.privateKey.subarray(0, 32));\n    const address = keypair.publicKey.toBase58();\n    return {\n        uid: generateUID(),\n        mnemonic,\n        address,\n        privateKey: Buffer.from(keypair.secretKey).toString(\"hex\"),\n        accountType: \"solana\"\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/utils/bip39.ts\n");

/***/ }),

/***/ "../../packages/app/utils/constants.ts":
/*!*********************************************!*\
  !*** ../../packages/app/utils/constants.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BSC_RPC: () => (/* binding */ BSC_RPC),\n/* harmony export */   BSC_RPC_BACKUP: () => (/* binding */ BSC_RPC_BACKUP),\n/* harmony export */   ETH_RPC: () => (/* binding */ ETH_RPC),\n/* harmony export */   ETH_RPC_BACKUP: () => (/* binding */ ETH_RPC_BACKUP),\n/* harmony export */   PASSWORD_MIN_LENGTH: () => (/* binding */ PASSWORD_MIN_LENGTH),\n/* harmony export */   PASSWORD_VAULT: () => (/* binding */ PASSWORD_VAULT),\n/* harmony export */   SOLANA_RPC: () => (/* binding */ SOLANA_RPC),\n/* harmony export */   SOLANA_RPC_BACKUP: () => (/* binding */ SOLANA_RPC_BACKUP),\n/* harmony export */   TIXIAN_URL: () => (/* binding */ TIXIAN_URL),\n/* harmony export */   WALLET_LIST: () => (/* binding */ WALLET_LIST)\n/* harmony export */ });\nconst PASSWORD_MIN_LENGTH = 8;\nconst PASSWORD_VAULT = \"PASSWORD_VAULT\";\nconst WALLET_LIST = \"WALLET_LIST\";\n// 使用更稳定的RPC节点\nconst ETH_RPC = \"https://ethereum-rpc.publicnode.com\";\nconst BSC_RPC = \"https://bsc-testnet-rpc.publicnode.com\";\nconst SOLANA_RPC = \"https://api.devnet.solana.com\";\n// 备用RPC节点\nconst ETH_RPC_BACKUP = \"https://eth.llamarpc.com\";\nconst BSC_RPC_BACKUP = \"https://bsc-testnet.drpc.org\";\nconst SOLANA_RPC_BACKUP = \"https://api.testnet.solana.com\";\n// 提现地址\nconst TIXIAN_URL = \"https://buy.onramper.com/?apiKey=pk_prod_01HWQY38G07M0VGP4EP17Z6TXT&mode=sell&onlyOfframps=alchemypay&sell_defaultCrypto=USDC_ETHEREUM\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL3V0aWxzL2NvbnN0YW50cy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQU8sTUFBTUEsc0JBQXNCLEVBQUM7QUFFN0IsTUFBTUMsaUJBQWlCLGlCQUFnQjtBQUV2QyxNQUFNQyxjQUFjLGNBQWE7QUFFeEMsY0FBYztBQUNQLE1BQU1DLFVBQVUsc0NBQXFDO0FBQ3JELE1BQU1DLFVBQVUseUNBQXdDO0FBQ3hELE1BQU1DLGFBQWEsZ0NBQStCO0FBRXpELFVBQVU7QUFDSCxNQUFNQyxpQkFBaUIsMkJBQTBCO0FBQ2pELE1BQU1DLGlCQUFpQiwrQkFBOEI7QUFDckQsTUFBTUMsb0JBQW9CLGlDQUFnQztBQUVqRSxPQUFPO0FBQ0EsTUFBTUMsYUFBYSx5SUFBd0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy9hcHAvdXRpbHMvY29uc3RhbnRzLnRzPzkyOGEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFBBU1NXT1JEX01JTl9MRU5HVEggPSA4XG5cbmV4cG9ydCBjb25zdCBQQVNTV09SRF9WQVVMVCA9ICdQQVNTV09SRF9WQVVMVCdcblxuZXhwb3J0IGNvbnN0IFdBTExFVF9MSVNUID0gJ1dBTExFVF9MSVNUJ1xuXG4vLyDkvb/nlKjmm7TnqLPlrprnmoRSUEPoioLngrlcbmV4cG9ydCBjb25zdCBFVEhfUlBDID0gJ2h0dHBzOi8vZXRoZXJldW0tcnBjLnB1YmxpY25vZGUuY29tJ1xuZXhwb3J0IGNvbnN0IEJTQ19SUEMgPSAnaHR0cHM6Ly9ic2MtdGVzdG5ldC1ycGMucHVibGljbm9kZS5jb20nXG5leHBvcnQgY29uc3QgU09MQU5BX1JQQyA9ICdodHRwczovL2FwaS5kZXZuZXQuc29sYW5hLmNvbSdcblxuLy8g5aSH55SoUlBD6IqC54K5XG5leHBvcnQgY29uc3QgRVRIX1JQQ19CQUNLVVAgPSAnaHR0cHM6Ly9ldGgubGxhbWFycGMuY29tJ1xuZXhwb3J0IGNvbnN0IEJTQ19SUENfQkFDS1VQID0gJ2h0dHBzOi8vYnNjLXRlc3RuZXQuZHJwYy5vcmcnXG5leHBvcnQgY29uc3QgU09MQU5BX1JQQ19CQUNLVVAgPSAnaHR0cHM6Ly9hcGkudGVzdG5ldC5zb2xhbmEuY29tJ1xuXG4vLyDmj5DnjrDlnLDlnYBcbmV4cG9ydCBjb25zdCBUSVhJQU5fVVJMID0gJ2h0dHBzOi8vYnV5Lm9ucmFtcGVyLmNvbS8/YXBpS2V5PXBrX3Byb2RfMDFIV1FZMzhHMDdNMFZHUDRFUDE3WjZUWFQmbW9kZT1zZWxsJm9ubHlPZmZyYW1wcz1hbGNoZW15cGF5JnNlbGxfZGVmYXVsdENyeXB0bz1VU0RDX0VUSEVSRVVNJ1xuIl0sIm5hbWVzIjpbIlBBU1NXT1JEX01JTl9MRU5HVEgiLCJQQVNTV09SRF9WQVVMVCIsIldBTExFVF9MSVNUIiwiRVRIX1JQQyIsIkJTQ19SUEMiLCJTT0xBTkFfUlBDIiwiRVRIX1JQQ19CQUNLVVAiLCJCU0NfUlBDX0JBQ0tVUCIsIlNPTEFOQV9SUENfQkFDS1VQIiwiVElYSUFOX1VSTCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/app/utils/constants.ts\n");

/***/ }),

/***/ "../../packages/app/utils/networkManager.ts":
/*!**************************************************!*\
  !*** ../../packages/app/utils/networkManager.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkNetworkHealth: () => (/* binding */ checkNetworkHealth),\n/* harmony export */   networkManager: () => (/* binding */ networkManager),\n/* harmony export */   requestWithRetry: () => (/* binding */ requestWithRetry),\n/* harmony export */   startHealthCheck: () => (/* binding */ startHealthCheck),\n/* harmony export */   stopHealthCheck: () => (/* binding */ stopHealthCheck)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"../../packages/app/utils/constants.ts\");\n\n// 网络状态管理\nclass NetworkManager {\n    // 获取可用的RPC节点\n    getAvailableRpc(chain) {\n        const rpcMap = {\n            eth: [\n                _constants__WEBPACK_IMPORTED_MODULE_0__.ETH_RPC,\n                _constants__WEBPACK_IMPORTED_MODULE_0__.ETH_RPC_BACKUP\n            ],\n            bsc: [\n                _constants__WEBPACK_IMPORTED_MODULE_0__.BSC_RPC,\n                _constants__WEBPACK_IMPORTED_MODULE_0__.BSC_RPC_BACKUP\n            ],\n            solana: [\n                _constants__WEBPACK_IMPORTED_MODULE_0__.SOLANA_RPC,\n                _constants__WEBPACK_IMPORTED_MODULE_0__.SOLANA_RPC_BACKUP\n            ]\n        };\n        const rpcs = rpcMap[chain];\n        for (const rpc of rpcs){\n            if (!this.failedNodes.has(rpc)) {\n                return rpc;\n            }\n        }\n        // 如果所有节点都失败了，重置失败状态并返回主节点\n        this.resetFailedNodes(chain);\n        return rpcs[0];\n    }\n    // 标记节点失败\n    markNodeFailed(rpc) {\n        this.failedNodes.add(rpc);\n        const count = this.retryCount.get(rpc) || 0;\n        this.retryCount.set(rpc, count + 1);\n        // 设置定时器恢复节点状态\n        setTimeout(()=>{\n            this.failedNodes.delete(rpc);\n            this.retryCount.delete(rpc);\n        }, this.retryDelay * Math.pow(2, count)) // 指数退避\n        ;\n    }\n    // 重置特定链的失败节点\n    resetFailedNodes(chain) {\n        const rpcMap = {\n            eth: [\n                _constants__WEBPACK_IMPORTED_MODULE_0__.ETH_RPC,\n                _constants__WEBPACK_IMPORTED_MODULE_0__.ETH_RPC_BACKUP\n            ],\n            bsc: [\n                _constants__WEBPACK_IMPORTED_MODULE_0__.BSC_RPC,\n                _constants__WEBPACK_IMPORTED_MODULE_0__.BSC_RPC_BACKUP\n            ],\n            solana: [\n                _constants__WEBPACK_IMPORTED_MODULE_0__.SOLANA_RPC,\n                _constants__WEBPACK_IMPORTED_MODULE_0__.SOLANA_RPC_BACKUP\n            ]\n        };\n        rpcMap[chain].forEach((rpc)=>{\n            this.failedNodes.delete(rpc);\n            this.retryCount.delete(rpc);\n        });\n    }\n    // 检查节点是否可用\n    isNodeAvailable(rpc) {\n        return !this.failedNodes.has(rpc);\n    }\n    // 获取网络状态\n    getNetworkStatus() {\n        return {\n            failedNodes: Array.from(this.failedNodes),\n            retryCount: Object.fromEntries(this.retryCount),\n            totalFailedNodes: this.failedNodes.size\n        };\n    }\n    // 重置所有网络状态\n    reset() {\n        this.failedNodes.clear();\n        this.retryCount.clear();\n    }\n    constructor(){\n        this.failedNodes = new Set();\n        this.retryCount = new Map();\n        this.maxRetries = 3;\n        this.retryDelay = 1000 // 1秒\n        ;\n    }\n}\n// 单例实例\nconst networkManager = new NetworkManager();\n// 带重试的请求函数\nconst requestWithRetry = async (requestFn, rpc, maxRetries = 3)=>{\n    let lastError;\n    for(let i = 0; i < maxRetries; i++){\n        try {\n            const result = await requestFn();\n            return result;\n        } catch (error) {\n            lastError = error;\n            console.warn(`Request failed (attempt ${i + 1}/${maxRetries}) for ${rpc}:`, error.message);\n            if (i === maxRetries - 1) {\n                // 最后一次重试失败，标记节点失败\n                networkManager.markNodeFailed(rpc);\n                break;\n            }\n            // 等待后重试\n            await new Promise((resolve)=>setTimeout(resolve, 1000 * (i + 1)));\n        }\n    }\n    throw lastError;\n};\n// 网络健康检查\nconst checkNetworkHealth = async ()=>{\n    const chains = [\n        \"eth\",\n        \"bsc\",\n        \"solana\"\n    ];\n    const results = {};\n    for (const chain of chains){\n        const rpc = networkManager.getAvailableRpc(chain);\n        try {\n            const start = Date.now();\n            if (chain === \"solana\") {\n                // Solana健康检查\n                const response = await fetch(rpc, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        jsonrpc: \"2.0\",\n                        id: 1,\n                        method: \"getHealth\"\n                    })\n                });\n                await response.json();\n            } else {\n                // EVM链健康检查\n                const response = await fetch(rpc, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        jsonrpc: \"2.0\",\n                        id: 1,\n                        method: \"eth_blockNumber\",\n                        params: []\n                    })\n                });\n                await response.json();\n            }\n            const latency = Date.now() - start;\n            results[chain] = {\n                status: \"healthy\",\n                latency,\n                rpc\n            };\n        } catch (error) {\n            results[chain] = {\n                status: \"unhealthy\",\n                error: error.message,\n                rpc\n            };\n            networkManager.markNodeFailed(rpc);\n        }\n    }\n    return results;\n};\n// 定期健康检查\nlet healthCheckInterval = null;\nconst startHealthCheck = (intervalMs = 60000)=>{\n    if (healthCheckInterval) {\n        clearInterval(healthCheckInterval);\n    }\n    healthCheckInterval = setInterval(async ()=>{\n        try {\n            const health = await checkNetworkHealth();\n            console.log(\"Network health check:\", health);\n        } catch (error) {\n            console.error(\"Health check failed:\", error);\n        }\n    }, intervalMs);\n};\nconst stopHealthCheck = ()=>{\n    if (healthCheckInterval) {\n        clearInterval(healthCheckInterval);\n        healthCheckInterval = null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL3V0aWxzL25ldHdvcmtNYW5hZ2VyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE2RztBQUU3RyxTQUFTO0FBQ1QsTUFBTU07SUFNSixhQUFhO0lBQ2JDLGdCQUFnQkMsS0FBK0IsRUFBVTtRQUN2RCxNQUFNQyxTQUFTO1lBQ2JDLEtBQUs7Z0JBQUNWLCtDQUFPQTtnQkFBRUcsc0RBQWNBO2FBQUM7WUFDOUJRLEtBQUs7Z0JBQUNWLCtDQUFPQTtnQkFBRUcsc0RBQWNBO2FBQUM7WUFDOUJRLFFBQVE7Z0JBQUNWLGtEQUFVQTtnQkFBRUcseURBQWlCQTthQUFDO1FBQ3pDO1FBRUEsTUFBTVEsT0FBT0osTUFBTSxDQUFDRCxNQUFNO1FBQzFCLEtBQUssTUFBTU0sT0FBT0QsS0FBTTtZQUN0QixJQUFJLENBQUMsSUFBSSxDQUFDRSxXQUFXLENBQUNDLEdBQUcsQ0FBQ0YsTUFBTTtnQkFDOUIsT0FBT0E7WUFDVDtRQUNGO1FBRUEsMEJBQTBCO1FBQzFCLElBQUksQ0FBQ0csZ0JBQWdCLENBQUNUO1FBQ3RCLE9BQU9LLElBQUksQ0FBQyxFQUFFO0lBQ2hCO0lBRUEsU0FBUztJQUNUSyxlQUFlSixHQUFXLEVBQUU7UUFDMUIsSUFBSSxDQUFDQyxXQUFXLENBQUNJLEdBQUcsQ0FBQ0w7UUFDckIsTUFBTU0sUUFBUSxJQUFJLENBQUNDLFVBQVUsQ0FBQ0MsR0FBRyxDQUFDUixRQUFRO1FBQzFDLElBQUksQ0FBQ08sVUFBVSxDQUFDRSxHQUFHLENBQUNULEtBQUtNLFFBQVE7UUFFakMsY0FBYztRQUNkSSxXQUFXO1lBQ1QsSUFBSSxDQUFDVCxXQUFXLENBQUNVLE1BQU0sQ0FBQ1g7WUFDeEIsSUFBSSxDQUFDTyxVQUFVLENBQUNJLE1BQU0sQ0FBQ1g7UUFDekIsR0FBRyxJQUFJLENBQUNZLFVBQVUsR0FBR0MsS0FBS0MsR0FBRyxDQUFDLEdBQUdSLFFBQVEsT0FBTzs7SUFDbEQ7SUFFQSxhQUFhO0lBQ0xILGlCQUFpQlQsS0FBK0IsRUFBRTtRQUN4RCxNQUFNQyxTQUFTO1lBQ2JDLEtBQUs7Z0JBQUNWLCtDQUFPQTtnQkFBRUcsc0RBQWNBO2FBQUM7WUFDOUJRLEtBQUs7Z0JBQUNWLCtDQUFPQTtnQkFBRUcsc0RBQWNBO2FBQUM7WUFDOUJRLFFBQVE7Z0JBQUNWLGtEQUFVQTtnQkFBRUcseURBQWlCQTthQUFDO1FBQ3pDO1FBRUFJLE1BQU0sQ0FBQ0QsTUFBTSxDQUFDcUIsT0FBTyxDQUFDZixDQUFBQTtZQUNwQixJQUFJLENBQUNDLFdBQVcsQ0FBQ1UsTUFBTSxDQUFDWDtZQUN4QixJQUFJLENBQUNPLFVBQVUsQ0FBQ0ksTUFBTSxDQUFDWDtRQUN6QjtJQUNGO0lBRUEsV0FBVztJQUNYZ0IsZ0JBQWdCaEIsR0FBVyxFQUFXO1FBQ3BDLE9BQU8sQ0FBQyxJQUFJLENBQUNDLFdBQVcsQ0FBQ0MsR0FBRyxDQUFDRjtJQUMvQjtJQUVBLFNBQVM7SUFDVGlCLG1CQUFtQjtRQUNqQixPQUFPO1lBQ0xoQixhQUFhaUIsTUFBTUMsSUFBSSxDQUFDLElBQUksQ0FBQ2xCLFdBQVc7WUFDeENNLFlBQVlhLE9BQU9DLFdBQVcsQ0FBQyxJQUFJLENBQUNkLFVBQVU7WUFDOUNlLGtCQUFrQixJQUFJLENBQUNyQixXQUFXLENBQUNzQixJQUFJO1FBQ3pDO0lBQ0Y7SUFFQSxXQUFXO0lBQ1hDLFFBQVE7UUFDTixJQUFJLENBQUN2QixXQUFXLENBQUN3QixLQUFLO1FBQ3RCLElBQUksQ0FBQ2xCLFVBQVUsQ0FBQ2tCLEtBQUs7SUFDdkI7O2FBdEVReEIsY0FBYyxJQUFJeUI7YUFDbEJuQixhQUFhLElBQUlvQjthQUNqQkMsYUFBYTthQUNiaEIsYUFBYSxLQUFLLEtBQUs7OztBQW9FakM7QUFFQSxPQUFPO0FBQ0EsTUFBTWlCLGlCQUFpQixJQUFJckMsaUJBQWdCO0FBRWxELFdBQVc7QUFDSixNQUFNc0MsbUJBQW1CLE9BQzlCQyxXQUNBL0IsS0FDQTRCLGFBQWEsQ0FBQztJQUVkLElBQUlJO0lBRUosSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUlMLFlBQVlLLElBQUs7UUFDbkMsSUFBSTtZQUNGLE1BQU1DLFNBQVMsTUFBTUg7WUFDckIsT0FBT0c7UUFDVCxFQUFFLE9BQU9DLE9BQU87WUFDZEgsWUFBWUc7WUFDWkMsUUFBUUMsSUFBSSxDQUFDLENBQUMsd0JBQXdCLEVBQUVKLElBQUksRUFBRSxDQUFDLEVBQUVMLFdBQVcsTUFBTSxFQUFFNUIsSUFBSSxDQUFDLENBQUMsRUFBRW1DLE1BQU1HLE9BQU87WUFFekYsSUFBSUwsTUFBTUwsYUFBYSxHQUFHO2dCQUN4QixrQkFBa0I7Z0JBQ2xCQyxlQUFlekIsY0FBYyxDQUFDSjtnQkFDOUI7WUFDRjtZQUVBLFFBQVE7WUFDUixNQUFNLElBQUl1QyxRQUFRQyxDQUFBQSxVQUFXOUIsV0FBVzhCLFNBQVMsT0FBUVAsQ0FBQUEsSUFBSTtRQUMvRDtJQUNGO0lBRUEsTUFBTUQ7QUFDUixFQUFDO0FBRUQsU0FBUztBQUNGLE1BQU1TLHFCQUFxQjtJQUNoQyxNQUFNQyxTQUFTO1FBQUM7UUFBTztRQUFPO0tBQVM7SUFDdkMsTUFBTUMsVUFBVSxDQUFDO0lBRWpCLEtBQUssTUFBTWpELFNBQVNnRCxPQUFRO1FBQzFCLE1BQU0xQyxNQUFNNkIsZUFBZXBDLGVBQWUsQ0FBQ0M7UUFDM0MsSUFBSTtZQUNGLE1BQU1rRCxRQUFRQyxLQUFLQyxHQUFHO1lBRXRCLElBQUlwRCxVQUFVLFVBQVU7Z0JBQ3RCLGFBQWE7Z0JBQ2IsTUFBTXFELFdBQVcsTUFBTUMsTUFBTWhELEtBQUs7b0JBQ2hDaUQsUUFBUTtvQkFDUkMsU0FBUzt3QkFBRSxnQkFBZ0I7b0JBQW1CO29CQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQkMsU0FBUzt3QkFDVEMsSUFBSTt3QkFDSk4sUUFBUTtvQkFDVjtnQkFDRjtnQkFDQSxNQUFNRixTQUFTUyxJQUFJO1lBQ3JCLE9BQU87Z0JBQ0wsV0FBVztnQkFDWCxNQUFNVCxXQUFXLE1BQU1DLE1BQU1oRCxLQUFLO29CQUNoQ2lELFFBQVE7b0JBQ1JDLFNBQVM7d0JBQUUsZ0JBQWdCO29CQUFtQjtvQkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFDbkJDLFNBQVM7d0JBQ1RDLElBQUk7d0JBQ0pOLFFBQVE7d0JBQ1JRLFFBQVEsRUFBRTtvQkFDWjtnQkFDRjtnQkFDQSxNQUFNVixTQUFTUyxJQUFJO1lBQ3JCO1lBRUEsTUFBTUUsVUFBVWIsS0FBS0MsR0FBRyxLQUFLRjtZQUM3QkQsT0FBTyxDQUFDakQsTUFBTSxHQUFHO2dCQUFFaUUsUUFBUTtnQkFBV0Q7Z0JBQVMxRDtZQUFJO1FBQ3JELEVBQUUsT0FBT21DLE9BQU87WUFDZFEsT0FBTyxDQUFDakQsTUFBTSxHQUFHO2dCQUFFaUUsUUFBUTtnQkFBYXhCLE9BQU9BLE1BQU1HLE9BQU87Z0JBQUV0QztZQUFJO1lBQ2xFNkIsZUFBZXpCLGNBQWMsQ0FBQ0o7UUFDaEM7SUFDRjtJQUVBLE9BQU8yQztBQUNULEVBQUM7QUFFRCxTQUFTO0FBQ1QsSUFBSWlCLHNCQUE2QztBQUUxQyxNQUFNQyxtQkFBbUIsQ0FBQ0MsYUFBYSxLQUFLO0lBQ2pELElBQUlGLHFCQUFxQjtRQUN2QkcsY0FBY0g7SUFDaEI7SUFFQUEsc0JBQXNCSSxZQUFZO1FBQ2hDLElBQUk7WUFDRixNQUFNQyxTQUFTLE1BQU14QjtZQUNyQkwsUUFBUThCLEdBQUcsQ0FBQyx5QkFBeUJEO1FBQ3ZDLEVBQUUsT0FBTzlCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHdCQUF3QkE7UUFDeEM7SUFDRixHQUFHMkI7QUFDTCxFQUFDO0FBRU0sTUFBTUssa0JBQWtCO0lBQzdCLElBQUlQLHFCQUFxQjtRQUN2QkcsY0FBY0g7UUFDZEEsc0JBQXNCO0lBQ3hCO0FBQ0YsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3BhY2thZ2VzL2FwcC91dGlscy9uZXR3b3JrTWFuYWdlci50cz8xZjM1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEVUSF9SUEMsIEJTQ19SUEMsIFNPTEFOQV9SUEMsIEVUSF9SUENfQkFDS1VQLCBCU0NfUlBDX0JBQ0tVUCwgU09MQU5BX1JQQ19CQUNLVVAgfSBmcm9tICcuL2NvbnN0YW50cydcblxuLy8g572R57uc54q25oCB566h55CGXG5jbGFzcyBOZXR3b3JrTWFuYWdlciB7XG4gIHByaXZhdGUgZmFpbGVkTm9kZXMgPSBuZXcgU2V0PHN0cmluZz4oKVxuICBwcml2YXRlIHJldHJ5Q291bnQgPSBuZXcgTWFwPHN0cmluZywgbnVtYmVyPigpXG4gIHByaXZhdGUgbWF4UmV0cmllcyA9IDNcbiAgcHJpdmF0ZSByZXRyeURlbGF5ID0gMTAwMCAvLyAx56eSXG5cbiAgLy8g6I635Y+W5Y+v55So55qEUlBD6IqC54K5XG4gIGdldEF2YWlsYWJsZVJwYyhjaGFpbjogJ2V0aCcgfCAnYnNjJyB8ICdzb2xhbmEnKTogc3RyaW5nIHtcbiAgICBjb25zdCBycGNNYXAgPSB7XG4gICAgICBldGg6IFtFVEhfUlBDLCBFVEhfUlBDX0JBQ0tVUF0sXG4gICAgICBic2M6IFtCU0NfUlBDLCBCU0NfUlBDX0JBQ0tVUF0sXG4gICAgICBzb2xhbmE6IFtTT0xBTkFfUlBDLCBTT0xBTkFfUlBDX0JBQ0tVUF1cbiAgICB9XG5cbiAgICBjb25zdCBycGNzID0gcnBjTWFwW2NoYWluXVxuICAgIGZvciAoY29uc3QgcnBjIG9mIHJwY3MpIHtcbiAgICAgIGlmICghdGhpcy5mYWlsZWROb2Rlcy5oYXMocnBjKSkge1xuICAgICAgICByZXR1cm4gcnBjXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g5aaC5p6c5omA5pyJ6IqC54K56YO95aSx6LSl5LqG77yM6YeN572u5aSx6LSl54q25oCB5bm26L+U5Zue5Li76IqC54K5XG4gICAgdGhpcy5yZXNldEZhaWxlZE5vZGVzKGNoYWluKVxuICAgIHJldHVybiBycGNzWzBdXG4gIH1cblxuICAvLyDmoIforrDoioLngrnlpLHotKVcbiAgbWFya05vZGVGYWlsZWQocnBjOiBzdHJpbmcpIHtcbiAgICB0aGlzLmZhaWxlZE5vZGVzLmFkZChycGMpXG4gICAgY29uc3QgY291bnQgPSB0aGlzLnJldHJ5Q291bnQuZ2V0KHJwYykgfHwgMFxuICAgIHRoaXMucmV0cnlDb3VudC5zZXQocnBjLCBjb3VudCArIDEpXG5cbiAgICAvLyDorr7nva7lrprml7blmajmgaLlpI3oioLngrnnirbmgIFcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHRoaXMuZmFpbGVkTm9kZXMuZGVsZXRlKHJwYylcbiAgICAgIHRoaXMucmV0cnlDb3VudC5kZWxldGUocnBjKVxuICAgIH0sIHRoaXMucmV0cnlEZWxheSAqIE1hdGgucG93KDIsIGNvdW50KSkgLy8g5oyH5pWw6YCA6YG/XG4gIH1cblxuICAvLyDph43nva7nibnlrprpk77nmoTlpLHotKXoioLngrlcbiAgcHJpdmF0ZSByZXNldEZhaWxlZE5vZGVzKGNoYWluOiAnZXRoJyB8ICdic2MnIHwgJ3NvbGFuYScpIHtcbiAgICBjb25zdCBycGNNYXAgPSB7XG4gICAgICBldGg6IFtFVEhfUlBDLCBFVEhfUlBDX0JBQ0tVUF0sXG4gICAgICBic2M6IFtCU0NfUlBDLCBCU0NfUlBDX0JBQ0tVUF0sXG4gICAgICBzb2xhbmE6IFtTT0xBTkFfUlBDLCBTT0xBTkFfUlBDX0JBQ0tVUF1cbiAgICB9XG5cbiAgICBycGNNYXBbY2hhaW5dLmZvckVhY2gocnBjID0+IHtcbiAgICAgIHRoaXMuZmFpbGVkTm9kZXMuZGVsZXRlKHJwYylcbiAgICAgIHRoaXMucmV0cnlDb3VudC5kZWxldGUocnBjKVxuICAgIH0pXG4gIH1cblxuICAvLyDmo4Dmn6XoioLngrnmmK/lkKblj6/nlKhcbiAgaXNOb2RlQXZhaWxhYmxlKHJwYzogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuICF0aGlzLmZhaWxlZE5vZGVzLmhhcyhycGMpXG4gIH1cblxuICAvLyDojrflj5bnvZHnu5znirbmgIFcbiAgZ2V0TmV0d29ya1N0YXR1cygpIHtcbiAgICByZXR1cm4ge1xuICAgICAgZmFpbGVkTm9kZXM6IEFycmF5LmZyb20odGhpcy5mYWlsZWROb2RlcyksXG4gICAgICByZXRyeUNvdW50OiBPYmplY3QuZnJvbUVudHJpZXModGhpcy5yZXRyeUNvdW50KSxcbiAgICAgIHRvdGFsRmFpbGVkTm9kZXM6IHRoaXMuZmFpbGVkTm9kZXMuc2l6ZVxuICAgIH1cbiAgfVxuXG4gIC8vIOmHjee9ruaJgOaciee9kee7nOeKtuaAgVxuICByZXNldCgpIHtcbiAgICB0aGlzLmZhaWxlZE5vZGVzLmNsZWFyKClcbiAgICB0aGlzLnJldHJ5Q291bnQuY2xlYXIoKVxuICB9XG59XG5cbi8vIOWNleS+i+WunuS+i1xuZXhwb3J0IGNvbnN0IG5ldHdvcmtNYW5hZ2VyID0gbmV3IE5ldHdvcmtNYW5hZ2VyKClcblxuLy8g5bim6YeN6K+V55qE6K+35rGC5Ye95pWwXG5leHBvcnQgY29uc3QgcmVxdWVzdFdpdGhSZXRyeSA9IGFzeW5jIDxUPihcbiAgcmVxdWVzdEZuOiAoKSA9PiBQcm9taXNlPFQ+LFxuICBycGM6IHN0cmluZyxcbiAgbWF4UmV0cmllcyA9IDNcbik6IFByb21pc2U8VD4gPT4ge1xuICBsZXQgbGFzdEVycm9yOiBFcnJvclxuXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbWF4UmV0cmllczsgaSsrKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlcXVlc3RGbigpXG4gICAgICByZXR1cm4gcmVzdWx0XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGxhc3RFcnJvciA9IGVycm9yIGFzIEVycm9yXG4gICAgICBjb25zb2xlLndhcm4oYFJlcXVlc3QgZmFpbGVkIChhdHRlbXB0ICR7aSArIDF9LyR7bWF4UmV0cmllc30pIGZvciAke3JwY306YCwgZXJyb3IubWVzc2FnZSlcbiAgICAgIFxuICAgICAgaWYgKGkgPT09IG1heFJldHJpZXMgLSAxKSB7XG4gICAgICAgIC8vIOacgOWQjuS4gOasoemHjeivleWksei0pe+8jOagh+iusOiKgueCueWksei0pVxuICAgICAgICBuZXR3b3JrTWFuYWdlci5tYXJrTm9kZUZhaWxlZChycGMpXG4gICAgICAgIGJyZWFrXG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIOetieW+heWQjumHjeivlVxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMDAgKiAoaSArIDEpKSlcbiAgICB9XG4gIH1cblxuICB0aHJvdyBsYXN0RXJyb3IhXG59XG5cbi8vIOe9kee7nOWBpeW6t+ajgOafpVxuZXhwb3J0IGNvbnN0IGNoZWNrTmV0d29ya0hlYWx0aCA9IGFzeW5jICgpID0+IHtcbiAgY29uc3QgY2hhaW5zID0gWydldGgnLCAnYnNjJywgJ3NvbGFuYSddIGFzIGNvbnN0XG4gIGNvbnN0IHJlc3VsdHMgPSB7fVxuXG4gIGZvciAoY29uc3QgY2hhaW4gb2YgY2hhaW5zKSB7XG4gICAgY29uc3QgcnBjID0gbmV0d29ya01hbmFnZXIuZ2V0QXZhaWxhYmxlUnBjKGNoYWluKVxuICAgIHRyeSB7XG4gICAgICBjb25zdCBzdGFydCA9IERhdGUubm93KClcbiAgICAgIFxuICAgICAgaWYgKGNoYWluID09PSAnc29sYW5hJykge1xuICAgICAgICAvLyBTb2xhbmHlgaXlurfmo4Dmn6VcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChycGMsIHtcbiAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICBqc29ucnBjOiAnMi4wJyxcbiAgICAgICAgICAgIGlkOiAxLFxuICAgICAgICAgICAgbWV0aG9kOiAnZ2V0SGVhbHRoJ1xuICAgICAgICAgIH0pXG4gICAgICAgIH0pXG4gICAgICAgIGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gRVZN6ZO+5YGl5bq35qOA5p+lXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2gocnBjLCB7XG4gICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAganNvbnJwYzogJzIuMCcsXG4gICAgICAgICAgICBpZDogMSxcbiAgICAgICAgICAgIG1ldGhvZDogJ2V0aF9ibG9ja051bWJlcicsXG4gICAgICAgICAgICBwYXJhbXM6IFtdXG4gICAgICAgICAgfSlcbiAgICAgICAgfSlcbiAgICAgICAgYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IGxhdGVuY3kgPSBEYXRlLm5vdygpIC0gc3RhcnRcbiAgICAgIHJlc3VsdHNbY2hhaW5dID0geyBzdGF0dXM6ICdoZWFsdGh5JywgbGF0ZW5jeSwgcnBjIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcmVzdWx0c1tjaGFpbl0gPSB7IHN0YXR1czogJ3VuaGVhbHRoeScsIGVycm9yOiBlcnJvci5tZXNzYWdlLCBycGMgfVxuICAgICAgbmV0d29ya01hbmFnZXIubWFya05vZGVGYWlsZWQocnBjKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiByZXN1bHRzXG59XG5cbi8vIOWumuacn+WBpeW6t+ajgOafpVxubGV0IGhlYWx0aENoZWNrSW50ZXJ2YWw6IE5vZGVKUy5UaW1lb3V0IHwgbnVsbCA9IG51bGxcblxuZXhwb3J0IGNvbnN0IHN0YXJ0SGVhbHRoQ2hlY2sgPSAoaW50ZXJ2YWxNcyA9IDYwMDAwKSA9PiB7XG4gIGlmIChoZWFsdGhDaGVja0ludGVydmFsKSB7XG4gICAgY2xlYXJJbnRlcnZhbChoZWFsdGhDaGVja0ludGVydmFsKVxuICB9XG5cbiAgaGVhbHRoQ2hlY2tJbnRlcnZhbCA9IHNldEludGVydmFsKGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgaGVhbHRoID0gYXdhaXQgY2hlY2tOZXR3b3JrSGVhbHRoKClcbiAgICAgIGNvbnNvbGUubG9nKCdOZXR3b3JrIGhlYWx0aCBjaGVjazonLCBoZWFsdGgpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0hlYWx0aCBjaGVjayBmYWlsZWQ6JywgZXJyb3IpXG4gICAgfVxuICB9LCBpbnRlcnZhbE1zKVxufVxuXG5leHBvcnQgY29uc3Qgc3RvcEhlYWx0aENoZWNrID0gKCkgPT4ge1xuICBpZiAoaGVhbHRoQ2hlY2tJbnRlcnZhbCkge1xuICAgIGNsZWFySW50ZXJ2YWwoaGVhbHRoQ2hlY2tJbnRlcnZhbClcbiAgICBoZWFsdGhDaGVja0ludGVydmFsID0gbnVsbFxuICB9XG59XG4iXSwibmFtZXMiOlsiRVRIX1JQQyIsIkJTQ19SUEMiLCJTT0xBTkFfUlBDIiwiRVRIX1JQQ19CQUNLVVAiLCJCU0NfUlBDX0JBQ0tVUCIsIlNPTEFOQV9SUENfQkFDS1VQIiwiTmV0d29ya01hbmFnZXIiLCJnZXRBdmFpbGFibGVScGMiLCJjaGFpbiIsInJwY01hcCIsImV0aCIsImJzYyIsInNvbGFuYSIsInJwY3MiLCJycGMiLCJmYWlsZWROb2RlcyIsImhhcyIsInJlc2V0RmFpbGVkTm9kZXMiLCJtYXJrTm9kZUZhaWxlZCIsImFkZCIsImNvdW50IiwicmV0cnlDb3VudCIsImdldCIsInNldCIsInNldFRpbWVvdXQiLCJkZWxldGUiLCJyZXRyeURlbGF5IiwiTWF0aCIsInBvdyIsImZvckVhY2giLCJpc05vZGVBdmFpbGFibGUiLCJnZXROZXR3b3JrU3RhdHVzIiwiQXJyYXkiLCJmcm9tIiwiT2JqZWN0IiwiZnJvbUVudHJpZXMiLCJ0b3RhbEZhaWxlZE5vZGVzIiwic2l6ZSIsInJlc2V0IiwiY2xlYXIiLCJTZXQiLCJNYXAiLCJtYXhSZXRyaWVzIiwibmV0d29ya01hbmFnZXIiLCJyZXF1ZXN0V2l0aFJldHJ5IiwicmVxdWVzdEZuIiwibGFzdEVycm9yIiwiaSIsInJlc3VsdCIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJtZXNzYWdlIiwiUHJvbWlzZSIsInJlc29sdmUiLCJjaGVja05ldHdvcmtIZWFsdGgiLCJjaGFpbnMiLCJyZXN1bHRzIiwic3RhcnQiLCJEYXRlIiwibm93IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImpzb25ycGMiLCJpZCIsImpzb24iLCJwYXJhbXMiLCJsYXRlbmN5Iiwic3RhdHVzIiwiaGVhbHRoQ2hlY2tJbnRlcnZhbCIsInN0YXJ0SGVhbHRoQ2hlY2siLCJpbnRlcnZhbE1zIiwiY2xlYXJJbnRlcnZhbCIsInNldEludGVydmFsIiwiaGVhbHRoIiwibG9nIiwic3RvcEhlYWx0aENoZWNrIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/app/utils/networkManager.ts\n");

/***/ }),

/***/ "../../packages/app/utils/storage.ts":
/*!*******************************************!*\
  !*** ../../packages/app/utils/storage.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/cjs/index.js\");\n\n// import AsyncStorage from '@react-native-async-storage/async-storage';\nconst storage = {\n    setItem: async (key, value)=>{\n        if (react_native__WEBPACK_IMPORTED_MODULE_0__.Platform.OS === \"web\") {\n            localStorage.setItem(key, value);\n        } else {\n        // await AsyncStorage.setItem(key, value);\n        }\n    },\n    getItem: async (key)=>{\n        if (react_native__WEBPACK_IMPORTED_MODULE_0__.Platform.OS === \"web\") {\n            return localStorage.getItem(key);\n        } else {\n        // return await AsyncStorage.getItem(key);\n        }\n    },\n    removeItem: async (key)=>{\n        if (react_native__WEBPACK_IMPORTED_MODULE_0__.Platform.OS === \"web\") {\n            localStorage.removeItem(key);\n        } else {\n        // await AsyncStorage.removeItem(key);\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (storage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL3V0aWxzL3N0b3JhZ2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBd0M7QUFFeEMsd0VBQXdFO0FBRXhFLE1BQU1DLFVBQVU7SUFDZEMsU0FBUyxPQUFPQyxLQUFhQztRQUMzQixJQUFJSixrREFBUUEsQ0FBQ0ssRUFBRSxLQUFLLE9BQU87WUFDekJDLGFBQWFKLE9BQU8sQ0FBQ0MsS0FBS0M7UUFDNUIsT0FBTztRQUNMLDBDQUEwQztRQUM1QztJQUNGO0lBQ0FHLFNBQVMsT0FBT0o7UUFDZCxJQUFJSCxrREFBUUEsQ0FBQ0ssRUFBRSxLQUFLLE9BQU87WUFDekIsT0FBT0MsYUFBYUMsT0FBTyxDQUFDSjtRQUM5QixPQUFPO1FBQ0wsMENBQTBDO1FBQzVDO0lBQ0Y7SUFDQUssWUFBWSxPQUFPTDtRQUNqQixJQUFJSCxrREFBUUEsQ0FBQ0ssRUFBRSxLQUFLLE9BQU87WUFDekJDLGFBQWFFLFVBQVUsQ0FBQ0w7UUFDMUIsT0FBTztRQUNMLHNDQUFzQztRQUN4QztJQUNGO0FBQ0Y7QUFFQSxpRUFBZUYsT0FBT0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3BhY2thZ2VzL2FwcC91dGlscy9zdG9yYWdlLnRzPzRlODkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGxhdGZvcm0gfSBmcm9tICdyZWFjdC1uYXRpdmUnO1xuXG4vLyBpbXBvcnQgQXN5bmNTdG9yYWdlIGZyb20gJ0ByZWFjdC1uYXRpdmUtYXN5bmMtc3RvcmFnZS9hc3luYy1zdG9yYWdlJztcblxuY29uc3Qgc3RvcmFnZSA9IHtcbiAgc2V0SXRlbTogYXN5bmMgKGtleTogc3RyaW5nLCB2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgaWYgKFBsYXRmb3JtLk9TID09PSAnd2ViJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oa2V5LCB2YWx1ZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIGF3YWl0IEFzeW5jU3RvcmFnZS5zZXRJdGVtKGtleSwgdmFsdWUpO1xuICAgIH1cbiAgfSxcbiAgZ2V0SXRlbTogYXN5bmMgKGtleTogc3RyaW5nKSA9PiB7XG4gICAgaWYgKFBsYXRmb3JtLk9TID09PSAnd2ViJykge1xuICAgICAgcmV0dXJuIGxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIHJldHVybiBhd2FpdCBBc3luY1N0b3JhZ2UuZ2V0SXRlbShrZXkpO1xuICAgIH1cbiAgfSxcbiAgcmVtb3ZlSXRlbTogYXN5bmMgKGtleTogc3RyaW5nKSA9PiB7XG4gICAgaWYgKFBsYXRmb3JtLk9TID09PSAnd2ViJykge1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gYXdhaXQgQXN5bmNTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KTtcbiAgICB9XG4gIH0sXG59O1xuXG5leHBvcnQgZGVmYXVsdCBzdG9yYWdlOyJdLCJuYW1lcyI6WyJQbGF0Zm9ybSIsInN0b3JhZ2UiLCJzZXRJdGVtIiwia2V5IiwidmFsdWUiLCJPUyIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJyZW1vdmVJdGVtIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/app/utils/storage.ts\n");

/***/ }),

/***/ "../../packages/config/src/animations.ts":
/*!***********************************************!*\
  !*** ../../packages/config/src/animations.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animations: () => (/* binding */ animations)\n/* harmony export */ });\n/* harmony import */ var _tamagui_animations_react_native__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/animations-react-native */ \"../../node_modules/@tamagui/animations-react-native/dist/esm/index.mjs\");\n\nconst animations = (0,_tamagui_animations_react_native__WEBPACK_IMPORTED_MODULE_0__.createAnimations)({\n    \"100ms\": {\n        type: \"timing\",\n        duration: 100\n    },\n    bouncy: {\n        damping: 9,\n        mass: 0.9,\n        stiffness: 150\n    },\n    lazy: {\n        damping: 18,\n        stiffness: 50\n    },\n    medium: {\n        damping: 15,\n        stiffness: 120,\n        mass: 1\n    },\n    slow: {\n        damping: 15,\n        stiffness: 40\n    },\n    quick: {\n        damping: 20,\n        mass: 1.2,\n        stiffness: 250\n    },\n    tooltip: {\n        damping: 10,\n        mass: 0.9,\n        stiffness: 100\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy9hbmltYXRpb25zLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1FO0FBRTVELE1BQU1DLGFBQWFELGtGQUFnQkEsQ0FBQztJQUN6QyxTQUFTO1FBQ1BFLE1BQU07UUFDTkMsVUFBVTtJQUNaO0lBQ0FDLFFBQVE7UUFDTkMsU0FBUztRQUNUQyxNQUFNO1FBQ05DLFdBQVc7SUFDYjtJQUNBQyxNQUFNO1FBQ0pILFNBQVM7UUFDVEUsV0FBVztJQUNiO0lBQ0FFLFFBQVE7UUFDTkosU0FBUztRQUNURSxXQUFXO1FBQ1hELE1BQU07SUFDUjtJQUNBSSxNQUFNO1FBQ0pMLFNBQVM7UUFDVEUsV0FBVztJQUNiO0lBQ0FJLE9BQU87UUFDTE4sU0FBUztRQUNUQyxNQUFNO1FBQ05DLFdBQVc7SUFDYjtJQUNBSyxTQUFTO1FBQ1BQLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxXQUFXO0lBQ2I7QUFDRixHQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy9hbmltYXRpb25zLnRzPzRlZGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQW5pbWF0aW9ucyB9IGZyb20gJ0B0YW1hZ3VpL2FuaW1hdGlvbnMtcmVhY3QtbmF0aXZlJ1xuXG5leHBvcnQgY29uc3QgYW5pbWF0aW9ucyA9IGNyZWF0ZUFuaW1hdGlvbnMoe1xuICAnMTAwbXMnOiB7XG4gICAgdHlwZTogJ3RpbWluZycsXG4gICAgZHVyYXRpb246IDEwMCxcbiAgfSxcbiAgYm91bmN5OiB7XG4gICAgZGFtcGluZzogOSxcbiAgICBtYXNzOiAwLjksXG4gICAgc3RpZmZuZXNzOiAxNTAsXG4gIH0sXG4gIGxhenk6IHtcbiAgICBkYW1waW5nOiAxOCxcbiAgICBzdGlmZm5lc3M6IDUwLFxuICB9LFxuICBtZWRpdW06IHtcbiAgICBkYW1waW5nOiAxNSxcbiAgICBzdGlmZm5lc3M6IDEyMCxcbiAgICBtYXNzOiAxLFxuICB9LFxuICBzbG93OiB7XG4gICAgZGFtcGluZzogMTUsXG4gICAgc3RpZmZuZXNzOiA0MCxcbiAgfSxcbiAgcXVpY2s6IHtcbiAgICBkYW1waW5nOiAyMCxcbiAgICBtYXNzOiAxLjIsXG4gICAgc3RpZmZuZXNzOiAyNTAsXG4gIH0sXG4gIHRvb2x0aXA6IHtcbiAgICBkYW1waW5nOiAxMCxcbiAgICBtYXNzOiAwLjksXG4gICAgc3RpZmZuZXNzOiAxMDAsXG4gIH0sXG59KVxuIl0sIm5hbWVzIjpbImNyZWF0ZUFuaW1hdGlvbnMiLCJhbmltYXRpb25zIiwidHlwZSIsImR1cmF0aW9uIiwiYm91bmN5IiwiZGFtcGluZyIsIm1hc3MiLCJzdGlmZm5lc3MiLCJsYXp5IiwibWVkaXVtIiwic2xvdyIsInF1aWNrIiwidG9vbHRpcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/config/src/animations.ts\n");

/***/ }),

/***/ "../../packages/config/src/fonts.ts":
/*!******************************************!*\
  !*** ../../packages/config/src/fonts.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bodyFont: () => (/* binding */ bodyFont),\n/* harmony export */   headingFont: () => (/* binding */ headingFont)\n/* harmony export */ });\n/* harmony import */ var _tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tamagui/font-inter */ \"../../node_modules/@tamagui/font-inter/dist/esm/index.mjs\");\n\nconst headingFont = (0,_tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__.createInterFont)({\n    size: {\n        6: 15\n    },\n    transform: {\n        6: \"uppercase\",\n        7: \"none\"\n    },\n    weight: {\n        6: \"400\",\n        7: \"700\"\n    },\n    color: {\n        6: \"$colorFocus\",\n        7: \"$color\"\n    },\n    letterSpacing: {\n        5: 2,\n        6: 1,\n        7: 0,\n        8: -1,\n        9: -2,\n        10: -3,\n        12: -4,\n        14: -5,\n        15: -6\n    },\n    face: {\n        700: {\n            normal: \"InterBold\"\n        }\n    }\n});\nconst bodyFont = (0,_tamagui_font_inter__WEBPACK_IMPORTED_MODULE_0__.createInterFont)({\n    face: {\n        700: {\n            normal: \"InterBold\"\n        }\n    }\n}, {\n    sizeSize: (size)=>Math.round(size * 1.1),\n    sizeLineHeight: (size)=>Math.round(size * 1.1 + (size > 20 ? 10 : 10))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/config/src/fonts.ts\n");

/***/ }),

/***/ "../../packages/config/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/config/src/index.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _tamagui_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tamagui.config */ \"../../packages/config/src/tamagui.config.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_tamagui_config__WEBPACK_IMPORTED_MODULE_0__]);\n_tamagui_config__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tamagui_config__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tamagui_config__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFnQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3BhY2thZ2VzL2NvbmZpZy9zcmMvaW5kZXgudHM/ZDFlNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuL3RhbWFndWkuY29uZmlnJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/config/src/index.ts\n");

/***/ }),

/***/ "../../packages/config/src/tamagui.config.ts":
/*!***************************************************!*\
  !*** ../../packages/config/src/tamagui.config.ts ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config)\n/* harmony export */ });\n/* harmony import */ var _tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/config/v4 */ \"../../node_modules/@tamagui/config/dist/esm/v4.mjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _fonts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./fonts */ \"../../packages/config/src/fonts.ts\");\n/* harmony import */ var _animations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./animations */ \"../../packages/config/src/animations.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_0__, _tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__]);\n([tamagui__WEBPACK_IMPORTED_MODULE_0__, _tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst config = (0,tamagui__WEBPACK_IMPORTED_MODULE_0__.createTamagui)({\n    ..._tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__.defaultConfig,\n    // themes,\n    // tokens,\n    animations: _animations__WEBPACK_IMPORTED_MODULE_2__.animations,\n    fonts: {\n        body: _fonts__WEBPACK_IMPORTED_MODULE_3__.bodyFont,\n        heading: _fonts__WEBPACK_IMPORTED_MODULE_3__.headingFont\n    },\n    settings: {\n        ..._tamagui_config_v4__WEBPACK_IMPORTED_MODULE_1__.defaultConfig.settings,\n        onlyAllowShorthands: false\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy90YW1hZ3VpLmNvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFrRDtBQUNHO0FBRU47QUFDTjtBQUdsQyxNQUFNSyxTQUFTSixzREFBYUEsQ0FBQztJQUNsQyxHQUFHRCw2REFBYTtJQUNoQixVQUFVO0lBQ1YsVUFBVTtJQUNWSSxVQUFVQSxxREFBQUE7SUFDVkUsT0FBTztRQUNMQyxNQUFNTCw0Q0FBUUE7UUFDZE0sU0FBU0wsK0NBQVdBO0lBQ3RCO0lBQ0FNLFVBQVU7UUFBRSxHQUFHVCw2REFBYUEsQ0FBQ1MsUUFBUTtRQUFFQyxxQkFBcUI7SUFBTztBQUdyRSxHQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vcGFja2FnZXMvY29uZmlnL3NyYy90YW1hZ3VpLmNvbmZpZy50cz8yZDYwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRlZmF1bHRDb25maWcgfSBmcm9tICdAdGFtYWd1aS9jb25maWcvdjQnXG5pbXBvcnQgeyBjcmVhdGVUYW1hZ3VpLCBjcmVhdGVUb2tlbnMgfSBmcm9tICd0YW1hZ3VpJ1xuaW1wb3J0IHsgY29sb3IsIHJhZGl1cywgc2l6ZSwgc3BhY2UsIHRoZW1lcywgekluZGV4IH0gZnJvbSAnQHRhbWFndWkvdGhlbWVzJ1xuaW1wb3J0IHsgYm9keUZvbnQsIGhlYWRpbmdGb250IH0gZnJvbSAnLi9mb250cydcbmltcG9ydCB7IGFuaW1hdGlvbnMgfSBmcm9tICcuL2FuaW1hdGlvbnMnXG5cblxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGNyZWF0ZVRhbWFndWkoe1xuICAuLi5kZWZhdWx0Q29uZmlnLFxuICAvLyB0aGVtZXMsXG4gIC8vIHRva2VucyxcbiAgYW5pbWF0aW9ucyxcbiAgZm9udHM6IHtcbiAgICBib2R5OiBib2R5Rm9udCxcbiAgICBoZWFkaW5nOiBoZWFkaW5nRm9udCxcbiAgfSxcbiAgc2V0dGluZ3M6IHsgLi4uZGVmYXVsdENvbmZpZy5zZXR0aW5ncywgb25seUFsbG93U2hvcnRoYW5kczogZmFsc2UsIH0sXG4gIC8vIHNob3J0aGFuZHM6IHVuZGVmaW5lZFxuICBcbn0pXG5cbnR5cGUgQXBwQ29uZmlnID0gdHlwZW9mIGNvbmZpZ1xuXG5kZWNsYXJlIG1vZHVsZSAndGFtYWd1aScge1xuICBpbnRlcmZhY2UgVGFtYWd1aUN1c3RvbUNvbmZpZyBleHRlbmRzIEFwcENvbmZpZyB7fVxufSJdLCJuYW1lcyI6WyJkZWZhdWx0Q29uZmlnIiwiY3JlYXRlVGFtYWd1aSIsImJvZHlGb250IiwiaGVhZGluZ0ZvbnQiLCJhbmltYXRpb25zIiwiY29uZmlnIiwiZm9udHMiLCJib2R5IiwiaGVhZGluZyIsInNldHRpbmdzIiwib25seUFsbG93U2hvcnRoYW5kcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/config/src/tamagui.config.ts\n");

/***/ }),

/***/ "../../packages/ui/src/ConfirmButton.tsx":
/*!***********************************************!*\
  !*** ../../packages/ui/src/ConfirmButton.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfirmButton: () => (/* binding */ ConfirmButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_linear_gradient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/linear-gradient */ \"../../node_modules/@tamagui/linear-gradient/dist/esm/index.mjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_2__]);\ntamagui__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction ConfirmButton({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_linear_gradient__WEBPACK_IMPORTED_MODULE_1__.LinearGradient, {\n        colors: [\n            \"#2576FE\",\n            \"#46DFE7\"\n        ],\n        start: {\n            x: 0,\n            y: 0\n        },\n        end: {\n            x: 1,\n            y: 0\n        },\n        style: {\n            width: \"100%\",\n            height: 48,\n            borderRadius: 24,\n            justifyContent: \"center\",\n            alignItems: \"center\"\n        },\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n            \"data-at\": \"ConfirmButton.tsx:19\",\n            \"data-in\": \"ConfirmButton\",\n            \"data-is\": \"YStack\",\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/ConfirmButton.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/ConfirmButton.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL0NvbmZpcm1CdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUF5RDtBQUN6QjtBQUV6QixTQUFTRSxjQUFjLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxPQUFPO0lBQ2xELHFCQUNFLDhEQUFDSixvRUFBY0E7UUFDYkssUUFBUTtZQUFDO1lBQVc7U0FBVTtRQUM5QkMsT0FBTztZQUFFQyxHQUFHO1lBQUdDLEdBQUc7UUFBRTtRQUNwQkMsS0FBSztZQUFFRixHQUFHO1lBQUdDLEdBQUc7UUFBRTtRQUNsQkUsT0FBTztZQUNMQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsY0FBYztZQUNkQyxnQkFBZ0I7WUFDaEJDLFlBQVk7UUFDZDtRQUNBLEdBQUlYLEtBQUs7a0JBRVQsNEVBQUNILDJDQUFNQTtZQUFBZSxXQUFBO1lBQUFDLFdBQUE7WUFBQUMsV0FBQTtzQkFBRWY7Ozs7Ozs7Ozs7O0FBR2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy91aS9zcmMvQ29uZmlybUJ1dHRvbi50c3g/NjU2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMaW5lYXJHcmFkaWVudCB9IGZyb20gJ0B0YW1hZ3VpL2xpbmVhci1ncmFkaWVudCdcbmltcG9ydCB7IFlTdGFjayB9IGZyb20gJ3RhbWFndWknXG5cbmV4cG9ydCBmdW5jdGlvbiBDb25maXJtQnV0dG9uKHsgY2hpbGRyZW4sIC4uLnByb3BzIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8TGluZWFyR3JhZGllbnRcbiAgICAgIGNvbG9ycz17WycjMjU3NkZFJywgJyM0NkRGRTcnXX1cbiAgICAgIHN0YXJ0PXt7IHg6IDAsIHk6IDAgfX1cbiAgICAgIGVuZD17eyB4OiAxLCB5OiAwIH19XG4gICAgICBzdHlsZT17e1xuICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICBoZWlnaHQ6IDQ4LFxuICAgICAgICBib3JkZXJSYWRpdXM6IDI0LFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgfX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICA+XG4gICAgICA8WVN0YWNrPntjaGlsZHJlbn08L1lTdGFjaz5cbiAgICA8L0xpbmVhckdyYWRpZW50PlxuICApXG59Il0sIm5hbWVzIjpbIkxpbmVhckdyYWRpZW50IiwiWVN0YWNrIiwiQ29uZmlybUJ1dHRvbiIsImNoaWxkcmVuIiwicHJvcHMiLCJjb2xvcnMiLCJzdGFydCIsIngiLCJ5IiwiZW5kIiwic3R5bGUiLCJ3aWR0aCIsImhlaWdodCIsImJvcmRlclJhZGl1cyIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsImRhdGEtYXQiLCJkYXRhLWluIiwiZGF0YS1pcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/ui/src/ConfirmButton.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/CustomToast.tsx":
/*!*********************************************!*\
  !*** ../../packages/ui/src/CustomToast.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomToast: () => (/* binding */ CustomToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var expo_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! expo-constants */ \"../../node_modules/expo-constants/build/Constants.js\");\n/* harmony import */ var _NativeToast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./NativeToast */ \"../../packages/ui/src/NativeToast.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_NativeToast__WEBPACK_IMPORTED_MODULE_2__]);\n_NativeToast__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst isExpo = expo_constants__WEBPACK_IMPORTED_MODULE_1__[\"default\"].executionEnvironment === expo_constants__WEBPACK_IMPORTED_MODULE_1__.ExecutionEnvironment.StoreClient;\nconst CustomToast = ()=>{\n    if (isExpo) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NativeToast__WEBPACK_IMPORTED_MODULE_2__.NativeToast, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/CustomToast.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL0N1c3RvbVRvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBZ0U7QUFDWjtBQUVwRCxNQUFNSSxTQUFTSiwyRUFBOEIsS0FBS0MsZ0VBQW9CQSxDQUFDSyxXQUFXO0FBRTNFLE1BQU1DLGNBQWM7SUFDekIsSUFBSUgsUUFBUTtRQUNWLE9BQU87SUFDVDtJQUNBLHFCQUFPLDhEQUFDRCxxREFBS0E7Ozs7O0FBQ2YsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3BhY2thZ2VzL3VpL3NyYy9DdXN0b21Ub2FzdC50c3g/OGMwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQ29uc3RhbnRzLCB7IEV4ZWN1dGlvbkVudmlyb25tZW50IH0gZnJvbSAnZXhwby1jb25zdGFudHMnXG5pbXBvcnQgeyBOYXRpdmVUb2FzdCBhcyBUb2FzdCB9IGZyb20gJy4vTmF0aXZlVG9hc3QnXG5cbmNvbnN0IGlzRXhwbyA9IENvbnN0YW50cy5leGVjdXRpb25FbnZpcm9ubWVudCA9PT0gRXhlY3V0aW9uRW52aXJvbm1lbnQuU3RvcmVDbGllbnRcblxuZXhwb3J0IGNvbnN0IEN1c3RvbVRvYXN0ID0gKCkgPT4ge1xuICBpZiAoaXNFeHBvKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxuICByZXR1cm4gPFRvYXN0IC8+XG59XG4iXSwibmFtZXMiOlsiQ29uc3RhbnRzIiwiRXhlY3V0aW9uRW52aXJvbm1lbnQiLCJOYXRpdmVUb2FzdCIsIlRvYXN0IiwiaXNFeHBvIiwiZXhlY3V0aW9uRW52aXJvbm1lbnQiLCJTdG9yZUNsaWVudCIsIkN1c3RvbVRvYXN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/ui/src/CustomToast.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/Keyboard.tsx":
/*!******************************************!*\
  !*** ../../packages/ui/src/Keyboard.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeyButton: () => (/* binding */ KeyButton),\n/* harmony export */   Keyboard: () => (/* binding */ Keyboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_1__]);\ntamagui__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst KeyButton = ({ children, onPress })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        \"data-at\": \"Keyboard.tsx:10-37\",\n        \"data-in\": \"KeyButton\",\n        \"data-is\": \"Button\",\n        fontSize: 24,\n        fontWeight: 500,\n        bg: \"$color0\",\n        width: \"33.3%\",\n        flex: 1,\n        pressStyle: {\n            bg: \"$color0\",\n            borderColor: \"$color0\",\n            shadowColor: \"transparent\",\n            opacity: 1,\n            borderWidth: 0,\n            outlineWidth: 0,\n            outlineColor: \"transparent\"\n        },\n        focusStyle: {\n            outlineWidth: 0,\n            outlineColor: \"transparent\",\n            borderColor: \"$color0\"\n        },\n        hoverStyle: {\n            bg: \"$color0\",\n            borderColor: \"$color0\",\n            shadowColor: \"transparent\",\n            opacity: 1\n        },\n        onPress: onPress,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/Keyboard.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\nconst Keyboard = ({ onKeyPress })=>{\n    const keys = [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\",\n        \"5\",\n        \"6\",\n        \"7\",\n        \"8\",\n        \"9\",\n        \"0\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.YStack, {\n        \"data-at\": \"Keyboard.tsx:47\",\n        \"data-in\": \"Keyboard\",\n        \"data-is\": \"YStack\",\n        width: \"100%\",\n        gap: \"$4\",\n        children: [\n            [\n                0,\n                1,\n                2\n            ].map((row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.XStack, {\n                    \"data-at\": \"Keyboard.tsx:49\",\n                    \"data-in\": \"Keyboard\",\n                    \"data-is\": \"XStack\",\n                    gap: \"$3\",\n                    justify: \"center\",\n                    children: keys.slice(row * 3, row * 3 + 3).map((key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KeyButton, {\n                            onPress: ()=>onKeyPress(key),\n                            children: key\n                        }, key, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/Keyboard.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 56\n                        }, undefined))\n                }, row, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/Keyboard.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 29\n                }, undefined)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.XStack, {\n                \"data-at\": \"Keyboard.tsx:55\",\n                \"data-in\": \"Keyboard\",\n                \"data-is\": \"XStack\",\n                gap: \"$3\",\n                justify: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KeyButton, {\n                        onPress: ()=>onKeyPress(\".\"),\n                        children: \".\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/Keyboard.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KeyButton, {\n                        onPress: ()=>onKeyPress(\"0\"),\n                        children: \"0\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/Keyboard.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KeyButton, {\n                        onPress: ()=>onKeyPress(\"back\"),\n                        children: \"←\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/Keyboard.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/Keyboard.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/Keyboard.tsx\",\n        lineNumber: 34,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/ui/src/Keyboard.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/MyComponent.tsx":
/*!*********************************************!*\
  !*** ../../packages/ui/src/MyComponent.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MyComponent: () => (/* binding */ MyComponent)\n/* harmony export */ });\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_0__]);\ntamagui__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst MyComponent = (0,tamagui__WEBPACK_IMPORTED_MODULE_0__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_0__.YStack, {\n    name: \"MyComponent\",\n    bg: \"red\",\n    variants: {\n        blue: {\n            true: {\n                bg: \"blue\"\n            }\n        }\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL015Q29tcG9uZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3QztBQUVqQyxNQUFNRSxjQUFjRCwrQ0FBTUEsQ0FBQ0QsMkNBQU1BLEVBQUU7SUFDeENHLE1BQU07SUFDTkMsSUFBSTtJQUVKQyxVQUFVO1FBQ1JDLE1BQU07WUFDSkMsTUFBTTtnQkFDSkgsSUFBSTtZQUNOO1FBQ0Y7SUFDRjtBQUNGLEdBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy91aS9zcmMvTXlDb21wb25lbnQudHN4Pzk5NWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgWVN0YWNrLCBzdHlsZWQgfSBmcm9tICd0YW1hZ3VpJ1xuXG5leHBvcnQgY29uc3QgTXlDb21wb25lbnQgPSBzdHlsZWQoWVN0YWNrLCB7XG4gIG5hbWU6ICdNeUNvbXBvbmVudCcsXG4gIGJnOiAncmVkJyxcblxuICB2YXJpYW50czoge1xuICAgIGJsdWU6IHtcbiAgICAgIHRydWU6IHtcbiAgICAgICAgYmc6ICdibHVlJyxcbiAgICAgIH0sXG4gICAgfSxcbiAgfSBhcyBjb25zdCxcbn0pXG4iXSwibmFtZXMiOlsiWVN0YWNrIiwic3R5bGVkIiwiTXlDb21wb25lbnQiLCJuYW1lIiwiYmciLCJ2YXJpYW50cyIsImJsdWUiLCJ0cnVlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/ui/src/MyComponent.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/NativeToast.tsx":
/*!*********************************************!*\
  !*** ../../packages/ui/src/NativeToast.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NativeToast: () => (/* binding */ NativeToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tamagui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/toast */ \"../../node_modules/@tamagui/toast/dist/esm/index.mjs\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_2__]);\ntamagui__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst NativeToast = ()=>{\n    const currentToast = (0,_tamagui_toast__WEBPACK_IMPORTED_MODULE_1__.useToastState)();\n    if (!currentToast || currentToast.isHandledNatively) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n        duration: currentToast.duration,\n        viewportName: currentToast.viewportName,\n        enterStyle: {\n            opacity: 0,\n            scale: 0.5,\n            y: -25\n        },\n        exitStyle: {\n            opacity: 0,\n            scale: 1,\n            y: -20\n        },\n        y: 0,\n        opacity: 1,\n        scale: 1,\n        animation: \"quick\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n            \"data-at\": \"NativeToast.tsx:23\",\n            \"data-in\": \"NativeToast\",\n            \"data-is\": \"YStack\",\n            py: \"$1.5\",\n            px: \"$2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast.Title, {\n                    lineHeight: \"$1\",\n                    children: currentToast.title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NativeToast.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined),\n                !!currentToast.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast.Description, {\n                    children: currentToast.message\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NativeToast.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 36\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NativeToast.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, currentToast.id, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NativeToast.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL05hdGl2ZVRvYXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBcUQ7QUFDckI7QUFFekIsTUFBTUcsY0FBY0E7SUFDekIsTUFBTUMsZUFBZUgsNkRBQWFBO0lBRWxDLElBQUksQ0FBQ0csZ0JBQWdCQSxhQUFhQyxpQkFBaUIsRUFBRTtRQUNuRCxPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ0wsaURBQUtBO1FBRUpNLFVBQVVGLGFBQWFFLFFBQVE7UUFDL0JDLGNBQWNILGFBQWFHLFlBQVk7UUFDdkNDLFlBQVk7WUFBRUMsU0FBUztZQUFHQyxPQUFPO1lBQUtDLEdBQUcsQ0FBQztRQUFHO1FBQzdDQyxXQUFXO1lBQUVILFNBQVM7WUFBR0MsT0FBTztZQUFHQyxHQUFHLENBQUM7UUFBRztRQUMxQ0EsR0FBRztRQUNIRixTQUFTO1FBQ1RDLE9BQU87UUFDUEcsV0FBVTtrQkFFViw0RUFBQ1gsMkNBQU1BO1lBQUFZLFdBQUE7WUFBQUMsV0FBQTtZQUFBQyxXQUFBO1lBQUNDLElBQUc7WUFBT0MsSUFBRzs7OEJBQ25CLDhEQUFDbEIsaURBQUtBLENBQUNtQixLQUFLO29CQUFDQyxZQUFXOzhCQUFNaEIsYUFBYWlCLEtBQUs7Ozs7OztnQkFDL0MsQ0FBQyxDQUFDakIsYUFBYWtCLE9BQU8sa0JBQUksOERBQUN0QixpREFBS0EsQ0FBQ3VCLFdBQVc7OEJBQUVuQixhQUFha0IsT0FBTzs7Ozs7Ozs7Ozs7O09BWmhFbEIsYUFBYW9CLEVBQUU7Ozs7O0FBZ0IxQixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1hcHAvLi4vLi4vcGFja2FnZXMvdWkvc3JjL05hdGl2ZVRvYXN0LnRzeD85MjA4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRvYXN0LCB1c2VUb2FzdFN0YXRlIH0gZnJvbSAnQHRhbWFndWkvdG9hc3QnXG5pbXBvcnQgeyBZU3RhY2sgfSBmcm9tICd0YW1hZ3VpJ1xuXG5leHBvcnQgY29uc3QgTmF0aXZlVG9hc3QgPSAoKSA9PiB7XG4gIGNvbnN0IGN1cnJlbnRUb2FzdCA9IHVzZVRvYXN0U3RhdGUoKVxuXG4gIGlmICghY3VycmVudFRvYXN0IHx8IGN1cnJlbnRUb2FzdC5pc0hhbmRsZWROYXRpdmVseSkge1xuICAgIHJldHVybiBudWxsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxUb2FzdFxuICAgICAga2V5PXtjdXJyZW50VG9hc3QuaWR9XG4gICAgICBkdXJhdGlvbj17Y3VycmVudFRvYXN0LmR1cmF0aW9ufVxuICAgICAgdmlld3BvcnROYW1lPXtjdXJyZW50VG9hc3Qudmlld3BvcnROYW1lfVxuICAgICAgZW50ZXJTdHlsZT17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC41LCB5OiAtMjUgfX1cbiAgICAgIGV4aXRTdHlsZT17eyBvcGFjaXR5OiAwLCBzY2FsZTogMSwgeTogLTIwIH19XG4gICAgICB5PXswfVxuICAgICAgb3BhY2l0eT17MX1cbiAgICAgIHNjYWxlPXsxfVxuICAgICAgYW5pbWF0aW9uPVwicXVpY2tcIlxuICAgID5cbiAgICAgIDxZU3RhY2sgcHk9XCIkMS41XCIgcHg9XCIkMlwiPlxuICAgICAgICA8VG9hc3QuVGl0bGUgbGluZUhlaWdodD1cIiQxXCI+e2N1cnJlbnRUb2FzdC50aXRsZX08L1RvYXN0LlRpdGxlPlxuICAgICAgICB7ISFjdXJyZW50VG9hc3QubWVzc2FnZSAmJiA8VG9hc3QuRGVzY3JpcHRpb24+e2N1cnJlbnRUb2FzdC5tZXNzYWdlfTwvVG9hc3QuRGVzY3JpcHRpb24+fVxuICAgICAgPC9ZU3RhY2s+XG4gICAgPC9Ub2FzdD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlRvYXN0IiwidXNlVG9hc3RTdGF0ZSIsIllTdGFjayIsIk5hdGl2ZVRvYXN0IiwiY3VycmVudFRvYXN0IiwiaXNIYW5kbGVkTmF0aXZlbHkiLCJkdXJhdGlvbiIsInZpZXdwb3J0TmFtZSIsImVudGVyU3R5bGUiLCJvcGFjaXR5Iiwic2NhbGUiLCJ5IiwiZXhpdFN0eWxlIiwiYW5pbWF0aW9uIiwiZGF0YS1hdCIsImRhdGEtaW4iLCJkYXRhLWlzIiwicHkiLCJweCIsIlRpdGxlIiwibGluZUhlaWdodCIsInRpdGxlIiwibWVzc2FnZSIsIkRlc2NyaXB0aW9uIiwiaWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/ui/src/NativeToast.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/NavBar.tsx":
/*!****************************************!*\
  !*** ../../packages/ui/src/NavBar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavBar: () => (/* binding */ NavBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_1__]);\ntamagui__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction NavBar({ title, onBack }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.YStack, {\n        \"data-at\": \"NavBar.tsx:11\",\n        \"data-in\": \"NavBar\",\n        \"data-is\": \"YStack\",\n        height: 64,\n        justifyContent: \"center\",\n        bg: \"$background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.XStack, {\n            \"data-at\": \"NavBar.tsx:12\",\n            \"data-in\": \"NavBar\",\n            \"data-is\": \"XStack\",\n            alignItems: \"center\",\n            height: 32,\n            px: \"$2\",\n            space: \"$2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                    \"data-at\": \"NavBar.tsx:13-20\",\n                    \"data-in\": \"NavBar\",\n                    \"data-is\": \"Button\",\n                    chromeless: true,\n                    size: \"$2\",\n                    onPress: onBack,\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_2__.ArrowLeft, {\n                        size: 20\n                    }, \"navbar-arrowleft\", false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NavBar.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 122\n                    }, void 0),\n                    \"aria-label\": \"Back\",\n                    circular: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NavBar.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                    \"data-at\": \"NavBar.tsx:22\",\n                    \"data-in\": \"NavBar\",\n                    \"data-is\": \"Text\",\n                    fontSize: 16,\n                    fontWeight: \"600\",\n                    children: title || \"\"\n                }, \"navbar-title\", false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NavBar.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 10\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NavBar.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/NavBar.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/ui/src/NavBar.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/PasswordForm.tsx":
/*!**********************************************!*\
  !*** ../../packages/ui/src/PasswordForm.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PasswordForm: () => (/* binding */ PasswordForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/constants */ \"../../packages/ui/src/utils/constants.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_3__]);\ntamagui__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction PasswordForm({ onChange }) {\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmPasswordError, setConfirmPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handlePasswordChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((passwordInput)=>{\n        console.log(passwordInput);\n        const isTooShort = passwordInput.length < _utils_constants__WEBPACK_IMPORTED_MODULE_2__.PASSWORD_MIN_LENGTH;\n        if (isTooShort) {\n            setPasswordError(\"密码长度不足\");\n        } else {\n            setPasswordError(\"\");\n        }\n        if (confirmPassword.length > 0 && passwordInput !== confirmPassword) {\n            setConfirmPasswordError(\"密码不匹配\");\n        } else {\n            setConfirmPasswordError(\"\");\n        }\n        setPassword(passwordInput);\n    }, [\n        confirmPassword\n    ]);\n    const handleConfirmPasswordChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((confirmPasswordInput)=>{\n        const error = password === confirmPasswordInput || confirmPasswordInput.length === 0 ? \"\" : \"密码不匹配\";\n        setConfirmPassword(confirmPasswordInput);\n        setConfirmPasswordError(error);\n    }, [\n        password\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (password.length >= _utils_constants__WEBPACK_IMPORTED_MODULE_2__.PASSWORD_MIN_LENGTH && confirmPassword.length >= _utils_constants__WEBPACK_IMPORTED_MODULE_2__.PASSWORD_MIN_LENGTH && password === confirmPassword) {\n            onChange(password);\n        } else {\n            onChange(\"\");\n        }\n    }, [\n        password,\n        confirmPassword,\n        onChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n        \"data-at\": \"PasswordForm.tsx:64\",\n        \"data-in\": \"PasswordForm\",\n        \"data-is\": \"YStack\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n                \"data-at\": \"PasswordForm.tsx:65\",\n                \"data-in\": \"PasswordForm\",\n                \"data-is\": \"YStack\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"PasswordForm.tsx:66\",\n                        \"data-in\": \"PasswordForm\",\n                        \"data-is\": \"Text\",\n                        children: \"输入新密码\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n                        \"data-at\": \"PasswordForm.tsx:68\",\n                        \"data-in\": \"PasswordForm\",\n                        \"data-is\": \"YStack\",\n                        height: 64,\n                        position: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                \"data-at\": \"PasswordForm.tsx:69-74\",\n                                \"data-in\": \"PasswordForm\",\n                                \"data-is\": \"Input\",\n                                mt: 8,\n                                size: \"$4\",\n                                borderWidth: 2,\n                                secureTextEntry: !showPassword,\n                                onChange: (e)=>{\n                                    // @ts-ignore\n                                    handlePasswordChange(e.target.value);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                                \"data-at\": \"PasswordForm.tsx:75-79\",\n                                \"data-in\": \"PasswordForm\",\n                                \"data-is\": \"XStack\",\n                                width: 20,\n                                position: \"absolute\",\n                                left: \"auto\",\n                                right: 12,\n                                top: 20,\n                                onPress: (e)=>{\n                                    setShowPassword(!showPassword);\n                                },\n                                children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_4__.Eye, {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_4__.EyeOff, {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 49\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"PasswordForm.tsx:83\",\n                        \"data-in\": \"PasswordForm\",\n                        \"data-is\": \"Text\",\n                        fontSize: 14,\n                        mt: 12,\n                        color: \"$red8\",\n                        children: passwordError\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n                \"data-at\": \"PasswordForm.tsx:85\",\n                \"data-in\": \"PasswordForm\",\n                \"data-is\": \"YStack\",\n                mt: 24,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"PasswordForm.tsx:86\",\n                        \"data-in\": \"PasswordForm\",\n                        \"data-is\": \"Text\",\n                        children: \"确认密码\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n                        \"data-at\": \"PasswordForm.tsx:87\",\n                        \"data-in\": \"PasswordForm\",\n                        \"data-is\": \"YStack\",\n                        height: 64,\n                        position: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                \"data-at\": \"PasswordForm.tsx:88-93\",\n                                \"data-in\": \"PasswordForm\",\n                                \"data-is\": \"Input\",\n                                mt: 8,\n                                size: \"$4\",\n                                borderWidth: 2,\n                                secureTextEntry: !showConfirmPassword,\n                                onChange: (e)=>{\n                                    // @ts-ignore\n                                    handleConfirmPasswordChange(e.target.value);\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                                \"data-at\": \"PasswordForm.tsx:94-98\",\n                                \"data-in\": \"PasswordForm\",\n                                \"data-is\": \"XStack\",\n                                width: 20,\n                                position: \"absolute\",\n                                left: \"auto\",\n                                right: 12,\n                                top: 20,\n                                onPress: (e)=>{\n                                    setShowConfirmPassword(!showConfirmPassword);\n                                },\n                                children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_4__.Eye, {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 36\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_4__.EyeOff, {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 56\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"PasswordForm.tsx:103\",\n                        \"data-in\": \"PasswordForm\",\n                        \"data-is\": \"Text\",\n                        fontSize: 14,\n                        mt: 12,\n                        color: \"$red8\",\n                        children: confirmPasswordError\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordForm.tsx\",\n        lineNumber: 45,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/ui/src/PasswordForm.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/PasswordInput.tsx":
/*!***********************************************!*\
  !*** ../../packages/ui/src/PasswordInput.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PasswordInput: () => (/* binding */ PasswordInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _Keyboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Keyboard */ \"../../packages/ui/src/Keyboard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_2__, _Keyboard__WEBPACK_IMPORTED_MODULE_3__]);\n([tamagui__WEBPACK_IMPORTED_MODULE_2__, _Keyboard__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst PasswordInput = ({ onSuccess, onKeyPress })=>{\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((key)=>{\n        onKeyPress(key);\n        if (key === \"back\") {\n            setInput(input.slice(0, -1));\n        } else if (input.length < 6) {\n            setInput(input + key);\n        }\n    }, [\n        input\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (input.length === 6) {\n            onSuccess(input);\n            setTimeout(()=>{\n                setInput(\"\");\n            }, 300);\n        }\n    }, [\n        input\n    ]);\n    const keys = [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\",\n        \"5\",\n        \"6\",\n        \"7\",\n        \"8\",\n        \"9\",\n        \"0\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n        \"data-at\": \"PasswordInput.tsx:30\",\n        \"data-in\": \"PasswordInput\",\n        \"data-is\": \"YStack\",\n        items: \"center\",\n        justifyContent: \"space-between\",\n        gap: \"$4\",\n        flex: 1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                \"data-at\": \"PasswordInput.tsx:31\",\n                \"data-in\": \"PasswordInput\",\n                \"data-is\": \"XStack\",\n                gap: 12,\n                mb: \"$2\",\n                children: [\n                    ...Array(6)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Circle, {\n                            \"data-at\": \"PasswordInput.tsx:34-43\",\n                            \"data-in\": \"PasswordInput\",\n                            \"data-is\": \"Circle\",\n                            size: \"$2\",\n                            bg: i < input.length ? \"$color\" : \"$color0\",\n                            borderStyle: \"solid\",\n                            borderWidth: 1,\n                            borderColor: \"#4575FF\",\n                            enterStyle: {\n                                scale: 0\n                            },\n                            exitStyle: {\n                                scale: 0\n                            },\n                            animation: \"quick\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordInput.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, undefined)\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordInput.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 38\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordInput.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n                \"data-at\": \"PasswordInput.tsx:47\",\n                \"data-in\": \"PasswordInput\",\n                \"data-is\": \"YStack\",\n                width: \"100%\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Keyboard__WEBPACK_IMPORTED_MODULE_3__.Keyboard, {\n                        onKeyPress: handleKeyPress\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordInput.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n                        \"data-at\": \"PasswordInput.tsx:49\",\n                        \"data-in\": \"PasswordInput\",\n                        \"data-is\": \"YStack\",\n                        height: 100\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordInput.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordInput.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/PasswordInput.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/ui/src/PasswordInput.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/SwitchThemeButton.tsx":
/*!***************************************************!*\
  !*** ../../packages/ui/src/SwitchThemeButton.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SwitchThemeButton: () => (/* binding */ SwitchThemeButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tamagui/next-theme */ \"../../node_modules/@tamagui/next-theme/dist/cjs/index.js\");\n/* harmony import */ var _tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_3__]);\ntamagui__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst SwitchThemeButton = ()=>{\n    const themeSetting = (0,_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2__.useThemeSetting)();\n    const [theme] = (0,_tamagui_next_theme__WEBPACK_IMPORTED_MODULE_2__.useRootTheme)();\n    const [clientTheme, setClientTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.useIsomorphicLayoutEffect)(()=>{\n        setClientTheme(themeSetting.forcedTheme || themeSetting.current || theme);\n    }, [\n        themeSetting.current,\n        themeSetting.resolvedTheme\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        \"data-at\": \"SwitchThemeButton.tsx:15\",\n        \"data-in\": \"SwitchThemeButton\",\n        \"data-is\": \"Button\",\n        onPress: themeSetting.toggle,\n        children: [\n            \"Change theme: \",\n            clientTheme\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/SwitchThemeButton.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL1N3aXRjaFRoZW1lQnV0dG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBZ0M7QUFDMkI7QUFDUTtBQUU1RCxNQUFNSyxvQkFBb0JBO0lBQy9CLE1BQU1DLGVBQWVILG9FQUFlQTtJQUNwQyxNQUFNLENBQUNJLE1BQU0sR0FBR0gsaUVBQVlBO0lBRTVCLE1BQU0sQ0FBQ0ksYUFBYUMsZUFBZSxHQUFHVCwrQ0FBUUEsQ0FBcUI7SUFFbkVFLGtFQUF5QkEsQ0FBQztRQUN4Qk8sZUFBZUgsYUFBYUksV0FBVyxJQUFJSixhQUFhSyxPQUFPLElBQUlKO0lBQ3JFLEdBQUc7UUFBQ0QsYUFBYUssT0FBTztRQUFFTCxhQUFhTSxhQUFhO0tBQUM7SUFFckQscUJBQU8sOERBQUNYLDJDQUFNQTtRQUFBWSxXQUFBO1FBQUFDLFdBQUE7UUFBQUMsV0FBQTtRQUFDQyxTQUFTVixhQUFhVyxNQUFNOztZQUFFO1lBQWVUOzs7Ozs7O0FBQzlELEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy91aS9zcmMvU3dpdGNoVGhlbWVCdXR0b24udHN4P2EyYmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiwgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCB9IGZyb20gJ3RhbWFndWknXG5pbXBvcnQgeyB1c2VUaGVtZVNldHRpbmcsIHVzZVJvb3RUaGVtZSB9IGZyb20gJ0B0YW1hZ3VpL25leHQtdGhlbWUnXG5cbmV4cG9ydCBjb25zdCBTd2l0Y2hUaGVtZUJ1dHRvbiA9ICgpID0+IHtcbiAgY29uc3QgdGhlbWVTZXR0aW5nID0gdXNlVGhlbWVTZXR0aW5nKClcbiAgY29uc3QgW3RoZW1lXSA9IHVzZVJvb3RUaGVtZSgpXG5cbiAgY29uc3QgW2NsaWVudFRoZW1lLCBzZXRDbGllbnRUaGVtZV0gPSB1c2VTdGF0ZTxzdHJpbmcgfCB1bmRlZmluZWQ+KCdsaWdodCcpXG5cbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgc2V0Q2xpZW50VGhlbWUodGhlbWVTZXR0aW5nLmZvcmNlZFRoZW1lIHx8IHRoZW1lU2V0dGluZy5jdXJyZW50IHx8IHRoZW1lKVxuICB9LCBbdGhlbWVTZXR0aW5nLmN1cnJlbnQsIHRoZW1lU2V0dGluZy5yZXNvbHZlZFRoZW1lXSlcblxuICByZXR1cm4gPEJ1dHRvbiBvblByZXNzPXt0aGVtZVNldHRpbmcudG9nZ2xlfT5DaGFuZ2UgdGhlbWU6IHtjbGllbnRUaGVtZX08L0J1dHRvbj5cbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkJ1dHRvbiIsInVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QiLCJ1c2VUaGVtZVNldHRpbmciLCJ1c2VSb290VGhlbWUiLCJTd2l0Y2hUaGVtZUJ1dHRvbiIsInRoZW1lU2V0dGluZyIsInRoZW1lIiwiY2xpZW50VGhlbWUiLCJzZXRDbGllbnRUaGVtZSIsImZvcmNlZFRoZW1lIiwiY3VycmVudCIsInJlc29sdmVkVGhlbWUiLCJkYXRhLWF0IiwiZGF0YS1pbiIsImRhdGEtaXMiLCJvblByZXNzIiwidG9nZ2xlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/ui/src/SwitchThemeButton.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/WalletSheet.tsx":
/*!*********************************************!*\
  !*** ../../packages/ui/src/WalletSheet.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletSheet: () => (/* binding */ WalletSheet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_2__]);\ntamagui__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst WalletSheet = ({ open, onOpenChange, wallets, selectedId, onSelect })=>{\n    const handleSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((index, account)=>{\n        // 隐藏底部抽屉\n        onOpenChange(false);\n        onSelect(account, index);\n    }, [\n        onSelect\n    ]);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Sheet, {\n        open: open,\n        forceRemoveScrollEnabled: open,\n        modal: false,\n        onOpenChange: onOpenChange,\n        snapPoints: [\n            \"60%\",\n            256,\n            190\n        ],\n        snapPointsMode: \"mixed\",\n        dismissOnSnapToBottom: true,\n        position: position,\n        onPositionChange: setPosition,\n        zIndex: 100000,\n        animation: \"medium\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Sheet.Overlay, {\n                animation: \"lazy\",\n                backgroundColor: \"$shadow6\",\n                enterStyle: {\n                    opacity: 0\n                },\n                exitStyle: {\n                    opacity: 0\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Sheet.Handle, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Sheet.Frame, {\n                padding: \"$4\",\n                backgroundColor: \"$color2\",\n                borderWidth: 1,\n                borderColor: \"#E5E6EB\",\n                borderTopLeftRadius: 16,\n                borderTopRightRadius: 16,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n                    \"data-at\": \"WalletSheet.tsx:55\",\n                    \"data-in\": \"WalletSheet\",\n                    \"data-is\": \"YStack\",\n                    gap: \"$3\",\n                    flex: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                            \"data-at\": \"WalletSheet.tsx:56\",\n                            \"data-in\": \"WalletSheet\",\n                            \"data-is\": \"XStack\",\n                            justifyContent: \"space-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                    \"data-at\": \"WalletSheet.tsx:57\",\n                                    \"data-in\": \"WalletSheet\",\n                                    \"data-is\": \"Text\",\n                                    fontWeight: \"600\",\n                                    fontSize: 18,\n                                    children: \"钱包\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                    \"data-at\": \"WalletSheet.tsx:60\",\n                                    \"data-in\": \"WalletSheet\",\n                                    \"data-is\": \"Text\",\n                                    fontWeight: \"500\",\n                                    fontSize: 16,\n                                    children: [\n                                        \"合计: $\",\n                                        (()=>{\n                                            let total = 0;\n                                            wallets.forEach((wallet)=>{\n                                                wallet.accounts.forEach((account)=>{\n                                                    const chains = [\n                                                        \"eth\",\n                                                        \"bsc\",\n                                                        \"btc\",\n                                                        \"solana\"\n                                                    ];\n                                                    chains.forEach((chain)=>{\n                                                        if (account[chain]?.balance) {\n                                                            total += parseFloat(account[chain].balance) || 0;\n                                                        }\n                                                    });\n                                                });\n                                            });\n                                            return total.toFixed(4);\n                                        })()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.ScrollView, {\n                            \"data-at\": \"WalletSheet.tsx:79\",\n                            \"data-in\": \"WalletSheet\",\n                            \"data-is\": \"ScrollView\",\n                            children: wallets.map((wallet, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n                                    \"data-at\": \"WalletSheet.tsx:81\",\n                                    \"data-in\": \"WalletSheet\",\n                                    \"data-is\": \"YStack\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            \"data-at\": \"WalletSheet.tsx:82\",\n                                            \"data-in\": \"WalletSheet\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 14,\n                                            color: \"$color11\",\n                                            my: 16,\n                                            children: `钱包 ${index + 1}`\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n                                            \"data-at\": \"WalletSheet.tsx:83\",\n                                            \"data-in\": \"WalletSheet\",\n                                            \"data-is\": \"YStack\",\n                                            children: wallet.accounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                                                    \"data-at\": \"WalletSheet.tsx:85-94\",\n                                                    \"data-in\": \"WalletSheet\",\n                                                    \"data-is\": \"XStack\",\n                                                    alignItems: \"center\",\n                                                    justifyContent: \"space-between\",\n                                                    padding: \"$3\",\n                                                    borderRadius: \"$4\",\n                                                    my: 12,\n                                                    backgroundColor: account.accountId === selectedId ? \"$blue4\" : \"transparent\",\n                                                    onPress: ()=>handleSelect(index, account),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                                                            \"data-at\": \"WalletSheet.tsx:95\",\n                                                            \"data-in\": \"WalletSheet\",\n                                                            \"data-is\": \"XStack\",\n                                                            alignItems: \"center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                                                    circular: true,\n                                                                    size: \"$4\",\n                                                                    mr: 12,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Avatar.Image, {\n                                                                            src: `https://api.dicebear.com/7.x/identicon/svg?seed=${account.accountId}`,\n                                                                            accessibilityLabel: account.accountId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                                            lineNumber: 63,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Avatar.Fallback, {\n                                                                            backgroundColor: \"$blue10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                                            lineNumber: 64,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                                    lineNumber: 62,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n                                                                    \"data-at\": \"WalletSheet.tsx:103\",\n                                                                    \"data-in\": \"WalletSheet\",\n                                                                    \"data-is\": \"YStack\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                                            \"data-at\": \"WalletSheet.tsx:104\",\n                                                                            \"data-in\": \"WalletSheet\",\n                                                                            \"data-is\": \"Text\",\n                                                                            fontWeight: \"600\",\n                                                                            fontSize: 16,\n                                                                            children: account.name || `地址 ${index + 1}`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                                            lineNumber: 67,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                                            \"data-at\": \"WalletSheet.tsx:107\",\n                                                                            \"data-in\": \"WalletSheet\",\n                                                                            \"data-is\": \"Text\",\n                                                                            fontSize: 14,\n                                                                            color: \"$color11\",\n                                                                            children: [\n                                                                                \"$\",\n                                                                                (()=>{\n                                                                                    let total = 0;\n                                                                                    const chains = [\n                                                                                        \"eth\",\n                                                                                        \"bsc\",\n                                                                                        \"btc\",\n                                                                                        \"solana\"\n                                                                                    ];\n                                                                                    chains.forEach((chain)=>{\n                                                                                        if (account[chain]?.balance) {\n                                                                                            total += parseFloat(account[chain].balance) || 0;\n                                                                                        }\n                                                                                    });\n                                                                                    return total.toFixed(4);\n                                                                                })()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                                            lineNumber: 70,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                                    lineNumber: 66,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        account.accountId === selectedId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_3__.Check, {\n                                                            size: 16,\n                                                            color: \"orange\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 60\n                                                        }, undefined)\n                                                    ]\n                                                }, account.accountId, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 60\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, wallet.walletId, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 45\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            \"data-at\": \"WalletSheet.tsx:131-139\",\n                            \"data-in\": \"WalletSheet\",\n                            \"data-is\": \"Button\",\n                            onPress: ()=>onSelect(\"addWallet\"),\n                            borderRadius: 40,\n                            height: 60,\n                            iconAfter: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_3__.Settings, {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 171\n                            }, void 0),\n                            fontSize: 16,\n                            color: \"$color\",\n                            fontWeight: 600,\n                            children: \"添加和管理钱包\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/ui/src/WalletSheet.tsx\",\n        lineNumber: 24,\n        columnNumber: 10\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/ui/src/WalletSheet.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/index.tsx":
/*!***************************************!*\
  !*** ../../packages/ui/src/index.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* reexport safe */ _my_config__WEBPACK_IMPORTED_MODULE_3__.config)\n/* harmony export */ });\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tamagui/toast */ \"../../node_modules/@tamagui/toast/dist/esm/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _tamagui_toast__WEBPACK_IMPORTED_MODULE_1__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _tamagui_toast__WEBPACK_IMPORTED_MODULE_1__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _MyComponent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MyComponent */ \"../../packages/ui/src/MyComponent.tsx\");\n/* harmony import */ var _my_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @my/config */ \"../../packages/config/src/index.ts\");\n/* harmony import */ var _CustomToast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CustomToast */ \"../../packages/ui/src/CustomToast.tsx\");\n/* harmony import */ var _SwitchThemeButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./SwitchThemeButton */ \"../../packages/ui/src/SwitchThemeButton.tsx\");\n/* harmony import */ var _NavBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NavBar */ \"../../packages/ui/src/NavBar.tsx\");\n/* harmony import */ var _PasswordForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PasswordForm */ \"../../packages/ui/src/PasswordForm.tsx\");\n/* harmony import */ var _PasswordInput__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PasswordInput */ \"../../packages/ui/src/PasswordInput.tsx\");\n/* harmony import */ var _ConfirmButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ConfirmButton */ \"../../packages/ui/src/ConfirmButton.tsx\");\n/* harmony import */ var _WalletSheet__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./WalletSheet */ \"../../packages/ui/src/WalletSheet.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([tamagui__WEBPACK_IMPORTED_MODULE_0__, _MyComponent__WEBPACK_IMPORTED_MODULE_2__, _my_config__WEBPACK_IMPORTED_MODULE_3__, _CustomToast__WEBPACK_IMPORTED_MODULE_4__, _SwitchThemeButton__WEBPACK_IMPORTED_MODULE_5__, _NavBar__WEBPACK_IMPORTED_MODULE_6__, _PasswordForm__WEBPACK_IMPORTED_MODULE_7__, _PasswordInput__WEBPACK_IMPORTED_MODULE_8__, _ConfirmButton__WEBPACK_IMPORTED_MODULE_9__, _WalletSheet__WEBPACK_IMPORTED_MODULE_10__]);\n([tamagui__WEBPACK_IMPORTED_MODULE_0__, _MyComponent__WEBPACK_IMPORTED_MODULE_2__, _my_config__WEBPACK_IMPORTED_MODULE_3__, _CustomToast__WEBPACK_IMPORTED_MODULE_4__, _SwitchThemeButton__WEBPACK_IMPORTED_MODULE_5__, _NavBar__WEBPACK_IMPORTED_MODULE_6__, _PasswordForm__WEBPACK_IMPORTED_MODULE_7__, _PasswordInput__WEBPACK_IMPORTED_MODULE_8__, _ConfirmButton__WEBPACK_IMPORTED_MODULE_9__, _WalletSheet__WEBPACK_IMPORTED_MODULE_10__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in tamagui__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => tamagui__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _MyComponent__WEBPACK_IMPORTED_MODULE_2__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _MyComponent__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _CustomToast__WEBPACK_IMPORTED_MODULE_4__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _CustomToast__WEBPACK_IMPORTED_MODULE_4__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _SwitchThemeButton__WEBPACK_IMPORTED_MODULE_5__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _SwitchThemeButton__WEBPACK_IMPORTED_MODULE_5__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _NavBar__WEBPACK_IMPORTED_MODULE_6__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _NavBar__WEBPACK_IMPORTED_MODULE_6__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _PasswordForm__WEBPACK_IMPORTED_MODULE_7__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _PasswordForm__WEBPACK_IMPORTED_MODULE_7__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _PasswordInput__WEBPACK_IMPORTED_MODULE_8__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _PasswordInput__WEBPACK_IMPORTED_MODULE_8__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _ConfirmButton__WEBPACK_IMPORTED_MODULE_9__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _ConfirmButton__WEBPACK_IMPORTED_MODULE_9__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _WalletSheet__WEBPACK_IMPORTED_MODULE_10__) if([\"default\",\"config\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _WalletSheet__WEBPACK_IMPORTED_MODULE_10__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n\n\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUI7QUFDTztBQUNEO0FBQ007QUFDTjtBQUNNO0FBQ1g7QUFDTTtBQUNDO0FBQ0E7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYXBwLy4uLy4uL3BhY2thZ2VzL3VpL3NyYy9pbmRleC50c3g/ZDA4MCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICd0YW1hZ3VpJ1xuZXhwb3J0ICogZnJvbSAnQHRhbWFndWkvdG9hc3QnXG5leHBvcnQgKiBmcm9tICcuL015Q29tcG9uZW50J1xuZXhwb3J0IHsgY29uZmlnIH0gZnJvbSAnQG15L2NvbmZpZydcbmV4cG9ydCAqIGZyb20gJy4vQ3VzdG9tVG9hc3QnXG5leHBvcnQgKiBmcm9tICcuL1N3aXRjaFRoZW1lQnV0dG9uJ1xuZXhwb3J0ICogZnJvbSAnLi9OYXZCYXInXG5leHBvcnQgKiBmcm9tICcuL1Bhc3N3b3JkRm9ybSdcbmV4cG9ydCAqIGZyb20gJy4vUGFzc3dvcmRJbnB1dCdcbmV4cG9ydCAqIGZyb20gJy4vQ29uZmlybUJ1dHRvbidcbmV4cG9ydCAqIGZyb20gJy4vV2FsbGV0U2hlZXQnXG4iXSwibmFtZXMiOlsiY29uZmlnIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/ui/src/index.tsx\n");

/***/ }),

/***/ "../../packages/ui/src/utils/constants.ts":
/*!************************************************!*\
  !*** ../../packages/ui/src/utils/constants.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PASSWORD_MIN_LENGTH: () => (/* binding */ PASSWORD_MIN_LENGTH)\n/* harmony export */ });\nconst PASSWORD_MIN_LENGTH = 8;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvdWkvc3JjL3V0aWxzL2NvbnN0YW50cy50cyIsIm1hcHBpbmdzIjoiOzs7O0FBRU8sTUFBTUEsc0JBQXNCLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWFwcC8uLi8uLi9wYWNrYWdlcy91aS9zcmMvdXRpbHMvY29uc3RhbnRzLnRzPzdkNGIiXSwic291cmNlc0NvbnRlbnQiOlsiXG5cbmV4cG9ydCBjb25zdCBQQVNTV09SRF9NSU5fTEVOR1RIID0gODsiXSwibmFtZXMiOlsiUEFTU1dPUkRfTUlOX0xFTkdUSCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/ui/src/utils/constants.ts\n");

/***/ }),

/***/ "../../packages/app/i18n/locales lazy recursive ^\\.\\/.*$":
/*!***********************************************************************!*\
  !*** ../../packages/app/i18n/locales/ lazy ^\.\/.*$ namespace object ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en": [
		"../../packages/app/i18n/locales/en.ts",
		"packages_app_i18n_locales_en_ts"
	],
	"./en.ts": [
		"../../packages/app/i18n/locales/en.ts",
		"packages_app_i18n_locales_en_ts"
	],
	"./zh": [
		"../../packages/app/i18n/locales/zh.ts",
		"packages_app_i18n_locales_zh_ts"
	],
	"./zh.ts": [
		"../../packages/app/i18n/locales/zh.ts",
		"packages_app_i18n_locales_zh_ts"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__(id);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "../../packages/app/i18n/locales lazy recursive ^\\.\\/.*$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "@react-native/assets-registry/registry":
/*!*********************************************************!*\
  !*** external "@react-native/assets-registry/registry" ***!
  \*********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@react-native/assets-registry/registry");

/***/ }),

/***/ "@react-native/normalize-color":
/*!************************************************!*\
  !*** external "@react-native/normalize-color" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@react-native/normalize-color");

/***/ }),

/***/ "@solana/web3.js":
/*!**********************************!*\
  !*** external "@solana/web3.js" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@solana/web3.js");

/***/ }),

/***/ "aria-hidden":
/*!******************************!*\
  !*** external "aria-hidden" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("aria-hidden");

/***/ }),

/***/ "bip39":
/*!************************!*\
  !*** external "bip39" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("bip39");

/***/ }),

/***/ "bitcoinjs-lib":
/*!********************************!*\
  !*** external "bitcoinjs-lib" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("bitcoinjs-lib");

/***/ }),

/***/ "fbjs/lib/invariant":
/*!*************************************!*\
  !*** external "fbjs/lib/invariant" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("fbjs/lib/invariant");

/***/ }),

/***/ "fbjs/lib/warning":
/*!***********************************!*\
  !*** external "fbjs/lib/warning" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("fbjs/lib/warning");

/***/ }),

/***/ "inline-style-prefixer/lib/createPrefixer":
/*!***********************************************************!*\
  !*** external "inline-style-prefixer/lib/createPrefixer" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/createPrefixer");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/backgroundClip":
/*!*******************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/backgroundClip" ***!
  \*******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/backgroundClip");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/crossFade":
/*!**************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/crossFade" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/crossFade");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/cursor":
/*!***********************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/cursor" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/cursor");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/filter":
/*!***********************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/filter" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/filter");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/imageSet":
/*!*************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/imageSet" ***!
  \*************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/imageSet");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/logical":
/*!************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/logical" ***!
  \************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/logical");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/position":
/*!*************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/position" ***!
  \*************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/position");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/sizing":
/*!***********************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/sizing" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/sizing");

/***/ }),

/***/ "inline-style-prefixer/lib/plugins/transition":
/*!***************************************************************!*\
  !*** external "inline-style-prefixer/lib/plugins/transition" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("inline-style-prefixer/lib/plugins/transition");

/***/ }),

/***/ "invariant":
/*!****************************!*\
  !*** external "invariant" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("invariant");

/***/ }),

/***/ "memoize-one":
/*!******************************!*\
  !*** external "memoize-one" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("memoize-one");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "nullthrows":
/*!*****************************!*\
  !*** external "nullthrows" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("nullthrows");

/***/ }),

/***/ "postcss-value-parser":
/*!***************************************!*\
  !*** external "postcss-value-parser" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("postcss-value-parser");

/***/ }),

/***/ "raf/polyfill":
/*!*******************************!*\
  !*** external "raf/polyfill" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("raf/polyfill");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-dom/client":
/*!***********************************!*\
  !*** external "react-dom/client" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom/client");

/***/ }),

/***/ "react-remove-scroll":
/*!**************************************!*\
  !*** external "react-remove-scroll" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-remove-scroll");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("styled-jsx/style");

/***/ }),

/***/ "styleq":
/*!*************************!*\
  !*** external "styleq" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("styleq");

/***/ }),

/***/ "styleq/transform-localize-style":
/*!**************************************************!*\
  !*** external "styleq/transform-localize-style" ***!
  \**************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("styleq/transform-localize-style");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "@floating-ui/react":
/*!*************************************!*\
  !*** external "@floating-ui/react" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@floating-ui/react");;

/***/ }),

/***/ "@floating-ui/react-dom":
/*!*****************************************!*\
  !*** external "@floating-ui/react-dom" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@floating-ui/react-dom");;

/***/ }),

/***/ "@scure/bip32":
/*!*******************************!*\
  !*** external "@scure/bip32" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("@scure/bip32");;

/***/ }),

/***/ "@scure/bip39":
/*!*******************************!*\
  !*** external "@scure/bip39" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = import("@scure/bip39");;

/***/ }),

/***/ "color2k":
/*!**************************!*\
  !*** external "color2k" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("color2k");;

/***/ }),

/***/ "ethers":
/*!*************************!*\
  !*** external "ethers" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("ethers");;

/***/ }),

/***/ "wif":
/*!**********************!*\
  !*** external "wif" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = import("wif");;

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tamagui","vendor-chunks/react-native-web","vendor-chunks/next","vendor-chunks/react-native-svg","vendor-chunks/expo-modules-core","vendor-chunks/tamagui","vendor-chunks/expo-constants","vendor-chunks/@babel","vendor-chunks/solito"], () => (__webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fwallet%2Fnotice&preferredRegion=&absolutePagePath=.%2Fpages%2Fwallet%2Fnotice.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();