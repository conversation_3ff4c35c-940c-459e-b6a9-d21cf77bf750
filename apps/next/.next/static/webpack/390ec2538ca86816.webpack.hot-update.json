{"c": ["pages/wallet/chooseNet", "webpack"], "r": ["pages/wallet/setting", "pages/wallet/buyTransfer", "/_error"], "m": ["../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsetting.tsx&page=%2Fwallet%2Fsetting!", "../../packages/app/features/wallet/setting-screen.tsx", "../../packages/assets/images/wallet/add.png", "../../packages/assets/images/wallet/set1.png", "../../packages/assets/images/wallet/set3.png", "../../packages/assets/images/wallet/set4.png", "../../packages/assets/images/wallet/set5.png", "../../packages/assets/images/wallet/set6.png", "../../packages/assets/images/wallet/set7.png", "../../packages/assets/images/wallet/user.png", "./pages/wallet/setting.tsx", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyTransfer.tsx&page=%2Fwallet%2FbuyTransfer!", "../../packages/app/features/wallet/buy-transfer-screen.tsx", "../../packages/assets/images/wallet/convert3.png", "./pages/wallet/buyTransfer.tsx", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fnode_modules%2Fnext%2Fdist%2Fpages%2F_error.js&page=%2F_error!"]}