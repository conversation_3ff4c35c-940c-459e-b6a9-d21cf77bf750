{"c": ["webpack"], "r": ["pages/wallet/buy", "pages/wallet/manager", "pages/wallet/import"], "m": ["../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fbuy.tsx&page=%2Fwallet%2Fbuy!", "../../packages/app/features/wallet/buy-screen.tsx", "../../packages/assets/images/wallet/change.png", "../../packages/assets/images/wallet/qrcode.png", "./pages/wallet/buy.tsx", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fmanager.tsx&page=%2Fwallet%2Fmanager!", "../../packages/app/features/wallet/manager-screen.tsx", "../../packages/assets/images/wallet/add.png", "../../packages/assets/images/wallet/daoru.png", "../../packages/assets/images/wallet/jiesuo.png", "../../packages/assets/images/wallet/new.png", "./pages/wallet/manager.tsx", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fimport.tsx&page=%2Fwallet%2Fimport!", "../../packages/app/features/wallet/import-mnemonic-screen.tsx", "../../packages/assets/images/wallet/copy.png", "./pages/wallet/import.tsx"]}