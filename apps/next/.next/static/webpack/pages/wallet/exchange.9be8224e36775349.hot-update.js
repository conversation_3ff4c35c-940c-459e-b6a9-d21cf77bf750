"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/exchange",{

/***/ "../../packages/assets/images/wallet/eth.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/wallet/eth.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/eth.b241a439.png\",\"height\":64,\"width\":64,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Feth.b241a439.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZXRoLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TEFBd0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2V0aC5wbmc/NjAwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZXRoLmIyNDFhNDM5LnBuZ1wiLFwiaGVpZ2h0XCI6NjQsXCJ3aWR0aFwiOjY0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmV0aC5iMjQxYTQzOS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/eth.png\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/exchange-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/exchange-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExchangeScreen: function() { return /* binding */ ExchangeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/exchange.png */ \"../../packages/assets/images/wallet/exchange.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 20\n});\n_c = Underline;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst ActiveUnderline = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = ActiveUnderline;\nfunction ExchangeScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_7__.useTransactionStore)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_8__.useWalletStore)();\n    const tabList = [\n        \"全部\",\n        \"交换\",\n        \"赚取\",\n        \"社交媒体\",\n        \"管理\",\n        \"监听\"\n    ];\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // 初始化交易数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        transactionStore.loadTransactions();\n    }, []);\n    // 获取当前账户的所有链的交易\n    const currentAccount = walletStore.currentAccount;\n    const transactions = currentAccount ? (()=>{\n        var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n        // 获取所有链的地址\n        const addresses = [\n            (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n            (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n            (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n            (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n        ].filter(Boolean); // 过滤掉undefined/null值\n        // 使用新的去重方法获取交易记录\n        return transactionStore.getTransactionsByAddresses(addresses);\n    })() : [];\n    // 按日期分组交易\n    const groupTransactionsByDate = (transactions)=>{\n        const groups = {};\n        transactions.forEach((tx)=>{\n            const date = new Date(tx.timestamp);\n            const today = new Date();\n            const yesterday = new Date(today);\n            yesterday.setDate(yesterday.getDate() - 1);\n            let dateKey = \"\";\n            if (date.toDateString() === today.toDateString()) {\n                dateKey = t(\"time.today\") || \"今天\";\n            } else if (date.toDateString() === yesterday.toDateString()) {\n                dateKey = t(\"time.yesterday\") || \"昨天\";\n            } else {\n                dateKey = date.toLocaleDateString();\n            }\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push((0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_7__.formatTransaction)(tx));\n        });\n        return groups;\n    };\n    const groupedTransactions = groupTransactionsByDate(transactions);\n    // 获取链图标\n    const getChainIcon = (chain)=>{\n        switch(chain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            // 暂时使用同一个图标\n            case \"btc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            default:\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n        }\n    };\n    // 获取状态颜色\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"confirmed\":\n                return \"#2FAB77\";\n            case \"pending\":\n                return \"#FFA500\";\n            case \"failed\":\n                return \"#C7545E\";\n            default:\n                return \"#8B8F9A\";\n        }\n    };\n    const dataList = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"交易资产\",\n            isSelected: false\n        },\n        {\n            id: 3,\n            name: \"SushiSwap\",\n            desc: \"交易资产\",\n            isSelected: false\n        }\n    ];\n    const onMnemonicClick = ()=>{\n        router.push(\"/wallet/password\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n        \"data-at\": \"exchange-screen.tsx:153\",\n        \"data-in\": \"ExchangeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        px: 16,\n        py: 30,\n        minHeight: \"100vh\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                \"data-at\": \"exchange-screen.tsx:154\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                justifyContent: \"space-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.H3, {\n                    \"data-at\": \"exchange-screen.tsx:155\",\n                    \"data-in\": \"ExchangeScreen\",\n                    \"data-is\": \"H3\",\n                    children: \"交易\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_10__.Pressable, {\n                onPress: ()=>router.push(\"/wallet/transaction\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                    \"data-at\": \"exchange-screen.tsx:160\",\n                    \"data-in\": \"ExchangeScreen\",\n                    \"data-is\": \"XStack\",\n                    mt: 20,\n                    p: 16,\n                    bg: \"#1A1A1A\",\n                    borderRadius: 12,\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                            \"data-at\": \"exchange-screen.tsx:161\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"XStack\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                    \"data-at\": \"exchange-screen.tsx:162\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"View\",\n                                    width: 40,\n                                    height: 40,\n                                    bg: \"#4575FF\",\n                                    borderRadius: 20,\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"exchange-screen.tsx:163\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 18,\n                                        fontWeight: \"bold\",\n                                        children: \"\\uD83D\\uDCCA\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                    \"data-at\": \"exchange-screen.tsx:165\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"YStack\",\n                                    ml: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:166\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"white\",\n                                            fontSize: 16,\n                                            fontWeight: \"bold\",\n                                            children: t(\"wallet.transactionHistory\") || \"交易历史\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:169\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#8B8F9A\",\n                                            fontSize: 14,\n                                            children: [\n                                                getRecentTransactionsCount(),\n                                                \" 笔交易记录\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__.ChevronRight, {\n                            size: 20,\n                            color: \"#8B8F9A\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            getRecentTransactionsCount() == 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"exchange-screen.tsx:178\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"XStack\",\n                        pl: 16,\n                        justifyContent: \"center\",\n                        mt: 50,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                            \"data-at\": \"exchange-screen.tsx:179\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                            width: 173,\n                            height: 142\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:181\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        pl: 16,\n                        justifyContent: \"center\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:182\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$white1\",\n                                fontSize: 16,\n                                fontWeight: \"bold\",\n                                textAlign: \"center\",\n                                mb: 10,\n                                children: \"还没有交易\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:183\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$accent11\",\n                                width: 280,\n                                textAlign: \"center\",\n                                margin: \"auto\",\n                                children: \"一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                \"data-at\": \"exchange-screen.tsx:186\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Button\",\n                rounded: 30,\n                position: \"absolute\",\n                bottom: 20,\n                width: \"90%\",\n                bg: \"$accent11\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                    \"data-at\": \"exchange-screen.tsx:187\",\n                    \"data-in\": \"ExchangeScreen\",\n                    \"data-is\": \"Text\",\n                    color: \"$white1\",\n                    children: \"将加密货币添加到您的钱包\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_12__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n        lineNumber: 132,\n        columnNumber: 10\n    }, this);\n}\n_s(ExchangeScreen, \"N/1dQ+BumDL/UnL/eMvSla1VVW8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_7__.useTransactionStore,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_8__.useWalletStore\n    ];\n});\n_c3 = ExchangeScreen;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"ActiveUnderline\");\n$RefreshReg$(_c3, \"ExchangeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL3dhbGxldC9leGNoYW5nZS1zY3JlZW4udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE0RztBQUMvRDtBQUNjO0FBQ1U7QUFFN0I7QUFDRztBQUNTO0FBQ1A7QUFDaUY7QUFDdkU7QUFDZDtBQUNrQjtBQUczRCxNQUFNb0IsWUFBWVosK0NBQU1BLENBQUNILHlDQUFJQSxFQUFFO0lBQzdCZ0IsT0FBTztJQUNQQyxRQUFRO0lBQ1JDLGlCQUFpQjtJQUNqQkMsSUFBSTtBQUNOOztBQUNBLE1BQU1DLGFBQWFqQiwrQ0FBTUEsQ0FBQ0YseUNBQUlBLEVBQUU7SUFDOUJvQixPQUFPO0lBQ1BDLGNBQWM7QUFDaEI7O0FBQ0EsTUFBTUMsa0JBQWtCcEIsK0NBQU1BLENBQUNILHlDQUFJQSxFQUFFO0lBQ25Dd0IsVUFBVTtJQUNWQyxRQUFRLENBQUM7SUFDVEMsTUFBTTtJQUNOQyxPQUFPO0lBQ1BWLFFBQVE7SUFDUkMsaUJBQWlCO0FBQ25COztBQUVPLFNBQVNVOztJQUNkLE1BQU1DLFNBQVM5Qiw0REFBU0E7SUFDeEIsTUFBTSxFQUFFK0IsQ0FBQUEsRUFBRyxHQUFHakIsd0RBQWNBO0lBQzVCLE1BQU1rQixtQkFBbUJyQixnRkFBbUJBO0lBQzVDLE1BQU1zQixjQUFjcEIsc0VBQWNBO0lBRWxDLE1BQU1xQixVQUFVO1FBQ2Q7UUFBTTtRQUFNO1FBQU07UUFBUTtRQUFNO0tBQ2pDO0lBQ0QsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUU3QztJQUNBQyxnREFBU0EsQ0FBQztRQUNSd0IsaUJBQWlCSyxnQkFBZ0I7SUFDbkMsR0FBRyxFQUFFO0lBRUw7SUFDQSxNQUFNQyxpQkFBaUJMLFlBQVlLLGNBQWM7SUFDakQsTUFBTUMsZUFBZUQsaUJBQ2pCLENBQUM7WUFHQ0EscUJBQ0FBLHFCQUNBQSxxQkFDQUE7UUFMRjtRQUNBLE1BQU1FLFlBQVk7YUFDaEJGLHNCQUFBQSxlQUFlRyxHQUFHLGNBQWxCSCwwQ0FBQUEsb0JBQW9CSSxPQUFPO2FBQzNCSixzQkFBQUEsZUFBZUssR0FBRyxjQUFsQkwsMENBQUFBLG9CQUFvQkksT0FBTzthQUMzQkosc0JBQUFBLGVBQWVNLEdBQUcsY0FBbEJOLDBDQUFBQSxvQkFBb0JJLE9BQU87YUFDM0JKLHlCQUFBQSxlQUFlTyxNQUFNLGNBQXJCUCw2Q0FBQUEsdUJBQXVCSSxPQUFPO1NBQy9CLENBQUNJLE1BQU0sQ0FBQ0MsVUFBUztRQUVsQjtRQUNBLE9BQU9mLGlCQUFpQmdCLDBCQUEwQixDQUFDUjtJQUNyRCxPQUNFLEVBQUU7SUFFTjtJQUNBLE1BQU1TLDBCQUEwQkEsQ0FBQ1Y7UUFDL0IsTUFBTVcsU0FBbUMsQ0FBQztRQUUxQ1gsYUFBYVksT0FBTyxDQUFFQyxDQUFBQTtZQUNwQixNQUFNQyxPQUFPLElBQUlDLEtBQUtGLEdBQUdHLFNBQVM7WUFDbEMsTUFBTUMsUUFBUSxJQUFJRjtZQUNsQixNQUFNRyxZQUFZLElBQUlILEtBQUtFO1lBQzNCQyxVQUFVQyxPQUFPLENBQUNELFVBQVVFLE9BQU8sS0FBSztZQUV4QyxJQUFJQyxVQUFVO1lBQ2QsSUFBSVAsS0FBS1EsWUFBWSxPQUFPTCxNQUFNSyxZQUFZLElBQUk7Z0JBQ2hERCxVQUFVN0IsRUFBRSxpQkFBaUI7WUFDL0IsT0FBTyxJQUFJc0IsS0FBS1EsWUFBWSxPQUFPSixVQUFVSSxZQUFZLElBQUk7Z0JBQzNERCxVQUFVN0IsRUFBRSxxQkFBcUI7WUFDbkMsT0FBTztnQkFDTDZCLFVBQVVQLEtBQUtTLGtCQUFrQjtZQUNuQztZQUVBLElBQUksQ0FBQ1osTUFBTSxDQUFDVSxRQUFRLEVBQUU7Z0JBQ3BCVixNQUFNLENBQUNVLFFBQVEsR0FBRyxFQUFFO1lBQ3RCO1lBQ0FWLE1BQU0sQ0FBQ1UsUUFBUSxDQUFDRyxJQUFJLENBQUNuRCw4RUFBaUJBLENBQUN3QztRQUN6QztRQUVBLE9BQU9GO0lBQ1Q7SUFFQSxNQUFNYyxzQkFBc0JmLHdCQUF3QlY7SUFFcEQ7SUFDQSxNQUFNMEIsZUFBZUEsQ0FBQ0M7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU9uRCx5RUFBVztZQUNwQixLQUFLO2dCQUNILE9BQU9BLHlFQUFXO1lBQUM7WUFDckIsS0FBSztnQkFDSCxPQUFPQSx5RUFBVztZQUNwQixLQUFLO2dCQUNILE9BQU9BLHlFQUFXO1lBQ3BCO2dCQUNFLE9BQU9BLHlFQUFXO1FBQ3RCO0lBQ0Y7SUFFQTtJQUNBLE1BQU1xRCxpQkFBaUJBLENBQUNDO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztnQkFDSCxPQUFPO1lBQ1Q7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFDQSxNQUFNQyxXQUFXO1FBQ2Y7WUFDRUMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtRQUNkO1FBQ0E7WUFDRUgsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtRQUNkO1FBQ0E7WUFDRUgsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtRQUNkO0tBQ0Q7SUFFRCxNQUFNQyxrQkFBa0JBO1FBQ3RCN0MsT0FBT2lDLElBQUksQ0FBQztJQUNkO0lBRUEscUJBQ0UsOERBQUNoRSwwQ0FBTUE7UUFBQTZFLFdBQUE7UUFBQUMsV0FBQTtRQUFBQyxXQUFBO1FBQUNDLElBQUc7UUFBY0MsSUFBSTtRQUFJQyxJQUFJO1FBQUlDLFdBQVc7OzBCQUNsRCw4REFBQ3BGLDBDQUFNQTtnQkFBQThFLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNLLElBQUk7Z0JBQUlDLGdCQUFlOzBCQUM3Qiw0RUFBQ3ZGLHNDQUFFQTtvQkFBQStFLFdBQUE7b0JBQUFDLFdBQUE7b0JBQUFDLFdBQUE7OEJBQUM7Ozs7Ozs7Ozs7OzBCQUlOLDhEQUFDeEUsb0RBQVNBO2dCQUFDK0UsU0FBUyxJQUFNdkQsT0FBT2lDLElBQUksQ0FBQzswQkFDcEMsNEVBQUNqRSwwQ0FBTUE7b0JBQUE4RSxXQUFBO29CQUFBQyxXQUFBO29CQUFBQyxXQUFBO29CQUFDMUQsSUFBSTtvQkFBSWtFLEdBQUc7b0JBQUlQLElBQUc7b0JBQVVRLGNBQWM7b0JBQUlDLFlBQVc7b0JBQVNKLGdCQUFlOztzQ0FDdkYsOERBQUN0RiwwQ0FBTUE7NEJBQUE4RSxXQUFBOzRCQUFBQyxXQUFBOzRCQUFBQyxXQUFBOzRCQUFDVSxZQUFXOzs4Q0FDakIsOERBQUN2Rix5Q0FBSUE7b0NBQUEyRSxXQUFBO29DQUFBQyxXQUFBO29DQUFBQyxXQUFBO29DQUFDN0QsT0FBTztvQ0FBSUMsUUFBUTtvQ0FBSTZELElBQUc7b0NBQVVRLGNBQWM7b0NBQUlDLFlBQVc7b0NBQVNKLGdCQUFlOzhDQUM3Riw0RUFBQ2xGLHlDQUFJQTt3Q0FBQTBFLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUN4RCxPQUFNO3dDQUFRbUUsVUFBVTt3Q0FBSUMsWUFBVztrREFBTzs7Ozs7Ozs7Ozs7OENBRXRELDhEQUFDM0YsMENBQU1BO29DQUFBNkUsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQ2EsSUFBSTs7c0RBQ1YsOERBQUN6Rix5Q0FBSUE7NENBQUEwRSxXQUFBOzRDQUFBQyxXQUFBOzRDQUFBQyxXQUFBOzRDQUFDeEQsT0FBTTs0Q0FBUW1FLFVBQVU7NENBQUlDLFlBQVc7c0RBQzFDM0QsRUFBRSxnQ0FBZ0M7Ozs7OztzREFFckMsOERBQUM3Qix5Q0FBSUE7NENBQUEwRSxXQUFBOzRDQUFBQyxXQUFBOzRDQUFBQyxXQUFBOzRDQUFDeEQsT0FBTTs0Q0FBVW1FLFVBQVU7O2dEQUM3Qkc7Z0RBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlwQyw4REFBQ25GLGdFQUFZQTs0QkFBQ29GLE1BQU07NEJBQUl2RSxPQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztZQUdqQ3NFLGdDQUFnQyxrQkFBSTs7a0NBQ25DLDhEQUFDOUYsMENBQU1BO3dCQUFBOEUsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ0ssSUFBSTt3QkFBSUMsZ0JBQWU7d0JBQVNoRSxJQUFJO2tDQUMxQyw0RUFBQ2pCLDBDQUFLQTs0QkFBQXlFLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUFDLFdBQUE7NEJBQUNnQixRQUFRekYsOEVBQWdCOzRCQUFFWSxPQUFPOzRCQUFLQyxRQUFROzs7Ozs7Ozs7OztrQ0FFdkQsOERBQUNuQiwwQ0FBTUE7d0JBQUE2RSxXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDSyxJQUFJO3dCQUFJQyxnQkFBZTt3QkFBU2hFLElBQUk7OzBDQUMxQyw4REFBQ2xCLHlDQUFJQTtnQ0FBQTBFLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUN4RCxPQUFNO2dDQUFVbUUsVUFBVTtnQ0FBSUMsWUFBVztnQ0FBT0ssV0FBVTtnQ0FBU0MsSUFBSTswQ0FBSTs7Ozs7OzBDQUNqRiw4REFBQzlGLHlDQUFJQTtnQ0FBQTBFLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUN4RCxPQUFNO2dDQUFZTCxPQUFPO2dDQUFLOEUsV0FBVTtnQ0FBU0UsUUFBTzswQ0FBTzs7Ozs7Ozs7Ozs7OzsrQkFFbkU7MEJBQ04sOERBQUNyRywwQ0FBTUE7Z0JBQUFnRixXQUFBO2dCQUFBQyxXQUFBO2dCQUFBQyxXQUFBO2dCQUFDb0IsU0FBUztnQkFBSXpFLFVBQVM7Z0JBQVdDLFFBQVE7Z0JBQUlULE9BQU07Z0JBQU04RCxJQUFHOzBCQUNsRSw0RUFBQzdFLHlDQUFJQTtvQkFBQTBFLFdBQUE7b0JBQUFDLFdBQUE7b0JBQUFDLFdBQUE7b0JBQUN4RCxPQUFNOzhCQUFVOzs7Ozs7Ozs7OzswQkFFeEIsOERBQUNaLHVEQUFZQTs7Ozs7Ozs7Ozs7QUFJbkI7R0E5SmdCbUI7O1FBQ0M3Qix3REFBU0E7UUFDVmMsb0RBQWNBO1FBQ0hILDRFQUFtQkE7UUFDeEJFLGtFQUFjQTs7O01BSnBCZ0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2FwcC9mZWF0dXJlcy93YWxsZXQvZXhjaGFuZ2Utc2NyZWVuLnRzeD82ZTEyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEF2YXRhciwgQnV0dG9uLCBIMywgSDUsIE5hdkJhciwgUGFyYWdyYXBoLCBTd2l0Y2hUaGVtZUJ1dHRvbiwgWFN0YWNrLCBZU3RhY2ssIElucHV0IH0gZnJvbSAnQG15L3VpJ1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnc29saXRvL25hdmlnYXRpb24nXG5pbXBvcnQgeyBWaWV3LCBUZXh0LCBJbWFnZSwgSDEsIEgyLCBzdHlsZWQgfSBmcm9tICd0YW1hZ3VpJ1xuaW1wb3J0IGV4Y2VoYW5nSWNvbiBmcm9tICcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL3dhbGxldC9leGNoYW5nZS5wbmcnXG5cbmltcG9ydCB7IFByZXNzYWJsZSB9IGZyb20gJ3JlYWN0LW5hdGl2ZSdcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENoZXZyb25SaWdodCB9IGZyb20gJ0B0YW1hZ3VpL2x1Y2lkZS1pY29ucydcbmltcG9ydCB7IEZvb3Rlck5hdkJhciB9IGZyb20gJy4uL2hvbWUvc2NyZWVuJ1xuaW1wb3J0IHsgdXNlVHJhbnNhY3Rpb25TdG9yZSwgZm9ybWF0VHJhbnNhY3Rpb24sIGdldFN0YXR1c0Rpc3BsYXlUZXh0LCBnZXRUeXBlRGlzcGxheVRleHQgfSBmcm9tICdhcHAvc3RvcmVzL3RyYW5zYWN0aW9uU3RvcmUnXG5pbXBvcnQgeyB1c2VXYWxsZXRTdG9yZSB9IGZyb20gJ2FwcC9zdG9yZXMvd2FsbGV0U3RvcmUnXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ2FwcC9pMThuJ1xuaW1wb3J0IGV0aEljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy93YWxsZXQvZXRoLnBuZydcblxuXG5jb25zdCBVbmRlcmxpbmUgPSBzdHlsZWQoVmlldywge1xuICB3aWR0aDogJzEwMCUnLFxuICBoZWlnaHQ6IDEsXG4gIGJhY2tncm91bmRDb2xvcjogJyMyMTIyMjQnLFxuICBtdDogMjBcbn0pXG5jb25zdCBBY3RpdmVUZXh0ID0gc3R5bGVkKFRleHQsIHtcbiAgY29sb3I6ICcjNDU3NUZGJyxcbiAgbWFyZ2luQm90dG9tOiAyXG59KVxuY29uc3QgQWN0aXZlVW5kZXJsaW5lID0gc3R5bGVkKFZpZXcsIHtcbiAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gIGJvdHRvbTogLTIsXG4gIGxlZnQ6IDAsXG4gIHJpZ2h0OiAwLFxuICBoZWlnaHQ6IDIsXG4gIGJhY2tncm91bmRDb2xvcjogJyM0NTc1RkYnXG59KVxuXG5leHBvcnQgZnVuY3Rpb24gRXhjaGFuZ2VTY3JlZW4oKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKVxuICBjb25zdCB0cmFuc2FjdGlvblN0b3JlID0gdXNlVHJhbnNhY3Rpb25TdG9yZSgpXG4gIGNvbnN0IHdhbGxldFN0b3JlID0gdXNlV2FsbGV0U3RvcmUoKVxuXG4gIGNvbnN0IHRhYkxpc3QgPSBbXG4gICAgJ+WFqOmDqCcsICfkuqTmjaInLCAn6LWa5Y+WJywgJ+ekvuS6pOWqkuS9kycsICfnrqHnkIYnLCAn55uR5ZCsJ1xuICBdXG4gIGNvbnN0IFtjdXJyZW50VGFiLCBzZXRDdXJyZW50VGFiXSA9IHVzZVN0YXRlKDApXG5cbiAgLy8g5Yid5aeL5YyW5Lqk5piT5pWw5o2uXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgdHJhbnNhY3Rpb25TdG9yZS5sb2FkVHJhbnNhY3Rpb25zKClcbiAgfSwgW10pXG5cbiAgLy8g6I635Y+W5b2T5YmN6LSm5oi355qE5omA5pyJ6ZO+55qE5Lqk5piTXG4gIGNvbnN0IGN1cnJlbnRBY2NvdW50ID0gd2FsbGV0U3RvcmUuY3VycmVudEFjY291bnRcbiAgY29uc3QgdHJhbnNhY3Rpb25zID0gY3VycmVudEFjY291bnRcbiAgICA/ICgoKSA9PiB7XG4gICAgICAvLyDojrflj5bmiYDmnInpk77nmoTlnLDlnYBcbiAgICAgIGNvbnN0IGFkZHJlc3NlcyA9IFtcbiAgICAgICAgY3VycmVudEFjY291bnQuZXRoPy5hZGRyZXNzLFxuICAgICAgICBjdXJyZW50QWNjb3VudC5ic2M/LmFkZHJlc3MsXG4gICAgICAgIGN1cnJlbnRBY2NvdW50LmJ0Yz8uYWRkcmVzcyxcbiAgICAgICAgY3VycmVudEFjY291bnQuc29sYW5hPy5hZGRyZXNzLFxuICAgICAgXS5maWx0ZXIoQm9vbGVhbikgLy8g6L+H5ruk5o6JdW5kZWZpbmVkL251bGzlgLxcblxuICAgICAgLy8g5L2/55So5paw55qE5Y676YeN5pa55rOV6I635Y+W5Lqk5piT6K6w5b2VXG4gICAgICByZXR1cm4gdHJhbnNhY3Rpb25TdG9yZS5nZXRUcmFuc2FjdGlvbnNCeUFkZHJlc3NlcyhhZGRyZXNzZXMpXG4gICAgfSkoKVxuICAgIDogW11cblxuICAvLyDmjInml6XmnJ/liIbnu4TkuqTmmJNcbiAgY29uc3QgZ3JvdXBUcmFuc2FjdGlvbnNCeURhdGUgPSAodHJhbnNhY3Rpb25zOiBhbnlbXSkgPT4ge1xuICAgIGNvbnN0IGdyb3VwczogeyBba2V5OiBzdHJpbmddOiBhbnlbXSB9ID0ge31cblxuICAgIHRyYW5zYWN0aW9ucy5mb3JFYWNoKCh0eCkgPT4ge1xuICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHR4LnRpbWVzdGFtcClcbiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKVxuICAgICAgY29uc3QgeWVzdGVyZGF5ID0gbmV3IERhdGUodG9kYXkpXG4gICAgICB5ZXN0ZXJkYXkuc2V0RGF0ZSh5ZXN0ZXJkYXkuZ2V0RGF0ZSgpIC0gMSlcblxuICAgICAgbGV0IGRhdGVLZXkgPSAnJ1xuICAgICAgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHRvZGF5LnRvRGF0ZVN0cmluZygpKSB7XG4gICAgICAgIGRhdGVLZXkgPSB0KCd0aW1lLnRvZGF5JykgfHwgJ+S7iuWkqSdcbiAgICAgIH0gZWxzZSBpZiAoZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0geWVzdGVyZGF5LnRvRGF0ZVN0cmluZygpKSB7XG4gICAgICAgIGRhdGVLZXkgPSB0KCd0aW1lLnllc3RlcmRheScpIHx8ICfmmKjlpKknXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBkYXRlS2V5ID0gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoKVxuICAgICAgfVxuXG4gICAgICBpZiAoIWdyb3Vwc1tkYXRlS2V5XSkge1xuICAgICAgICBncm91cHNbZGF0ZUtleV0gPSBbXVxuICAgICAgfVxuICAgICAgZ3JvdXBzW2RhdGVLZXldLnB1c2goZm9ybWF0VHJhbnNhY3Rpb24odHgpKVxuICAgIH0pXG5cbiAgICByZXR1cm4gZ3JvdXBzXG4gIH1cblxuICBjb25zdCBncm91cGVkVHJhbnNhY3Rpb25zID0gZ3JvdXBUcmFuc2FjdGlvbnNCeURhdGUodHJhbnNhY3Rpb25zKVxuXG4gIC8vIOiOt+WPlumTvuWbvuagh1xuICBjb25zdCBnZXRDaGFpbkljb24gPSAoY2hhaW46IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoY2hhaW4pIHtcbiAgICAgIGNhc2UgJ2V0aCc6XG4gICAgICAgIHJldHVybiBldGhJY29uLnNyY1xuICAgICAgY2FzZSAnYnNjJzpcbiAgICAgICAgcmV0dXJuIGV0aEljb24uc3JjIC8vIOaaguaXtuS9v+eUqOWQjOS4gOS4quWbvuagh1xuICAgICAgY2FzZSAnYnRjJzpcbiAgICAgICAgcmV0dXJuIGV0aEljb24uc3JjXG4gICAgICBjYXNlICdzb2xhbmEnOlxuICAgICAgICByZXR1cm4gZXRoSWNvbi5zcmNcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBldGhJY29uLnNyY1xuICAgIH1cbiAgfVxuXG4gIC8vIOiOt+WPlueKtuaAgeminOiJslxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdjb25maXJtZWQnOlxuICAgICAgICByZXR1cm4gJyMyRkFCNzcnXG4gICAgICBjYXNlICdwZW5kaW5nJzpcbiAgICAgICAgcmV0dXJuICcjRkZBNTAwJ1xuICAgICAgY2FzZSAnZmFpbGVkJzpcbiAgICAgICAgcmV0dXJuICcjQzc1NDVFJ1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuICcjOEI4RjlBJ1xuICAgIH1cbiAgfVxuICBjb25zdCBkYXRhTGlzdCA9IFtcbiAgICB7XG4gICAgICBpZDogMSxcbiAgICAgIG5hbWU6ICdBZXJvZHJvbWUnLFxuICAgICAgZGVzYzogJ+S6pOaYk+i1hOS6pycsXG4gICAgICBpc1NlbGVjdGVkOiB0cnVlXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogMixcbiAgICAgIG5hbWU6ICdVbmlzd2FwJyxcbiAgICAgIGRlc2M6ICfkuqTmmJPotYTkuqcnLFxuICAgICAgaXNTZWxlY3RlZDogZmFsc2VcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAzLFxuICAgICAgbmFtZTogJ1N1c2hpU3dhcCcsXG4gICAgICBkZXNjOiAn5Lqk5piT6LWE5LqnJyxcbiAgICAgIGlzU2VsZWN0ZWQ6IGZhbHNlXG4gICAgfSxcbiAgXVxuXG4gIGNvbnN0IG9uTW5lbW9uaWNDbGljayA9ICgpID0+IHtcbiAgICByb3V0ZXIucHVzaCgnL3dhbGxldC9wYXNzd29yZCcpXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxZU3RhY2sgYmc9XCIkYmFja2dyb3VuZFwiIHB4PXsxNn0gcHk9ezMwfSBtaW5IZWlnaHQ9eycxMDB2aCd9PlxuICAgICAgPFhTdGFjayBwbD17MTZ9IGp1c3RpZnlDb250ZW50PSdzcGFjZS1iZXR3ZWVuJz5cbiAgICAgICAgPEgzPuS6pOaYkzwvSDM+XG4gICAgICA8L1hTdGFjaz5cblxuICAgICAgey8qIOS6pOaYk+WOhuWPsuW/q+aNt+WFpeWPoyAqL31cbiAgICAgIDxQcmVzc2FibGUgb25QcmVzcz17KCkgPT4gcm91dGVyLnB1c2goJy93YWxsZXQvdHJhbnNhY3Rpb24nKX0+XG4gICAgICAgIDxYU3RhY2sgbXQ9ezIwfSBwPXsxNn0gYmc9XCIjMUExQTFBXCIgYm9yZGVyUmFkaXVzPXsxMn0gYWxpZ25JdGVtcz1cImNlbnRlclwiIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiPlxuICAgICAgICAgIDxYU3RhY2sgYWxpZ25JdGVtcz1cImNlbnRlclwiPlxuICAgICAgICAgICAgPFZpZXcgd2lkdGg9ezQwfSBoZWlnaHQ9ezQwfSBiZz1cIiM0NTc1RkZcIiBib3JkZXJSYWRpdXM9ezIwfSBhbGlnbkl0ZW1zPVwiY2VudGVyXCIganVzdGlmeUNvbnRlbnQ9XCJjZW50ZXJcIj5cbiAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCJ3aGl0ZVwiIGZvbnRTaXplPXsxOH0gZm9udFdlaWdodD1cImJvbGRcIj7wn5OKPC9UZXh0PlxuICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgICAgPFlTdGFjayBtbD17MTJ9PlxuICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCIgZm9udFNpemU9ezE2fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICAgIHt0KCd3YWxsZXQudHJhbnNhY3Rpb25IaXN0b3J5JykgfHwgJ+S6pOaYk+WOhuWPsid9XG4gICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCIjOEI4RjlBXCIgZm9udFNpemU9ezE0fT5cbiAgICAgICAgICAgICAgICB7Z2V0UmVjZW50VHJhbnNhY3Rpb25zQ291bnQoKX0g56yU5Lqk5piT6K6w5b2VXG4gICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgIDwvWVN0YWNrPlxuICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgIDxDaGV2cm9uUmlnaHQgc2l6ZT17MjB9IGNvbG9yPVwiIzhCOEY5QVwiIC8+XG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgPC9QcmVzc2FibGU+XG4gICAgICB7Z2V0UmVjZW50VHJhbnNhY3Rpb25zQ291bnQoKSA9PSAwID8gPD5cbiAgICAgICAgPFhTdGFjayBwbD17MTZ9IGp1c3RpZnlDb250ZW50PSdjZW50ZXInIG10PXs1MH0+XG4gICAgICAgICAgPEltYWdlIHNvdXJjZT17ZXhjZWhhbmdJY29uLnNyY30gd2lkdGg9ezE3M30gaGVpZ2h0PXsxNDJ9IC8+XG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgICA8WVN0YWNrIHBsPXsxNn0ganVzdGlmeUNvbnRlbnQ9J2NlbnRlcicgbXQ9ezIwfT5cbiAgICAgICAgICA8VGV4dCBjb2xvcj1cIiR3aGl0ZTFcIiBmb250U2l6ZT17MTZ9IGZvbnRXZWlnaHQ9XCJib2xkXCIgdGV4dEFsaWduPSdjZW50ZXInIG1iPXsxMH0+6L+Y5rKh5pyJ5Lqk5piTPC9UZXh0PlxuICAgICAgICAgIDxUZXh0IGNvbG9yPVwiJGFjY2VudDExXCIgd2lkdGg9ezI4MH0gdGV4dEFsaWduPSdjZW50ZXInIG1hcmdpbj1cImF1dG9cIj7kuIDml6bmgqjlvIDlp4vkvb/nlKjpkrHljIXvvIzmgqjnmoTliqDlr4botKfluIHlkowgTkZUIOa0u+WKqOWwhuaYvuekuuWcqOi/memHjOOAgjwvVGV4dD5cbiAgICAgICAgPC9ZU3RhY2s+XG4gICAgICA8Lz4gOiBudWxsfVxuICAgICAgPEJ1dHRvbiByb3VuZGVkPXszMH0gcG9zaXRpb249J2Fic29sdXRlJyBib3R0b209ezIwfSB3aWR0aD1cIjkwJVwiIGJnPVwiJGFjY2VudDExXCI+XG4gICAgICAgIDxUZXh0IGNvbG9yPVwiJHdoaXRlMVwiPuWwhuWKoOWvhui0p+W4gea3u+WKoOWIsOaCqOeahOmSseWMhTwvVGV4dD5cbiAgICAgIDwvQnV0dG9uPlxuICAgICAgPEZvb3Rlck5hdkJhciAvPlxuICAgIDwvWVN0YWNrPlxuXG4gIClcbn0iXSwibmFtZXMiOlsiQnV0dG9uIiwiSDMiLCJYU3RhY2siLCJZU3RhY2siLCJ1c2VSb3V0ZXIiLCJWaWV3IiwiVGV4dCIsIkltYWdlIiwic3R5bGVkIiwiZXhjZWhhbmdJY29uIiwiUHJlc3NhYmxlIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDaGV2cm9uUmlnaHQiLCJGb290ZXJOYXZCYXIiLCJ1c2VUcmFuc2FjdGlvblN0b3JlIiwiZm9ybWF0VHJhbnNhY3Rpb24iLCJ1c2VXYWxsZXRTdG9yZSIsInVzZVRyYW5zbGF0aW9uIiwiZXRoSWNvbiIsIlVuZGVybGluZSIsIndpZHRoIiwiaGVpZ2h0IiwiYmFja2dyb3VuZENvbG9yIiwibXQiLCJBY3RpdmVUZXh0IiwiY29sb3IiLCJtYXJnaW5Cb3R0b20iLCJBY3RpdmVVbmRlcmxpbmUiLCJwb3NpdGlvbiIsImJvdHRvbSIsImxlZnQiLCJyaWdodCIsIkV4Y2hhbmdlU2NyZWVuIiwicm91dGVyIiwidCIsInRyYW5zYWN0aW9uU3RvcmUiLCJ3YWxsZXRTdG9yZSIsInRhYkxpc3QiLCJjdXJyZW50VGFiIiwic2V0Q3VycmVudFRhYiIsImxvYWRUcmFuc2FjdGlvbnMiLCJjdXJyZW50QWNjb3VudCIsInRyYW5zYWN0aW9ucyIsImFkZHJlc3NlcyIsImV0aCIsImFkZHJlc3MiLCJic2MiLCJidGMiLCJzb2xhbmEiLCJmaWx0ZXIiLCJCb29sZWFuIiwiZ2V0VHJhbnNhY3Rpb25zQnlBZGRyZXNzZXMiLCJncm91cFRyYW5zYWN0aW9uc0J5RGF0ZSIsImdyb3VwcyIsImZvckVhY2giLCJ0eCIsImRhdGUiLCJEYXRlIiwidGltZXN0YW1wIiwidG9kYXkiLCJ5ZXN0ZXJkYXkiLCJzZXREYXRlIiwiZ2V0RGF0ZSIsImRhdGVLZXkiLCJ0b0RhdGVTdHJpbmciLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJwdXNoIiwiZ3JvdXBlZFRyYW5zYWN0aW9ucyIsImdldENoYWluSWNvbiIsImNoYWluIiwic3JjIiwiZ2V0U3RhdHVzQ29sb3IiLCJzdGF0dXMiLCJkYXRhTGlzdCIsImlkIiwibmFtZSIsImRlc2MiLCJpc1NlbGVjdGVkIiwib25NbmVtb25pY0NsaWNrIiwiZGF0YS1hdCIsImRhdGEtaW4iLCJkYXRhLWlzIiwiYmciLCJweCIsInB5IiwibWluSGVpZ2h0IiwicGwiLCJqdXN0aWZ5Q29udGVudCIsIm9uUHJlc3MiLCJwIiwiYm9yZGVyUmFkaXVzIiwiYWxpZ25JdGVtcyIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsIm1sIiwiZ2V0UmVjZW50VHJhbnNhY3Rpb25zQ291bnQiLCJzaXplIiwic291cmNlIiwidGV4dEFsaWduIiwibWIiLCJtYXJnaW4iLCJyb3VuZGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/exchange-screen.tsx\n"));

/***/ })

});