"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/network",{

/***/ "../../packages/app/features/wallet/network-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/network-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NetworkScreen: function() { return /* binding */ NetworkScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/net1.png */ \"../../packages/assets/images/wallet/net1.png\");\n/* harmony import */ var _assets_images_wallet_net2_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/net2.png */ \"../../packages/assets/images/wallet/net2.png\");\n/* harmony import */ var _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/net3.png */ \"../../packages/assets/images/wallet/net3.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 20\n});\n_c = Underline;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst ActiveUnderline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = ActiveUnderline;\nfunction NetworkScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const tabList = [\n        \"全部\",\n        \"交换\",\n        \"赚取\",\n        \"社交媒体\",\n        \"管理\",\n        \"监听\"\n    ];\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    // 全部应用数据\n    const allAppsData = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true,\n            url: \"https://aerodrome.finance/\",\n            icon: \"https://aerodrome.finance/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"通过汇集做市交换代币并赚取费用\",\n            isSelected: false,\n            url: \"https://app.uniswap.org/swap?disableNFTs=true\",\n            icon: \"https://app.uniswap.org/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Toshi Mart\",\n            desc: \"Base 上的模因币生成器和交易平台。\",\n            isSelected: true,\n            url: \"https://toshi.fun/\",\n            icon: \"https://toshi.fun/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Matcha\",\n            desc: \"0x 开发的 DEX 聚合器\",\n            isSelected: true,\n            url: \"https://matcha.xyz/\",\n            icon: \"https://matcha.xyz/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Alien Base\",\n            desc: \"为非传统地球人而打造的 Base 原生去中心化交易所\",\n            isSelected: false,\n            url: \"https://app.alienbase.xyz/\",\n            icon: \"https://app.alienbase.xyz/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"PancakeSwap\",\n            desc: \"交易并赚取加密货币\",\n            isSelected: false,\n            url: \"https://pancakeswap.finance/\",\n            icon: \"https://pancakeswap.finance/favicon.ico\"\n        },\n        {\n            id: 7,\n            name: \"Seamless Protocol\",\n            desc: \"实现收益最大化\",\n            isSelected: true,\n            url: \"https://seamlessprotocol.com/\",\n            icon: \"https://seamlessprotocol.com/favicon.ico\"\n        },\n        {\n            id: 8,\n            name: \"Plaza Finance\",\n            desc: \"债券和杠杆\",\n            isSelected: true,\n            url: \"https://plaza.finance/\",\n            icon: \"https://plaza.finance/favicon.ico\"\n        },\n        {\n            id: 9,\n            name: \"MetaLend\",\n            desc: \"在 5 分钟内实现加密货币收益最大化\",\n            isSelected: true,\n            url: \"https://metalend.fi/\",\n            icon: \"https://metalend.fi/favicon.ico\"\n        },\n        {\n            id: 10,\n            name: \"DIMO\",\n            desc: \"更聪明地驾驶并获得奖励\",\n            isSelected: false,\n            url: \"https://dimo.zone/\",\n            icon: \"https://dimo.zone/favicon.ico\"\n        },\n        {\n            id: 11,\n            name: \"ether.fi\",\n            desc: \"通过 ether.fi 质押、消费、赚钱\",\n            isSelected: false,\n            url: \"https://www.ether.fi/\",\n            icon: \"https://www.ether.fi/favicon.ico\"\n        },\n        {\n            id: 12,\n            name: \"Moonwell\",\n            desc: \"使贷款变得简单\",\n            isSelected: false,\n            url: \"https://moonwell.fi/\",\n            icon: \"https://moonwell.fi/favicon.ico\"\n        }\n    ];\n    // 交换应用数据\n    const exchangeAppsData = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true,\n            url: \"https://aerodrome.finance/\",\n            icon: \"https://aerodrome.finance/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"通过汇集做市交换代币并赚取费用\",\n            isSelected: false,\n            url: \"https://app.uniswap.org/swap?disableNFTs=true\",\n            icon: \"https://app.uniswap.org/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Toshi Mart\",\n            desc: \"Base 上的模因币生成器和交易平台。\",\n            isSelected: true,\n            url: \"https://toshi.fun/\",\n            icon: \"https://toshi.fun/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Matcha\",\n            desc: \"0x 开发的 DEX 聚合器\",\n            isSelected: true,\n            url: \"https://matcha.xyz/\",\n            icon: \"https://matcha.xyz/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Alien Base\",\n            desc: \"为非传统地球人而打造的 Base 原生去中心化交易所\",\n            isSelected: false,\n            url: \"https://app.alienbase.xyz/\",\n            icon: \"https://app.alienbase.xyz/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"PancakeSwap\",\n            desc: \"交易并赚取加密货币\",\n            isSelected: false,\n            url: \"https://pancakeswap.finance/\",\n            icon: \"https://pancakeswap.finance/favicon.ico\"\n        }\n    ];\n    // 赚取应用数据\n    const earnAppsData = [\n        {\n            id: 1,\n            name: \"Seamless Protocol\",\n            desc: \"实现收益最大化\",\n            isSelected: true,\n            url: \"https://seamlessprotocol.com/\",\n            icon: \"https://seamlessprotocol.com/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Plaza Finance\",\n            desc: \"债券和杠杆\",\n            isSelected: true,\n            url: \"https://plaza.finance/\",\n            icon: \"https://plaza.finance/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"MetaLend\",\n            desc: \"在 5 分钟内实现加密货币收益最大化\",\n            isSelected: true,\n            url: \"https://metalend.fi/\",\n            icon: \"https://metalend.fi/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"ether.fi\",\n            desc: \"通过 ether.fi 质押、消费、赚钱\",\n            isSelected: false,\n            url: \"https://www.ether.fi/\",\n            icon: \"https://www.ether.fi/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Moonwell\",\n            desc: \"使贷款变得简单\",\n            isSelected: false,\n            url: \"https://moonwell.fi/\",\n            icon: \"https://moonwell.fi/favicon.ico\"\n        }\n    ];\n    // 社交媒体应用数据\n    const socialMediaAppsData = [\n        {\n            id: 1,\n            name: \"moshi.cam\",\n            desc: \"链上照片共享应用程序\",\n            isSelected: false,\n            url: \"https://moshi.cam/\",\n            icon: \"https://moshi.cam/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Virtuals\",\n            desc: \"链上 AI 代理协会\",\n            isSelected: true,\n            url: \"https://virtuals.io/\",\n            icon: \"https://virtuals.io/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Hypersub\",\n            desc: \"订阅并赚取\",\n            isSelected: false,\n            url: \"https://hypersub.withfabric.xyz/\",\n            icon: \"https://hypersub.withfabric.xyz/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Soulbound TV\",\n            desc: \"实现电视去中心化，新一代直播平台。\",\n            isSelected: false,\n            url: \"https://soulbound.tv/\",\n            icon: \"https://soulbound.tv/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Rad TV\",\n            desc: \"为创作者和粉丝提供链上视频流\",\n            isSelected: false,\n            url: \"https://rad.tv/\",\n            icon: \"https://rad.tv/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"Aura\",\n            desc: \"个人链上克隆体\",\n            isSelected: false,\n            url: \"https://aura.network/\",\n            icon: \"https://aura.network/favicon.ico\"\n        }\n    ];\n    // 管理应用数据\n    const managementAppsData = [\n        {\n            id: 1,\n            name: \"Onboard\",\n            desc: \"成为 Onboard 商家，每天最多可赚取 100...\",\n            isSelected: true,\n            url: \"https://onboard.xyz/\",\n            icon: \"https://onboard.xyz/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Webacy\",\n            desc: \"管理钱包安全。\",\n            isSelected: false,\n            url: \"https://webacy.com/\",\n            icon: \"https://webacy.com/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Dune\",\n            desc: \"在 Base 上开发应用程序\",\n            isSelected: false,\n            url: \"https://dune.com/\",\n            icon: \"https://dune.com/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Venice\",\n            desc: \"私有且不受审查的人工智能\",\n            isSelected: false,\n            url: \"https://venice.ai/\",\n            icon: \"https://venice.ai/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Quip Network\",\n            desc: \"保护您的资产免受量子计算机黑客的攻击\",\n            isSelected: false,\n            url: \"https://quip.network/\",\n            icon: \"https://quip.network/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"Daos.world\",\n            desc: \"Dao 将吞噬世界\",\n            isSelected: false,\n            url: \"https://daos.world/\",\n            icon: \"https://daos.world/favicon.ico\"\n        }\n    ];\n    // 监听应用数据\n    const monitoringAppsData = [\n        {\n            id: 1,\n            name: \"DIMO\",\n            desc: \"更聪明地驾驶并获得奖励\",\n            isSelected: false,\n            url: \"https://dimo.zone/\",\n            icon: \"https://dimo.zone/favicon.ico\"\n        }\n    ];\n    // 根据当前标签获取对应的数据\n    const getCurrentTabData = ()=>{\n        switch(currentTab){\n            case 0:\n                return allAppsData;\n            case 1:\n                return exchangeAppsData;\n            case 2:\n                return earnAppsData;\n            case 3:\n                return socialMediaAppsData;\n            case 4:\n                return managementAppsData;\n            case 5:\n                return monitoringAppsData;\n            default:\n                return allAppsData;\n        }\n    };\n    const dataList = getCurrentTabData();\n    const handleOpenUrl = (url)=>{\n        window.location.href = url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"network-screen.tsx:386\",\n        \"data-in\": \"NetworkScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        minHeight: \"100vh\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"network-screen.tsx:387\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                alignItems: \"center\",\n                mb: 32,\n                justifyContent: \"space-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                        title: \"\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                        \"data-at\": \"network-screen.tsx:389\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Input\",\n                        placeholder: \"搜索或输入网址\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"network-screen.tsx:390\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"View\",\n                        flexDirection: \"row\",\n                        justifyContent: \"space-between\",\n                        ml: 30,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:392\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:395\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net2_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:398\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"network-screen.tsx:403-412\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                bg: \"#02A9DE\",\n                width: 343,\n                height: 80,\n                borderRadius: 20,\n                margin: \"auto\",\n                pt: 6,\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                        \"data-at\": \"network-screen.tsx:413\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 70,\n                        height: 70,\n                        mr: 10\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"network-screen.tsx:414\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"View\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"network-screen.tsx:415\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$black1\",\n                                fontWeight: \"bold\",\n                                fontSize: 14,\n                                children: \"用作资金的免费 NFT\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"network-screen.tsx:418\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$black1\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mt: 4,\n                                children: \"获取特别的 NFT 来为钱包注入资金。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"network-screen.tsx:425\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 20,\n                rowGap: 16,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                        \"data-at\": \"network-screen.tsx:426\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Text\",\n                        children: \"热门应用\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"network-screen.tsx:427\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        children: tabList.map((i, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>setCurrentTab(index),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                    \"data-at\": \"network-screen.tsx:430\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"View\",\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: i\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 41\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            \"data-at\": \"network-screen.tsx:434\",\n                                            \"data-in\": \"NetworkScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: i\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 72\n                                        }, this),\n                                        currentTab === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveUnderline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 42\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this)\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 38\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"network-screen.tsx:441\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"YStack\",\n                        py: 10,\n                        children: dataList.map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"network-screen.tsx:443\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                mb: 16,\n                                onPress: ()=>handleOpenUrl(i.url),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                        \"data-at\": \"network-screen.tsx:444-451\",\n                                        \"data-in\": \"NetworkScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: {\n                                            uri: i.icon\n                                        },\n                                        width: 40,\n                                        height: 40,\n                                        rounded: 20,\n                                        mr: 10,\n                                        bg: \"$white1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                        \"data-at\": \"network-screen.tsx:452\",\n                                        \"data-in\": \"NetworkScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                                \"data-at\": \"network-screen.tsx:453\",\n                                                \"data-in\": \"NetworkScreen\",\n                                                \"data-is\": \"XStack\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                        \"data-at\": \"network-screen.tsx:454\",\n                                                        \"data-in\": \"NetworkScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        mr: 20,\n                                                        children: i.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    i.isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                        \"data-at\": \"network-screen.tsx:456-464\",\n                                                        \"data-in\": \"NetworkScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        bg: \"#141519\",\n                                                        rounded: 10,\n                                                        width: 55,\n                                                        height: 22,\n                                                        fontSize: 12,\n                                                        textAlign: \"center\",\n                                                        lineHeight: 22,\n                                                        children: \"精选\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 36\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"network-screen.tsx:469\",\n                                                \"data-in\": \"NetworkScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"$color10\",\n                                                fontSize: 14,\n                                                mt: 11,\n                                                children: i.desc\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, i.id, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 30\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_9__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n        lineNumber: 326,\n        columnNumber: 10\n    }, this);\n}\n_s(NetworkScreen, \"k5oVBT4gVizGZi8FjIw9Gkdgvrs=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c3 = NetworkScreen;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"ActiveUnderline\");\n$RefreshReg$(_c3, \"NetworkScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/network-screen.tsx\n"));

/***/ })

});