"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/noticeSetting",{

/***/ "../../packages/app/features/wallet/notice-seeting-screen.tsx":
/*!********************************************************************!*\
  !*** ../../packages/app/features/wallet/notice-seeting-screen.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoticeSettingScreen: function() { return /* binding */ NoticeSettingScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction NoticeSettingScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 通知开关状态管理\n    const [productAnnouncement, setProductAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [insightsAndTips, setInsightsAndTips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [specialOffers, setSpecialOffers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [priceAlerts, setPriceAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [btcPriceAlerts, setBtcPriceAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [ethPriceAlerts, setEthPriceAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [nftOfferAlerts, setNftOfferAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [accountActivity, setAccountActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n        \"data-at\": \"notice-seeting-screen.tsx:25\",\n        \"data-in\": \"NoticeSettingScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                \"data-at\": \"notice-seeting-screen.tsx:26\",\n                \"data-in\": \"NoticeSettingScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.NavBar, {\n                    title: \"通知\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n                \"data-at\": \"notice-seeting-screen.tsx:29\",\n                \"data-in\": \"NoticeSettingScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 10,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:30\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:31\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:32\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"产品公告\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:33\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"率先了解最新功能\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:35-44\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: productAnnouncement ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: productAnnouncement ? \"#3873F5\" : \"#3A3D44\",\n                                checked: productAnnouncement,\n                                onCheckedChange: setProductAnnouncement,\n                                style: {\n                                    backgroundColor: productAnnouncement ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:48\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:49\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:50\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 15,\n                                    fontWeight: \"bold\",\n                                    children: \"见解和技巧\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:52-61\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: insightsAndTips ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: insightsAndTips ? \"#3873F5\" : \"#3A3D44\",\n                                checked: insightsAndTips,\n                                onCheckedChange: setInsightsAndTips,\n                                style: {\n                                    backgroundColor: insightsAndTips ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:65\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:66\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:67\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 15,\n                                    fontWeight: \"bold\",\n                                    children: \"特别优惠\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:69-78\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: specialOffers ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: specialOffers ? \"#3873F5\" : \"#3A3D44\",\n                                checked: specialOffers,\n                                onCheckedChange: setSpecialOffers,\n                                style: {\n                                    backgroundColor: specialOffers ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:82\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:83\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:84\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:85\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"资产价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:87-96\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: priceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: priceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                checked: priceAlerts,\n                                onCheckedChange: setPriceAlerts,\n                                style: {\n                                    backgroundColor: priceAlerts ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:100\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:101\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:102\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"BTC 价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:103\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"BTC 价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:105-114\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: btcPriceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: btcPriceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                checked: btcPriceAlerts,\n                                onCheckedChange: setBtcPriceAlerts,\n                                style: {\n                                    backgroundColor: btcPriceAlerts ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:118\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:119\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:120\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"ETH 价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:121\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"ETH 价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:123-132\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: ethPriceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: ethPriceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                checked: ethPriceAlerts,\n                                onCheckedChange: setEthPriceAlerts,\n                                style: {\n                                    backgroundColor: ethPriceAlerts ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:136\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:137\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                width: \"80%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:138\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"NFT 报价提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:139\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"当有人对您的 NFT 出价时收到通知。您不会 收到极低报价的通知。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:142-151\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: nftOfferAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: nftOfferAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                checked: nftOfferAlerts,\n                                onCheckedChange: setNftOfferAlerts,\n                                style: {\n                                    backgroundColor: nftOfferAlerts ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:155\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:156\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:157\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 15,\n                                    fontWeight: \"bold\",\n                                    children: \"账户活动\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:159\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:160\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n        lineNumber: 21,\n        columnNumber: 10\n    }, this);\n}\n_s(NoticeSettingScreen, \"bOnEtxfGudAFZPFveHmZwIvscQ8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NoticeSettingScreen;\nvar _c;\n$RefreshReg$(_c, \"NoticeSettingScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/notice-seeting-screen.tsx\n"));

/***/ })

});