"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/network",{

/***/ "../../packages/app/features/home/<USER>":
/*!*****************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomePage: function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/close.png */ \"../../packages/assets/images/close.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/mint1.png */ \"../../packages/assets/images/mint1.png\");\n/* harmony import */ var _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/mint2.png */ \"../../packages/assets/images/mint2.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Underline;\nconst ActiveBlock = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#fff\",\n    borderRadius: 10\n});\n_c1 = ActiveBlock;\nconst Block = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#262729\",\n    borderRadius: 10\n});\n_c2 = Block;\nconst SwiperContainer = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    position: \"relative\",\n    overflow: \"hidden\",\n    width: \"100%\"\n});\n_c3 = SwiperContainer;\nconst SwiperWrapper = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    display: \"flex\",\n    flexDirection: \"row\",\n    transition: \"transform 0.3s ease\",\n    width: \"400%\" // 4页内容，每页100%\n});\n_c4 = SwiperWrapper;\nconst SwiperSlide = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"25%\",\n    // 每个slide占25%（因为总宽度是400%）\n    flexShrink: 0\n});\n_c5 = SwiperSlide;\nfunction HomePage(param) {\n    let { pagesMode = false } = param;\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const totalPages = 4;\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n    // 处理触摸开始\n    const handleTouchStart = (e)=>{\n        setTouchEnd(0); // 重置touchEnd\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    // 处理触摸移动\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    // 处理触摸结束\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && currentPage < totalPages - 1) {\n            setCurrentPage(currentPage + 1);\n        }\n        if (isRightSwipe && currentPage > 0) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    // 轮播内容数据\n    const swiperData = [\n        {\n            id: 1,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.1\") || \"获得 4.1% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Base\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.1\") || \"将 Base 上的 USDC 添加到您的钱包，每年可赚取 4.1% 的奖励\"\n        },\n        {\n            id: 2,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"3.8\") || \"获得 3.8% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Ethereum\").replace(\"{token}\", \"USDT\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"3.8\") || \"将 Ethereum 上的 USDT 添加到您的钱包，每年可赚取 3.8% 的奖励\"\n        },\n        {\n            id: 3,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"5.2\") || \"获得 5.2% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Polygon\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"5.2\") || \"将 Polygon 上的 USDC 添加到您的钱包，每年可赚取 5.2% 的奖励\"\n        },\n        {\n            id: 4,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.5\") || \"获得 4.5% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Arbitrum\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.5\") || \"将 Arbitrum 上的 USDC 添加到您的钱包，每年可赚取 4.5% 的奖励\"\n        }\n    ];\n    // Trending swaps 测试数据\n    const trendingSwapsData = [\n        {\n            id: 1,\n            name: \"KTA\",\n            price: \"US$0.54\",\n            change: \"15.51%\",\n            changeColor: \"#C7545E\",\n            swaps: 681,\n            buyPercentage: 70,\n            sellPercentage: 30\n        },\n        {\n            id: 2,\n            name: \"DEGEN\",\n            price: \"US$0.012\",\n            change: \"+8.23%\",\n            changeColor: \"#2FAB77\",\n            swaps: 47,\n            buyPercentage: 65,\n            sellPercentage: 35\n        },\n        {\n            id: 3,\n            name: \"HIGHER\",\n            price: \"US$0.089\",\n            change: \"-3.45%\",\n            changeColor: \"#C7545E\",\n            swaps: 82,\n            buyPercentage: 45,\n            sellPercentage: 55\n        },\n        {\n            id: 4,\n            name: \"BALD\",\n            price: \"US$0.0034\",\n            change: \"+12.67%\",\n            changeColor: \"#2FAB77\",\n            swaps: 156,\n            buyPercentage: 78,\n            sellPercentage: 22\n        }\n    ];\n    const handleAction = (action)=>{\n        if (action === \"watchlist\") {\n            router.push(\"/user/myAttention\");\n        }\n    };\n    // 处理外部链接点击\n    const handleExternalLink = (url)=>{\n        react_native__WEBPACK_IMPORTED_MODULE_9__.Linking.openURL(url).catch((err)=>console.error(\"Failed to open URL:\", err));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n        \"data-at\": \"homePage.tsx:182\",\n        \"data-in\": \"HomePage\",\n        \"data-is\": \"YStack\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                \"data-at\": \"homePage.tsx:184\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"homePage.tsx:185\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        mt: 20,\n                        flex: 1,\n                        alignContent: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                                \"data-at\": \"homePage.tsx:186\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"XStack\",\n                                gap: \"$2\",\n                                children: [\n                                    0,\n                                    1,\n                                    2,\n                                    3\n                                ].map((index)=>index <= currentPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveBlock, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 156,\n                                        columnNumber: 63\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Block, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 156,\n                                        columnNumber: 93\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:191\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                style: {\n                                    width: 12,\n                                    height: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperContainer, {\n                        onTouchStart: handleTouchStart,\n                        onTouchMove: handleTouchMove,\n                        onTouchEnd: handleTouchEnd,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperWrapper, {\n                            style: {\n                                transform: \"translateX(-\".concat(currentPage * 25, \"%)\")\n                            },\n                            children: swiperData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperSlide, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                                        \"data-at\": \"homePage.tsx:205-212\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 10,\n                                        flex: 1,\n                                        gap: \"$4\",\n                                        alignContent: \"center\",\n                                        justifyContent: \"space-between\",\n                                        mb: 10,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                \"data-at\": \"homePage.tsx:213\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Image\",\n                                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                                style: {\n                                                    width: 50,\n                                                    height: 50\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                                \"data-at\": \"homePage.tsx:214\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"YStack\",\n                                                flex: 1,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:215\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#fff\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:218\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#8B8F9A\",\n                                                        fontSize: 12,\n                                                        children: item.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 167,\n                                    columnNumber: 46\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                \"data-at\": \"homePage.tsx:229\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                onPress: ()=>handleAction(\"watchlist\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:230\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        color: \"white\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"Watchlist\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"homePage.tsx:233-242\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        bg: \"#141519\",\n                        borderRadius: 10,\n                        p: 10,\n                        mt: 10,\n                        height: 70,\n                        flex: 1,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                \"data-at\": \"homePage.tsx:243\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"YStack\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:244\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"创建“我的关注”\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:247\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        mt: 6,\n                                        children: \"获取价格提醒并了解最新信息\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:251\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                style: {\n                                    width: 70,\n                                    height: 37\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                \"data-at\": \"homePage.tsx:255\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:256\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        color: \"white\",\n                        fontWeight: \"bold\",\n                        children: \"Trending swaps on Base\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.ScrollView, {\n                        \"data-at\": \"homePage.tsx:259\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"ScrollView\",\n                        horizontal: true,\n                        showsHorizontalScrollIndicator: false,\n                        mt: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                            \"data-at\": \"homePage.tsx:260\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"XStack\",\n                            gap: \"$3\",\n                            children: trendingSwapsData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                    \"data-at\": \"homePage.tsx:262\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"View\",\n                                    width: 260,\n                                    height: 180,\n                                    borderRadius: 10,\n                                    bg: \"#141519\",\n                                    px: 14,\n                                    py: 14,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                        \"data-at\": \"homePage.tsx:263\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"YStack\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                                                \"data-at\": \"homePage.tsx:264\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                mb: \"$4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                        \"data-at\": \"homePage.tsx:265-268\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Image\",\n                                                        source: {\n                                                            uri: \"\"\n                                                        },\n                                                        style: {\n                                                            width: 28,\n                                                            height: 28,\n                                                            borderRadius: \"50%\",\n                                                            background: \"#2B2B2B\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:269\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        ml: \"$2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                \"data-at\": \"homePage.tsx:270\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"Text\",\n                                                                color: \"white\",\n                                                                fontSize: 14,\n                                                                fontWeight: \"bold\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                \"data-at\": \"homePage.tsx:273\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"Text\",\n                                                                color: \"#8B8F9A\",\n                                                                fontSize: 12,\n                                                                fontWeight: 500,\n                                                                mt: 6,\n                                                                children: item.price\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:277\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        style: {\n                                                            color: item.changeColor\n                                                        },\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        ml: \"$4\",\n                                                        children: item.change\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                                                \"data-at\": \"homePage.tsx:281\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                alignItems: \"center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:282\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"white\",\n                                                        fontSize: 20,\n                                                        fontWeight: \"bold\",\n                                                        children: item.swaps\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:285\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"white\",\n                                                        fontSize: 16,\n                                                        fontWeight: \"bold\",\n                                                        ml: \"$1\",\n                                                        children: \"兑换\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:288-297\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        bg: \"#282B32\",\n                                                        width: 137,\n                                                        height: 34,\n                                                        borderRadius: 20,\n                                                        ml: \"$5\",\n                                                        onPress: ()=>{\n                                                            router.push(\"/wallet/convert\");\n                                                        },\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                            \"data-at\": \"homePage.tsx:298\",\n                                                            \"data-in\": \"HomePage\",\n                                                            \"data-is\": \"Text\",\n                                                            color: \"white\",\n                                                            text: \"center\",\n                                                            lineHeight: 34,\n                                                            children: \"兑换\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 245,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                                                \"data-at\": \"homePage.tsx:303\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                mt: 20,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:304-309\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        width: Math.round(224 * item.buyPercentage / 100),\n                                                        height: 4,\n                                                        bg: \"#2FAB77\",\n                                                        borderRadius: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:310-315\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        width: Math.round(224 * item.sellPercentage / 100),\n                                                        height: 4,\n                                                        bg: \"#C7545E\",\n                                                        borderRadius: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                                                \"data-at\": \"homePage.tsx:317\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                mt: 20,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                                                        \"data-at\": \"homePage.tsx:318\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"XStack\",\n                                                        flex: 1,\n                                                        alignItems: \"center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                                \"data-at\": \"homePage.tsx:319\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"View\",\n                                                                width: 6,\n                                                                height: 6,\n                                                                bg: \"#2FAB77\",\n                                                                borderRadius: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                \"data-at\": \"homePage.tsx:320\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"Text\",\n                                                                fontSize: 12,\n                                                                color: \"#8B8F9A\",\n                                                                ml: \"$2\",\n                                                                children: [\n                                                                    \"已购买 \",\n                                                                    item.buyPercentage,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                                                        \"data-at\": \"homePage.tsx:324\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"XStack\",\n                                                        flex: 1,\n                                                        alignItems: \"center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                                \"data-at\": \"homePage.tsx:325\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"View\",\n                                                                width: 6,\n                                                                height: 6,\n                                                                bg: \"#C7545E\",\n                                                                borderRadius: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                \"data-at\": \"homePage.tsx:326\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"Text\",\n                                                                fontSize: 12,\n                                                                color: \"#8B8F9A\",\n                                                                ml: \"$2\",\n                                                                children: [\n                                                                    \"已售出 \",\n                                                                    item.sellPercentage,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 214,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 213,\n                                    columnNumber: 44\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                \"data-at\": \"homePage.tsx:338\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:339\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    onPress: ()=>handleExternalLink(\"https://bridge.base.org\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:340\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:341\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 12,\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Sponsored\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:344\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"In-App Bridging is Here\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:347\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"For when you really, really want that one token. Onthat other chain.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                            \"data-at\": \"homePage.tsx:350\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"XStack\",\n                            mt: \"$4\",\n                            alignItems: \"center\",\n                            gap: \"$5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                    \"data-at\": \"homePage.tsx:351-358\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"View\",\n                                    bg: \"#282B32\",\n                                    width: 137,\n                                    height: 34,\n                                    borderRadius: 20,\n                                    alignItems: \"center\",\n                                    onPress: ()=>handleExternalLink(\"https://bridge.base.org\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:359\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        lineHeight: 34,\n                                        children: \"Learn More\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                    \"data-at\": \"homePage.tsx:363\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 12,\n                                    fontWeight: \"bold\",\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 305,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                \"data-at\": \"homePage.tsx:370\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:371\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:372\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            mb: 10,\n                            children: \"Trending onchain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:375\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:376\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Drifters\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:379\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"Drifters are handcrafted, fully customiz..\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 324,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 151,\n        columnNumber: 10\n    }, this);\n}\n_s(HomePage, \"W+W+6QFeS0A5s7b4o2uYY16EQGU=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c6 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveBlock\");\n$RefreshReg$(_c2, \"Block\");\n$RefreshReg$(_c3, \"SwiperContainer\");\n$RefreshReg$(_c4, \"SwiperWrapper\");\n$RefreshReg$(_c5, \"SwiperSlide\");\n$RefreshReg$(_c6, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>"));

/***/ })

});