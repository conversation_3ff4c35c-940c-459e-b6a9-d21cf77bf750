"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/exchange",{

/***/ "../../packages/app/features/wallet/exchange-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/exchange-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExchangeScreen: function() { return /* binding */ ExchangeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/exchange.png */ \"../../packages/assets/images/wallet/exchange.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ExchangeScreen() {\n    _s();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // 初始化交易数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        transactionStore.loadTransactions();\n    }, []);\n    // 获取当前账户的所有链的交易\n    const currentAccount = walletStore.currentAccount;\n    const transactions = currentAccount ? (()=>{\n        var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n        // 获取所有链的地址\n        const addresses = [\n            (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n            (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n            (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n            (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n        ].filter(Boolean); // 过滤掉undefined/null值\n        // 使用新的去重方法获取交易记录\n        return transactionStore.getTransactionsByAddresses(addresses);\n    })() : [];\n    // 按日期分组交易\n    const groupTransactionsByDate = (transactions)=>{\n        const groups = {};\n        transactions.forEach((tx)=>{\n            const date = new Date(tx.timestamp);\n            const today = new Date();\n            const yesterday = new Date(today);\n            yesterday.setDate(yesterday.getDate() - 1);\n            let dateKey = \"\";\n            if (date.toDateString() === today.toDateString()) {\n                dateKey = t(\"time.today\") || \"今天\";\n            } else if (date.toDateString() === yesterday.toDateString()) {\n                dateKey = t(\"time.yesterday\") || \"昨天\";\n            } else {\n                dateKey = date.toLocaleDateString();\n            }\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push((0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.formatTransaction)(tx));\n        });\n        return groups;\n    };\n    const groupedTransactions = groupTransactionsByDate(transactions);\n    // 获取链图标\n    const getChainIcon = (chain)=>{\n        switch(chain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            // 暂时使用同一个图标\n            case \"btc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            default:\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n        }\n    };\n    if (transactionStore.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n            \"data-at\": \"exchange-screen.tsx:88\",\n            \"data-in\": \"ExchangeScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                \"data-at\": \"exchange-screen.tsx:89\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"加载中...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n            lineNumber: 78,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n        \"data-at\": \"exchange-screen.tsx:95\",\n        \"data-in\": \"ExchangeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        flex: 1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                \"data-at\": \"exchange-screen.tsx:96\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 30,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"exchange-screen.tsx:97\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"XStack\",\n                        pl: 16,\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.H3, {\n                                \"data-at\": \"exchange-screen.tsx:98\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"H3\",\n                                children: \"交易\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:99\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"#4575FF\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mr: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:105\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        flex: 1,\n                        mb: currentAccount ? 80 : 140,\n                        children: Object.keys(groupedTransactions).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                            \"data-at\": \"exchange-screen.tsx:107\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"YStack\",\n                            flex: 1,\n                            justifyContent: \"center\",\n                            mt: 0,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                    \"data-at\": \"exchange-screen.tsx:108\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"XStack\",\n                                    justifyContent: \"center\",\n                                    mt: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                        \"data-at\": \"exchange-screen.tsx:109\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                        width: 173,\n                                        height: 142\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                    \"data-at\": \"exchange-screen.tsx:111\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"YStack\",\n                                    justifyContent: \"center\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:112\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$white1\",\n                                            fontSize: 16,\n                                            fontWeight: \"bold\",\n                                            textAlign: \"center\",\n                                            mb: 10,\n                                            children: \"还没有交易\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:113\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$accent11\",\n                                            width: 280,\n                                            textAlign: \"center\",\n                                            margin: \"auto\",\n                                            children: \"一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 60\n                        }, this) : Object.entries(groupedTransactions).map((param)=>{\n                            let [date, txs] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                \"data-at\": \"exchange-screen.tsx:118\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"YStack\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                        \"data-at\": \"exchange-screen.tsx:119\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mb: 10,\n                                        children: date\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this),\n                                    txs.map((tx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                            \"data-at\": \"exchange-screen.tsx:123\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"XStack\",\n                                            justifyContent: \"space-between\",\n                                            mt: 25,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:124\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"XStack\",\n                                                    alignItems: \"center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:125\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            position: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                                                \"data-at\": \"exchange-screen.tsx:126\",\n                                                                \"data-in\": \"ExchangeScreen\",\n                                                                \"data-is\": \"Image\",\n                                                                source: getChainIcon(tx.chain),\n                                                                width: 38,\n                                                                height: 38\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:128\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            ml: 10,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:129\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 14,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"white\",\n                                                                    children: (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.getTypeDisplayText)(tx.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 111,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:132\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 12,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"$accent11\",\n                                                                    mt: 2,\n                                                                    children: tx.displayAddress\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:137\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"YStack\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"flex-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:138\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 14,\n                                                            fontWeight: \"bold\",\n                                                            color: \"white\",\n                                                            children: tx.displayAmount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:141\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 12,\n                                                            fontWeight: \"bold\",\n                                                            color: \"$accent11\",\n                                                            mt: 2,\n                                                            children: tx.displayTime\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        tx.txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:145\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 10,\n                                                            color: \"$accent11\",\n                                                            mt: 1,\n                                                            children: [\n                                                                tx.txHash.slice(0, 8),\n                                                                \"...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 36\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, tx.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 32\n                                        }, this))\n                                ]\n                            }, date, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 82\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    currentAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:159\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        position: \"absolute\",\n                        bottom: 90,\n                        left: 16,\n                        right: 16,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            \"data-at\": \"exchange-screen.tsx:160\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Button\",\n                            rounded: 30,\n                            width: \"100%\",\n                            bg: \"$accent11\",\n                            onPress: ()=>router.push(\"/wallet/buyCoin\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:161\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$white1\",\n                                children: \"将加密货币添加到您的钱包\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 28\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_10__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n        lineNumber: 82,\n        columnNumber: 10\n    }, this);\n}\n_s(ExchangeScreen, \"q//dzex2CKMy1P3Zhz+bD7rYdYw=\", false, function() {\n    return [\n        app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = ExchangeScreen;\nvar _c;\n$RefreshReg$(_c, \"ExchangeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/exchange-screen.tsx\n"));

/***/ })

});