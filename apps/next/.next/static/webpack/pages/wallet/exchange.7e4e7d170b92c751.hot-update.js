"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/exchange",{

/***/ "../../packages/app/features/wallet/exchange-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/exchange-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExchangeScreen: function() { return /* binding */ ExchangeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/exchange.png */ \"../../packages/assets/images/wallet/exchange.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ExchangeScreen() {\n    _s();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // 初始化交易数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        transactionStore.loadTransactions();\n    }, []);\n    // 获取当前账户的所有链的交易\n    const currentAccount = walletStore.currentAccount;\n    const transactions = currentAccount ? (()=>{\n        var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n        // 获取所有链的地址\n        const addresses = [\n            (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n            (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n            (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n            (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n        ].filter(Boolean); // 过滤掉undefined/null值\n        // 使用新的去重方法获取交易记录\n        return transactionStore.getTransactionsByAddresses(addresses);\n    })() : [];\n    // 按日期分组交易\n    const groupTransactionsByDate = (transactions)=>{\n        const groups = {};\n        transactions.forEach((tx)=>{\n            const date = new Date(tx.timestamp);\n            const today = new Date();\n            const yesterday = new Date(today);\n            yesterday.setDate(yesterday.getDate() - 1);\n            let dateKey = \"\";\n            if (date.toDateString() === today.toDateString()) {\n                dateKey = t(\"time.today\") || \"今天\";\n            } else if (date.toDateString() === yesterday.toDateString()) {\n                dateKey = t(\"time.yesterday\") || \"昨天\";\n            } else {\n                dateKey = date.toLocaleDateString();\n            }\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push((0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.formatTransaction)(tx));\n        });\n        return groups;\n    };\n    const groupedTransactions = groupTransactionsByDate(transactions);\n    // 获取链图标\n    const getChainIcon = (chain)=>{\n        switch(chain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            // 暂时使用同一个图标\n            case \"btc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            default:\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n        }\n    };\n    if (transactionStore.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n            \"data-at\": \"exchange-screen.tsx:88\",\n            \"data-in\": \"ExchangeScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                \"data-at\": \"exchange-screen.tsx:89\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"加载中...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n            lineNumber: 78,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n        \"data-at\": \"exchange-screen.tsx:95\",\n        \"data-in\": \"ExchangeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        flex: 1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                \"data-at\": \"exchange-screen.tsx:96\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 30,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"exchange-screen.tsx:97\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"XStack\",\n                        pl: 16,\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.H3, {\n                                \"data-at\": \"exchange-screen.tsx:98\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"H3\",\n                                children: \"交易\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:99\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"#4575FF\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mr: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:105\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        flex: 1,\n                        mb: currentAccount ? 80 : 140,\n                        children: Object.keys(groupedTransactions).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                            \"data-at\": \"exchange-screen.tsx:107\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"YStack\",\n                            flex: 1,\n                            alignItems: \"center\",\n                            mt: 0,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                    \"data-at\": \"exchange-screen.tsx:108\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"XStack\",\n                                    justifyContent: \"center\",\n                                    mt: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                        \"data-at\": \"exchange-screen.tsx:109\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                        width: 173,\n                                        height: 142\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                    \"data-at\": \"exchange-screen.tsx:111\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"YStack\",\n                                    justifyContent: \"center\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:112\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$white1\",\n                                            fontSize: 16,\n                                            fontWeight: \"bold\",\n                                            textAlign: \"center\",\n                                            mb: 10,\n                                            children: \"还没有交易\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:113\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$accent11\",\n                                            width: 280,\n                                            textAlign: \"center\",\n                                            margin: \"auto\",\n                                            children: \"一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 60\n                        }, this) : Object.entries(groupedTransactions).map((param)=>{\n                            let [date, txs] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                \"data-at\": \"exchange-screen.tsx:118\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"YStack\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                        \"data-at\": \"exchange-screen.tsx:119\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mb: 10,\n                                        children: date\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this),\n                                    txs.map((tx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                            \"data-at\": \"exchange-screen.tsx:123\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"XStack\",\n                                            justifyContent: \"space-between\",\n                                            mt: 25,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:124\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"XStack\",\n                                                    alignItems: \"center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:125\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            position: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                                                \"data-at\": \"exchange-screen.tsx:126\",\n                                                                \"data-in\": \"ExchangeScreen\",\n                                                                \"data-is\": \"Image\",\n                                                                source: getChainIcon(tx.chain),\n                                                                width: 38,\n                                                                height: 38\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:128\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            ml: 10,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:129\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 14,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"white\",\n                                                                    children: (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.getTypeDisplayText)(tx.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 111,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:132\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 12,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"$accent11\",\n                                                                    mt: 2,\n                                                                    children: tx.displayAddress\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 114,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:137\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"YStack\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"flex-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:138\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 14,\n                                                            fontWeight: \"bold\",\n                                                            color: \"white\",\n                                                            children: tx.displayAmount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:141\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 12,\n                                                            fontWeight: \"bold\",\n                                                            color: \"$accent11\",\n                                                            mt: 2,\n                                                            children: tx.displayTime\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        tx.txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:145\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 10,\n                                                            color: \"$accent11\",\n                                                            mt: 1,\n                                                            children: [\n                                                                tx.txHash.slice(0, 8),\n                                                                \"...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 36\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, tx.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 32\n                                        }, this))\n                                ]\n                            }, date, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 82\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    currentAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:159\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        position: \"absolute\",\n                        bottom: 90,\n                        left: 16,\n                        right: 16,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            \"data-at\": \"exchange-screen.tsx:160\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Button\",\n                            rounded: 30,\n                            width: \"100%\",\n                            bg: \"$accent11\",\n                            onPress: ()=>router.push(\"/wallet/buyCoin\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:161\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$white1\",\n                                children: \"将加密货币添加到您的钱包\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 28\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_10__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n        lineNumber: 82,\n        columnNumber: 10\n    }, this);\n}\n_s(ExchangeScreen, \"q//dzex2CKMy1P3Zhz+bD7rYdYw=\", false, function() {\n    return [\n        app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = ExchangeScreen;\nvar _c;\n$RefreshReg$(_c, \"ExchangeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL3dhbGxldC9leGNoYW5nZS1zY3JlZW4udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBbUQ7QUFDUjtBQUMwQjtBQUVwQztBQUNZO0FBQzJEO0FBQ2pEO0FBQ2Q7QUFDa0I7QUFDZDtBQUV0QyxTQUFTaUI7O0lBQ2QsTUFBTSxFQUFFQyxDQUFBQSxFQUFHLEdBQUdKLHdEQUFjQTtJQUM1QixNQUFNSyxtQkFBbUJULGdGQUFtQkE7SUFDNUMsTUFBTVUsY0FBY1Asc0VBQWNBO0lBQ2xDLE1BQU1RLFNBQVNMLDREQUFTQTtJQUV4QjtJQUNBUixnREFBU0EsQ0FBQztRQUNSVyxpQkFBaUJHLGdCQUFnQjtJQUNuQyxHQUFHLEVBQUU7SUFFTDtJQUNBLE1BQU1DLGlCQUFpQkgsWUFBWUcsY0FBYztJQUNqRCxNQUFNQyxlQUFlRCxpQkFDakIsQ0FBQztZQUdDQSxxQkFDQUEscUJBQ0FBLHFCQUNBQTtRQUxGO1FBQ0EsTUFBTUUsWUFBWTthQUNoQkYsc0JBQUFBLGVBQWVHLEdBQUcsY0FBbEJILDBDQUFBQSxvQkFBb0JJLE9BQU87YUFDM0JKLHNCQUFBQSxlQUFlSyxHQUFHLGNBQWxCTCwwQ0FBQUEsb0JBQW9CSSxPQUFPO2FBQzNCSixzQkFBQUEsZUFBZU0sR0FBRyxjQUFsQk4sMENBQUFBLG9CQUFvQkksT0FBTzthQUMzQkoseUJBQUFBLGVBQWVPLE1BQU0sY0FBckJQLDZDQUFBQSx1QkFBdUJJLE9BQU87U0FDL0IsQ0FBQ0ksTUFBTSxDQUFDQyxVQUFTO1FBRWxCO1FBQ0EsT0FBT2IsaUJBQWlCYywwQkFBMEIsQ0FBQ1I7SUFDckQsT0FDRSxFQUFFO0lBRU47SUFDQSxNQUFNUywwQkFBMEJBLENBQUNWO1FBQy9CLE1BQU1XLFNBQW1DLENBQUM7UUFFMUNYLGFBQWFZLE9BQU8sQ0FBRUMsQ0FBQUE7WUFDcEIsTUFBTUMsT0FBTyxJQUFJQyxLQUFLRixHQUFHRyxTQUFTO1lBQ2xDLE1BQU1DLFFBQVEsSUFBSUY7WUFDbEIsTUFBTUcsWUFBWSxJQUFJSCxLQUFLRTtZQUMzQkMsVUFBVUMsT0FBTyxDQUFDRCxVQUFVRSxPQUFPLEtBQUs7WUFFeEMsSUFBSUMsVUFBVTtZQUNkLElBQUlQLEtBQUtRLFlBQVksT0FBT0wsTUFBTUssWUFBWSxJQUFJO2dCQUNoREQsVUFBVTNCLEVBQUUsaUJBQWlCO1lBQy9CLE9BQU8sSUFBSW9CLEtBQUtRLFlBQVksT0FBT0osVUFBVUksWUFBWSxJQUFJO2dCQUMzREQsVUFBVTNCLEVBQUUscUJBQXFCO1lBQ25DLE9BQU87Z0JBQ0wyQixVQUFVUCxLQUFLUyxrQkFBa0I7WUFDbkM7WUFFQSxJQUFJLENBQUNaLE1BQU0sQ0FBQ1UsUUFBUSxFQUFFO2dCQUNwQlYsTUFBTSxDQUFDVSxRQUFRLEdBQUcsRUFBRTtZQUN0QjtZQUNBVixNQUFNLENBQUNVLFFBQVEsQ0FBQ0csSUFBSSxDQUFDckMsOEVBQWlCQSxDQUFDMEI7UUFDekM7UUFFQSxPQUFPRjtJQUNUO0lBRUEsTUFBTWMsc0JBQXNCZix3QkFBd0JWO0lBRXBEO0lBQ0EsTUFBTTBCLGVBQWVBLENBQUNDO1FBQ3BCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxPQUFPcEMseUVBQVc7WUFDcEIsS0FBSztnQkFDSCxPQUFPQSx5RUFBVztZQUFDO1lBQ3JCLEtBQUs7Z0JBQ0gsT0FBT0EseUVBQVc7WUFDcEIsS0FBSztnQkFDSCxPQUFPQSx5RUFBVztZQUNwQjtnQkFDRSxPQUFPQSx5RUFBVztRQUN0QjtJQUNGO0lBQ0EsSUFBSUksaUJBQWlCa0MsU0FBUyxFQUFFO1FBQzlCLHFCQUNFLDhEQUFDbEQsMENBQU1BO1lBQUFtRCxXQUFBO1lBQUFDLFdBQUE7WUFBQUMsV0FBQTtZQUFDQyxJQUFHO1lBQWNDLE1BQU07WUFBR0MsZ0JBQWU7WUFBU0MsWUFBVztzQkFDbkUsNEVBQUN2RCx5Q0FBSUE7Z0JBQUFpRCxXQUFBO2dCQUFBQyxXQUFBO2dCQUFBQyxXQUFBO2dCQUFDSyxPQUFNOzBCQUFROzs7Ozs7Ozs7OztJQUcxQjtJQUVBLHFCQUNFLDhEQUFDMUQsMENBQU1BO1FBQUFtRCxXQUFBO1FBQUFDLFdBQUE7UUFBQUMsV0FBQTtRQUFDQyxJQUFHO1FBQWNDLE1BQU07OzBCQUM3Qiw4REFBQ3ZELDBDQUFNQTtnQkFBQW1ELFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNNLElBQUk7Z0JBQUlDLElBQUk7Z0JBQUlMLE1BQU07O2tDQUM1Qiw4REFBQ3hELDBDQUFNQTt3QkFBQW9ELFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNRLElBQUk7d0JBQUlMLGdCQUFlOzswQ0FDN0IsOERBQUMxRCxzQ0FBRUE7Z0NBQUFxRCxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBOzBDQUFDOzs7Ozs7MENBQ0osOERBQUNuRCx5Q0FBSUE7Z0NBQUFpRCxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFDSyxPQUFNO2dDQUFVSSxVQUFVO2dDQUFJQyxZQUFZO2dDQUFLQyxJQUFJOzs7Ozs7Ozs7Ozs7a0NBTTNELDhEQUFDaEUsMENBQU1BO3dCQUFBbUQsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ0UsTUFBTTt3QkFBR1UsSUFBSTdDLGlCQUFpQixLQUFLO2tDQUN4QzhDLE9BQU9DLElBQUksQ0FBQ3JCLHFCQUFxQnNCLE1BQU0sS0FBSyxrQkFDM0MsOERBQUNwRSwwQ0FBTUE7NEJBQUFtRCxXQUFBOzRCQUFBQyxXQUFBOzRCQUFBQyxXQUFBOzRCQUFDRSxNQUFNOzRCQUFHRSxZQUFXOzRCQUFTWSxJQUFJOzs4Q0FDdkMsOERBQUN0RSwwQ0FBTUE7b0NBQUFvRCxXQUFBO29DQUFBQyxXQUFBO29DQUFBQyxXQUFBO29DQUFDRyxnQkFBZTtvQ0FBU2EsSUFBSTs4Q0FDbEMsNEVBQUNsRSwwQ0FBS0E7d0NBQUFnRCxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDaUIsUUFBUWxFLDhFQUFnQjt3Q0FBRW1FLE9BQU87d0NBQUtDLFFBQVE7Ozs7Ozs7Ozs7OzhDQUV2RCw4REFBQ3hFLDBDQUFNQTtvQ0FBQW1ELFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUNHLGdCQUFlO29DQUFTYSxJQUFJOztzREFDbEMsOERBQUNuRSx5Q0FBSUE7NENBQUFpRCxXQUFBOzRDQUFBQyxXQUFBOzRDQUFBQyxXQUFBOzRDQUFDSyxPQUFNOzRDQUFVSSxVQUFVOzRDQUFJQyxZQUFXOzRDQUFPVSxXQUFVOzRDQUFTUixJQUFJO3NEQUFJOzs7Ozs7c0RBQ2pGLDhEQUFDL0QseUNBQUlBOzRDQUFBaUQsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQ0ssT0FBTTs0Q0FBWWEsT0FBTzs0Q0FBS0UsV0FBVTs0Q0FBU0MsUUFBTztzREFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBSXpFUixPQUFPUyxPQUFPLENBQUM3QixxQkFBcUI4QixHQUFHLENBQUM7Z0NBQUMsQ0FBQ3pDLE1BQU0wQyxJQUFJO2lEQUNsRCw4REFBQzdFLDBDQUFNQTtnQ0FBQW1ELFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQVlnQixJQUFJOztrREFDckIsOERBQUNuRSx5Q0FBSUE7d0NBQUFpRCxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDUyxVQUFVO3dDQUFJQyxZQUFXO3dDQUFPTCxPQUFNO3dDQUFZTyxJQUFJO2tEQUN6RDlCOzs7Ozs7b0NBRUYwQyxJQUFJRCxHQUFHLENBQUUxQyxDQUFBQSxtQkFDUiw4REFBQ25DLDBDQUFNQTs0Q0FBQW9ELFdBQUE7NENBQUFDLFdBQUE7NENBQUFDLFdBQUE7NENBQWFHLGdCQUFlOzRDQUFnQmEsSUFBSTs0Q0FBSVosWUFBVzs7OERBQ3BFLDhEQUFDMUQsMENBQU1BO29EQUFBb0QsV0FBQTtvREFBQUMsV0FBQTtvREFBQUMsV0FBQTtvREFBQ0ksWUFBVzs7c0VBQ2pCLDhEQUFDeEQseUNBQUlBOzREQUFBa0QsV0FBQTs0REFBQUMsV0FBQTs0REFBQUMsV0FBQTs0REFBQ3lCLFVBQVM7c0VBQ2IsNEVBQUMzRSwwQ0FBS0E7Z0VBQUFnRCxXQUFBO2dFQUFBQyxXQUFBO2dFQUFBQyxXQUFBO2dFQUFDaUIsUUFBUXZCLGFBQWFiLEdBQUdjLEtBQUs7Z0VBQUd1QixPQUFPO2dFQUFJQyxRQUFROzs7Ozs7Ozs7OztzRUFFNUQsOERBQUN2RSx5Q0FBSUE7NERBQUFrRCxXQUFBOzREQUFBQyxXQUFBOzREQUFBQyxXQUFBOzREQUFDMEIsSUFBSTs7OEVBQ1IsOERBQUM3RSx5Q0FBSUE7b0VBQUFpRCxXQUFBO29FQUFBQyxXQUFBO29FQUFBQyxXQUFBO29FQUFDUyxVQUFVO29FQUFJQyxZQUFXO29FQUFPTCxPQUFNOzhFQUN6Q2pELCtFQUFrQkEsQ0FBQ3lCLEdBQUc4QyxJQUFJOzs7Ozs7OEVBRTdCLDhEQUFDOUUseUNBQUlBO29FQUFBaUQsV0FBQTtvRUFBQUMsV0FBQTtvRUFBQUMsV0FBQTtvRUFBQ1MsVUFBVTtvRUFBSUMsWUFBVztvRUFBT0wsT0FBTTtvRUFBWVcsSUFBSTs4RUFDekRuQyxHQUFHK0MsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUl4Qiw4REFBQ2pGLDBDQUFNQTtvREFBQW1ELFdBQUE7b0RBQUFDLFdBQUE7b0RBQUFDLFdBQUE7b0RBQUM2QixlQUFjO29EQUFTekIsWUFBVzs7c0VBQ3hDLDhEQUFDdkQseUNBQUlBOzREQUFBaUQsV0FBQTs0REFBQUMsV0FBQTs0REFBQUMsV0FBQTs0REFBQ1MsVUFBVTs0REFBSUMsWUFBVzs0REFBT0wsT0FBTTtzRUFDekN4QixHQUFHaUQsYUFBYTs7Ozs7O3NFQUVuQiw4REFBQ2pGLHlDQUFJQTs0REFBQWlELFdBQUE7NERBQUFDLFdBQUE7NERBQUFDLFdBQUE7NERBQUNTLFVBQVU7NERBQUlDLFlBQVc7NERBQU9MLE9BQU07NERBQVlXLElBQUk7c0VBQ3pEbkMsR0FBR2tELFdBQVc7Ozs7Ozt3REFFaEJsRCxHQUFHbUQsTUFBTSxpQkFDUiw4REFBQ25GLHlDQUFJQTs0REFBQWlELFdBQUE7NERBQUFDLFdBQUE7NERBQUFDLFdBQUE7NERBQUNTLFVBQVU7NERBQUlKLE9BQU07NERBQVlXLElBQUk7O2dFQUN2Q25DLEdBQUdtRCxNQUFNLENBQUNDLEtBQUssQ0FBQyxHQUFHO2dFQUFHOzs7Ozs7bUVBRXZCOzs7Ozs7OzsyQ0F6QktwRCxHQUFHcUQsRUFBRTs7Ozs7OytCQUxUcEQ7Ozs7Ozs7Ozs7O29CQXdDbEJmLGdDQUNDLDhEQUFDcEIsMENBQU1BO3dCQUFBbUQsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ3lCLFVBQVM7d0JBQVdVLFFBQVE7d0JBQUlDLE1BQU07d0JBQUlDLE9BQU87a0NBQ3ZELDRFQUFDN0YsMENBQU1BOzRCQUFBc0QsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQ3NDLFNBQVM7NEJBQUlwQixPQUFNOzRCQUFPakIsSUFBRzs0QkFBWXNDLFNBQVMsSUFBTTFFLE9BQU8yQixJQUFJLENBQUM7c0NBQzFFLDRFQUFDM0MseUNBQUlBO2dDQUFBaUQsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ0ssT0FBTTswQ0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNOUIsOERBQUNwRCx1REFBWUE7Ozs7Ozs7Ozs7O0FBR25CO0dBN0pnQlE7O1FBQ0FILG9EQUFjQTtRQUNISiw0RUFBbUJBO1FBQ3hCRyxrRUFBY0E7UUFDbkJHLHdEQUFTQTs7O0tBSlZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hcHAvZmVhdHVyZXMvd2FsbGV0L2V4Y2hhbmdlLXNjcmVlbi50c3g/NmUxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCdXR0b24sIEgzLCBYU3RhY2ssIFlTdGFjayB9IGZyb20gJ0BteS91aSdcbmltcG9ydCB7IFZpZXcsIFRleHQsIEltYWdlIH0gZnJvbSAndGFtYWd1aSdcbmltcG9ydCBleGNlaGFuZ0ljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy93YWxsZXQvZXhjaGFuZ2UucG5nJ1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEZvb3Rlck5hdkJhciB9IGZyb20gJy4uL2hvbWUvc2NyZWVuJ1xuaW1wb3J0IHsgdXNlVHJhbnNhY3Rpb25TdG9yZSwgZm9ybWF0VHJhbnNhY3Rpb24sIGdldFR5cGVEaXNwbGF5VGV4dCB9IGZyb20gJ2FwcC9zdG9yZXMvdHJhbnNhY3Rpb25TdG9yZSdcbmltcG9ydCB7IHVzZVdhbGxldFN0b3JlIH0gZnJvbSAnYXBwL3N0b3Jlcy93YWxsZXRTdG9yZSdcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnYXBwL2kxOG4nXG5pbXBvcnQgZXRoSWNvbiBmcm9tICcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL3dhbGxldC9ldGgucG5nJ1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnc29saXRvL25hdmlnYXRpb24nXG5cbmV4cG9ydCBmdW5jdGlvbiBFeGNoYW5nZVNjcmVlbigpIHtcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpXG4gIGNvbnN0IHRyYW5zYWN0aW9uU3RvcmUgPSB1c2VUcmFuc2FjdGlvblN0b3JlKClcbiAgY29uc3Qgd2FsbGV0U3RvcmUgPSB1c2VXYWxsZXRTdG9yZSgpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG5cbiAgLy8g5Yid5aeL5YyW5Lqk5piT5pWw5o2uXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgdHJhbnNhY3Rpb25TdG9yZS5sb2FkVHJhbnNhY3Rpb25zKClcbiAgfSwgW10pXG5cbiAgLy8g6I635Y+W5b2T5YmN6LSm5oi355qE5omA5pyJ6ZO+55qE5Lqk5piTXG4gIGNvbnN0IGN1cnJlbnRBY2NvdW50ID0gd2FsbGV0U3RvcmUuY3VycmVudEFjY291bnRcbiAgY29uc3QgdHJhbnNhY3Rpb25zID0gY3VycmVudEFjY291bnRcbiAgICA/ICgoKSA9PiB7XG4gICAgICAvLyDojrflj5bmiYDmnInpk77nmoTlnLDlnYBcbiAgICAgIGNvbnN0IGFkZHJlc3NlcyA9IFtcbiAgICAgICAgY3VycmVudEFjY291bnQuZXRoPy5hZGRyZXNzLFxuICAgICAgICBjdXJyZW50QWNjb3VudC5ic2M/LmFkZHJlc3MsXG4gICAgICAgIGN1cnJlbnRBY2NvdW50LmJ0Yz8uYWRkcmVzcyxcbiAgICAgICAgY3VycmVudEFjY291bnQuc29sYW5hPy5hZGRyZXNzLFxuICAgICAgXS5maWx0ZXIoQm9vbGVhbikgLy8g6L+H5ruk5o6JdW5kZWZpbmVkL251bGzlgLxcblxuICAgICAgLy8g5L2/55So5paw55qE5Y676YeN5pa55rOV6I635Y+W5Lqk5piT6K6w5b2VXG4gICAgICByZXR1cm4gdHJhbnNhY3Rpb25TdG9yZS5nZXRUcmFuc2FjdGlvbnNCeUFkZHJlc3NlcyhhZGRyZXNzZXMpXG4gICAgfSkoKVxuICAgIDogW11cblxuICAvLyDmjInml6XmnJ/liIbnu4TkuqTmmJNcbiAgY29uc3QgZ3JvdXBUcmFuc2FjdGlvbnNCeURhdGUgPSAodHJhbnNhY3Rpb25zOiBhbnlbXSkgPT4ge1xuICAgIGNvbnN0IGdyb3VwczogeyBba2V5OiBzdHJpbmddOiBhbnlbXSB9ID0ge31cblxuICAgIHRyYW5zYWN0aW9ucy5mb3JFYWNoKCh0eCkgPT4ge1xuICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHR4LnRpbWVzdGFtcClcbiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKVxuICAgICAgY29uc3QgeWVzdGVyZGF5ID0gbmV3IERhdGUodG9kYXkpXG4gICAgICB5ZXN0ZXJkYXkuc2V0RGF0ZSh5ZXN0ZXJkYXkuZ2V0RGF0ZSgpIC0gMSlcblxuICAgICAgbGV0IGRhdGVLZXkgPSAnJ1xuICAgICAgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHRvZGF5LnRvRGF0ZVN0cmluZygpKSB7XG4gICAgICAgIGRhdGVLZXkgPSB0KCd0aW1lLnRvZGF5JykgfHwgJ+S7iuWkqSdcbiAgICAgIH0gZWxzZSBpZiAoZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0geWVzdGVyZGF5LnRvRGF0ZVN0cmluZygpKSB7XG4gICAgICAgIGRhdGVLZXkgPSB0KCd0aW1lLnllc3RlcmRheScpIHx8ICfmmKjlpKknXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBkYXRlS2V5ID0gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoKVxuICAgICAgfVxuXG4gICAgICBpZiAoIWdyb3Vwc1tkYXRlS2V5XSkge1xuICAgICAgICBncm91cHNbZGF0ZUtleV0gPSBbXVxuICAgICAgfVxuICAgICAgZ3JvdXBzW2RhdGVLZXldLnB1c2goZm9ybWF0VHJhbnNhY3Rpb24odHgpKVxuICAgIH0pXG5cbiAgICByZXR1cm4gZ3JvdXBzXG4gIH1cblxuICBjb25zdCBncm91cGVkVHJhbnNhY3Rpb25zID0gZ3JvdXBUcmFuc2FjdGlvbnNCeURhdGUodHJhbnNhY3Rpb25zKVxuXG4gIC8vIOiOt+WPlumTvuWbvuagh1xuICBjb25zdCBnZXRDaGFpbkljb24gPSAoY2hhaW46IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoY2hhaW4pIHtcbiAgICAgIGNhc2UgJ2V0aCc6XG4gICAgICAgIHJldHVybiBldGhJY29uLnNyY1xuICAgICAgY2FzZSAnYnNjJzpcbiAgICAgICAgcmV0dXJuIGV0aEljb24uc3JjIC8vIOaaguaXtuS9v+eUqOWQjOS4gOS4quWbvuagh1xuICAgICAgY2FzZSAnYnRjJzpcbiAgICAgICAgcmV0dXJuIGV0aEljb24uc3JjXG4gICAgICBjYXNlICdzb2xhbmEnOlxuICAgICAgICByZXR1cm4gZXRoSWNvbi5zcmNcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBldGhJY29uLnNyY1xuICAgIH1cbiAgfVxuICBpZiAodHJhbnNhY3Rpb25TdG9yZS5pc0xvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPFlTdGFjayBiZz1cIiRiYWNrZ3JvdW5kXCIgZmxleD17MX0ganVzdGlmeUNvbnRlbnQ9XCJjZW50ZXJcIiBhbGlnbkl0ZW1zPVwiY2VudGVyXCI+XG4gICAgICAgIDxUZXh0IGNvbG9yPVwid2hpdGVcIj7liqDovb3kuK0uLi48L1RleHQ+XG4gICAgICA8L1lTdGFjaz5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxZU3RhY2sgYmc9XCIkYmFja2dyb3VuZFwiIGZsZXg9ezF9PlxuICAgICAgPFlTdGFjayBweD17MTZ9IHB5PXszMH0gZmxleD17MX0+XG4gICAgICAgIDxYU3RhY2sgcGw9ezE2fSBqdXN0aWZ5Q29udGVudD0nc3BhY2UtYmV0d2Vlbic+XG4gICAgICAgICAgPEgzPuS6pOaYkzwvSDM+XG4gICAgICAgICAgPFRleHQgY29sb3I9XCIjNDU3NUZGXCIgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PXs1MDB9IG1yPXsxNn0+XG4gICAgICAgICAgICB7Lyoge3QoJ3RpbWUuZmlsdGVyJykgfHwgJ+etm+mAiSd9ICovfVxuICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgPC9YU3RhY2s+XG5cbiAgICAgICAgey8qIOS6pOaYk+iusOW9leWIl+ihqCAqL31cbiAgICAgICAgPFlTdGFjayBmbGV4PXsxfSBtYj17Y3VycmVudEFjY291bnQgPyA4MCA6IDE0MH0+XG4gICAgICAgICAge09iamVjdC5rZXlzKGdyb3VwZWRUcmFuc2FjdGlvbnMpLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgIDxZU3RhY2sgZmxleD17MX0gYWxpZ25JdGVtcz1cImNlbnRlclwiIG10PXswfT5cbiAgICAgICAgICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD0nY2VudGVyJyBtdD17MH0+XG4gICAgICAgICAgICAgICAgPEltYWdlIHNvdXJjZT17ZXhjZWhhbmdJY29uLnNyY30gd2lkdGg9ezE3M30gaGVpZ2h0PXsxNDJ9IC8+XG4gICAgICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgICAgICA8WVN0YWNrIGp1c3RpZnlDb250ZW50PSdjZW50ZXInIG10PXsyMH0+XG4gICAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCIkd2hpdGUxXCIgZm9udFNpemU9ezE2fSBmb250V2VpZ2h0PVwiYm9sZFwiIHRleHRBbGlnbj0nY2VudGVyJyBtYj17MTB9Pui/mOayoeacieS6pOaYkzwvVGV4dD5cbiAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIiRhY2NlbnQxMVwiIHdpZHRoPXsyODB9IHRleHRBbGlnbj0nY2VudGVyJyBtYXJnaW49XCJhdXRvXCI+5LiA5pem5oKo5byA5aeL5L2/55So6ZKx5YyF77yM5oKo55qE5Yqg5a+G6LSn5biB5ZKMIE5GVCDmtLvliqjlsIbmmL7npLrlnKjov5nph4zjgII8L1RleHQ+XG4gICAgICAgICAgICAgIDwvWVN0YWNrPlxuICAgICAgICAgICAgPC9ZU3RhY2s+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIE9iamVjdC5lbnRyaWVzKGdyb3VwZWRUcmFuc2FjdGlvbnMpLm1hcCgoW2RhdGUsIHR4c10pID0+IChcbiAgICAgICAgICAgICAgPFlTdGFjayBrZXk9e2RhdGV9IG10PXsyMH0+XG4gICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiIGNvbG9yPVwiJGFjY2VudDExXCIgbWI9ezEwfT5cbiAgICAgICAgICAgICAgICAgIHtkYXRlfVxuICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICB7dHhzLm1hcCgodHgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxYU3RhY2sga2V5PXt0eC5pZH0ganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezI1fSBhbGlnbkl0ZW1zPVwiY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxYU3RhY2sgYWxpZ25JdGVtcz1cImNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxWaWV3IHBvc2l0aW9uPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e2dldENoYWluSWNvbih0eC5jaGFpbil9IHdpZHRoPXszOH0gaGVpZ2h0PXszOH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgICAgICAgICAgPFZpZXcgbWw9ezEwfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cIndoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRUeXBlRGlzcGxheVRleHQodHgudHlwZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTJ9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCIkYWNjZW50MTFcIiBtdD17Mn0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0eC5kaXNwbGF5QWRkcmVzc31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgICAgICAgICAgICA8WVN0YWNrIGZsZXhEaXJlY3Rpb249XCJjb2x1bW5cIiBhbGlnbkl0ZW1zPVwiZmxleC1lbmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCJ3aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3R4LmRpc3BsYXlBbW91bnR9XG4gICAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxMn0gZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cIiRhY2NlbnQxMVwiIG10PXsyfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt0eC5kaXNwbGF5VGltZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAge3R4LnR4SGFzaCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxMH0gY29sb3I9XCIkYWNjZW50MTFcIiBtdD17MX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0eC50eEhhc2guc2xpY2UoMCwgOCl9Li4uXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgICAgIDwvWVN0YWNrPlxuICAgICAgICAgICAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvWVN0YWNrPlxuICAgICAgICAgICAgKSlcbiAgICAgICAgICApfVxuICAgICAgICA8L1lTdGFjaz5cblxuICAgICAgICB7Lyog5bqV6YOo5oyJ6ZKuIC0g5qC55o2u5piv5ZCm5pyJ6ZKx5YyF5Yqo5oCB5pi+56S6ICovfVxuICAgICAgICB7Y3VycmVudEFjY291bnQgJiYgKFxuICAgICAgICAgIDxZU3RhY2sgcG9zaXRpb249XCJhYnNvbHV0ZVwiIGJvdHRvbT17OTB9IGxlZnQ9ezE2fSByaWdodD17MTZ9PlxuICAgICAgICAgICAgPEJ1dHRvbiByb3VuZGVkPXszMH0gd2lkdGg9XCIxMDAlXCIgYmc9XCIkYWNjZW50MTFcIiBvblByZXNzPXsoKSA9PiByb3V0ZXIucHVzaCgnL3dhbGxldC9idXlDb2luJyl9PlxuICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIiR3aGl0ZTFcIj7lsIbliqDlr4botKfluIHmt7vliqDliLDmgqjnmoTpkrHljIU8L1RleHQ+XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L1lTdGFjaz5cbiAgICAgICAgKX1cbiAgICAgIDwvWVN0YWNrPlxuXG4gICAgICA8Rm9vdGVyTmF2QmFyIC8+XG4gICAgPC9ZU3RhY2s+XG4gIClcbn0iXSwibmFtZXMiOlsiQnV0dG9uIiwiSDMiLCJYU3RhY2siLCJZU3RhY2siLCJWaWV3IiwiVGV4dCIsIkltYWdlIiwiZXhjZWhhbmdJY29uIiwidXNlRWZmZWN0IiwiRm9vdGVyTmF2QmFyIiwidXNlVHJhbnNhY3Rpb25TdG9yZSIsImZvcm1hdFRyYW5zYWN0aW9uIiwiZ2V0VHlwZURpc3BsYXlUZXh0IiwidXNlV2FsbGV0U3RvcmUiLCJ1c2VUcmFuc2xhdGlvbiIsImV0aEljb24iLCJ1c2VSb3V0ZXIiLCJFeGNoYW5nZVNjcmVlbiIsInQiLCJ0cmFuc2FjdGlvblN0b3JlIiwid2FsbGV0U3RvcmUiLCJyb3V0ZXIiLCJsb2FkVHJhbnNhY3Rpb25zIiwiY3VycmVudEFjY291bnQiLCJ0cmFuc2FjdGlvbnMiLCJhZGRyZXNzZXMiLCJldGgiLCJhZGRyZXNzIiwiYnNjIiwiYnRjIiwic29sYW5hIiwiZmlsdGVyIiwiQm9vbGVhbiIsImdldFRyYW5zYWN0aW9uc0J5QWRkcmVzc2VzIiwiZ3JvdXBUcmFuc2FjdGlvbnNCeURhdGUiLCJncm91cHMiLCJmb3JFYWNoIiwidHgiLCJkYXRlIiwiRGF0ZSIsInRpbWVzdGFtcCIsInRvZGF5IiwieWVzdGVyZGF5Iiwic2V0RGF0ZSIsImdldERhdGUiLCJkYXRlS2V5IiwidG9EYXRlU3RyaW5nIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwicHVzaCIsImdyb3VwZWRUcmFuc2FjdGlvbnMiLCJnZXRDaGFpbkljb24iLCJjaGFpbiIsInNyYyIsImlzTG9hZGluZyIsImRhdGEtYXQiLCJkYXRhLWluIiwiZGF0YS1pcyIsImJnIiwiZmxleCIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsImNvbG9yIiwicHgiLCJweSIsInBsIiwiZm9udFNpemUiLCJmb250V2VpZ2h0IiwibXIiLCJtYiIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJtdCIsInNvdXJjZSIsIndpZHRoIiwiaGVpZ2h0IiwidGV4dEFsaWduIiwibWFyZ2luIiwiZW50cmllcyIsIm1hcCIsInR4cyIsInBvc2l0aW9uIiwibWwiLCJ0eXBlIiwiZGlzcGxheUFkZHJlc3MiLCJmbGV4RGlyZWN0aW9uIiwiZGlzcGxheUFtb3VudCIsImRpc3BsYXlUaW1lIiwidHhIYXNoIiwic2xpY2UiLCJpZCIsImJvdHRvbSIsImxlZnQiLCJyaWdodCIsInJvdW5kZWQiLCJvblByZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/exchange-screen.tsx\n"));

/***/ })

});