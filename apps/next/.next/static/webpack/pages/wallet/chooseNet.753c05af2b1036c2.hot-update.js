"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/chooseNet",{

/***/ "../../packages/app/features/wallet/choose-net-screen.tsx":
/*!****************************************************************!*\
  !*** ../../packages/app/features/wallet/choose-net-screen.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChooseNetScreen: function() { return /* binding */ ChooseNetScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _assets_images_wallet_choose1_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/choose1.png */ \"../../packages/assets/images/wallet/choose1.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nconst Underlineblock = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2\n});\n_c2 = Underlineblock;\nfunction ChooseNetScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [netList, setNetList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Ethereum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n        },\n        {\n            id: 2,\n            name: \"Polygon\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n        },\n        {\n            id: 3,\n            name: \"BNB Smart Chain\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n        },\n        {\n            id: 4,\n            name: \"Solana\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.YStack, {\n        \"data-at\": \"choose-net-screen.tsx:60\",\n        \"data-in\": \"ChooseNetScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                \"data-at\": \"choose-net-screen.tsx:61\",\n                \"data-in\": \"ChooseNetScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                pr: 16,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.NavBar, {\n                        title: \"网络\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                        \"data-at\": \"choose-net-screen.tsx:63\",\n                        \"data-in\": \"ChooseNetScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_choose1_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                        width: 16,\n                        height: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.YStack, {\n                \"data-at\": \"choose-net-screen.tsx:65\",\n                \"data-in\": \"ChooseNetScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                position: \"relative\",\n                height: 700,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                        \"data-at\": \"choose-net-screen.tsx:66\",\n                        \"data-in\": \"ChooseNetScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        gap: \"$5\",\n                        height: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_7__.Pressable, {\n                                onPress: ()=>setCurrentTab(0),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                    \"data-at\": \"choose-net-screen.tsx:68\",\n                                    \"data-in\": \"ChooseNetScreen\",\n                                    \"data-is\": \"View\",\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"主网\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            \"data-at\": \"choose-net-screen.tsx:69\",\n                                            \"data-in\": \"ChooseNetScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"主网\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 65\n                                        }, this),\n                                        currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_7__.Pressable, {\n                                onPress: ()=>setCurrentTab(1),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                    \"data-at\": \"choose-net-screen.tsx:74\",\n                                    \"data-in\": \"ChooseNetScreen\",\n                                    \"data-is\": \"View\",\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"测试网\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            \"data-at\": \"choose-net-screen.tsx:75\",\n                                            \"data-in\": \"ChooseNetScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"测试网\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 66\n                                        }, this),\n                                        currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.YStack, {\n                        \"data-at\": \"choose-net-screen.tsx:80\",\n                        \"data-in\": \"ChooseNetScreen\",\n                        \"data-is\": \"YStack\",\n                        mt: 20,\n                        children: netList.map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                                \"data-at\": \"choose-net-screen.tsx:82\",\n                                \"data-in\": \"ChooseNetScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\",\n                                mt: 10,\n                                mb: 20,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                                    \"data-at\": \"choose-net-screen.tsx:83\",\n                                    \"data-in\": \"ChooseNetScreen\",\n                                    \"data-is\": \"XStack\",\n                                    items: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                            \"data-at\": \"choose-net-screen.tsx:84-87\",\n                                            \"data-in\": \"ChooseNetScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: i.icon,\n                                            style: {\n                                                width: 24,\n                                                height: 24\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            \"data-at\": \"choose-net-screen.tsx:88\",\n                                            \"data-in\": \"ChooseNetScreen\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 16,\n                                            color: \"#fff\",\n                                            fontWeight: \"bold\",\n                                            ml: 6,\n                                            children: i.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this)\n                            }, i.id, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n        lineNumber: 50,\n        columnNumber: 10\n    }, this);\n}\n_s(ChooseNetScreen, \"ofap02gcqxQ1tGnozxJSrXAm6LE=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c3 = ChooseNetScreen;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"Underlineblock\");\n$RefreshReg$(_c3, \"ChooseNetScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/choose-net-screen.tsx\n"));

/***/ })

});