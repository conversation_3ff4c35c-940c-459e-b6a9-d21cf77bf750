"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/chooseNet",{

/***/ "../../packages/app/features/wallet/choose-net-screen.tsx":
/*!****************************************************************!*\
  !*** ../../packages/app/features/wallet/choose-net-screen.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChooseNetScreen: function() { return /* binding */ ChooseNetScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _assets_images_wallet_choose1_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/choose1.png */ \"../../packages/assets/images/wallet/choose1.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/wallet/arrowright.png */ \"../../packages/assets/images/wallet/arrowright.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nconst Underlineblock = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2\n});\n_c2 = Underlineblock;\nfunction ChooseNetScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [netList, setNetList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Ethereum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n        },\n        {\n            id: 2,\n            name: \"Polygon\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n        },\n        {\n            id: 3,\n            name: \"BNB Smart Chain\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n        },\n        {\n            id: 4,\n            name: \"Solana\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"choose-net-screen.tsx:60\",\n        \"data-in\": \"ChooseNetScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"choose-net-screen.tsx:61\",\n                \"data-in\": \"ChooseNetScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                pr: 16,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                        title: \"网络\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                        \"data-at\": \"choose-net-screen.tsx:63\",\n                        \"data-in\": \"ChooseNetScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_choose1_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                        width: 16,\n                        height: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"choose-net-screen.tsx:65\",\n                \"data-in\": \"ChooseNetScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                position: \"relative\",\n                height: 700,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"choose-net-screen.tsx:66\",\n                        \"data-in\": \"ChooseNetScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        gap: \"$5\",\n                        height: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>setCurrentTab(0),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                    \"data-at\": \"choose-net-screen.tsx:68\",\n                                    \"data-in\": \"ChooseNetScreen\",\n                                    \"data-is\": \"View\",\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"主网\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            \"data-at\": \"choose-net-screen.tsx:69\",\n                                            \"data-in\": \"ChooseNetScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"主网\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 65\n                                        }, this),\n                                        currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>setCurrentTab(1),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                    \"data-at\": \"choose-net-screen.tsx:74\",\n                                    \"data-in\": \"ChooseNetScreen\",\n                                    \"data-is\": \"View\",\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"测试网\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            \"data-at\": \"choose-net-screen.tsx:75\",\n                                            \"data-in\": \"ChooseNetScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"测试网\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 66\n                                        }, this),\n                                        currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"choose-net-screen.tsx:80\",\n                        \"data-in\": \"ChooseNetScreen\",\n                        \"data-is\": \"YStack\",\n                        mt: 20,\n                        children: netList.map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"choose-net-screen.tsx:82\",\n                                \"data-in\": \"ChooseNetScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\",\n                                mt: 10,\n                                mb: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                        \"data-at\": \"choose-net-screen.tsx:83\",\n                                        \"data-in\": \"ChooseNetScreen\",\n                                        \"data-is\": \"XStack\",\n                                        items: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                                \"data-at\": \"choose-net-screen.tsx:84-87\",\n                                                \"data-in\": \"ChooseNetScreen\",\n                                                \"data-is\": \"Image\",\n                                                source: i.icon,\n                                                style: {\n                                                    width: 24,\n                                                    height: 24\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"choose-net-screen.tsx:88\",\n                                                \"data-in\": \"ChooseNetScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 16,\n                                                color: \"#fff\",\n                                                fontWeight: \"bold\",\n                                                ml: 6,\n                                                children: i.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                        \"data-at\": \"choose-net-screen.tsx:90\",\n                                        \"data-in\": \"ChooseNetScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                        width: 6,\n                                        height: 9\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, i.id, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/choose-net-screen.tsx\",\n        lineNumber: 50,\n        columnNumber: 10\n    }, this);\n}\n_s(ChooseNetScreen, \"ofap02gcqxQ1tGnozxJSrXAm6LE=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c3 = ChooseNetScreen;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"Underlineblock\");\n$RefreshReg$(_c3, \"ChooseNetScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/choose-net-screen.tsx\n"));

/***/ })

});