"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/exchange",{

/***/ "../../packages/app/features/wallet/exchange-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/exchange-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExchangeScreen: function() { return /* binding */ ExchangeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/exchange.png */ \"../../packages/assets/images/wallet/exchange.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ExchangeScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_6__.useTransactionStore)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_7__.useWalletStore)();\n    // 初始化交易数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        transactionStore.loadTransactions();\n    }, []);\n    // 获取当前账户的所有链的交易\n    const currentAccount = walletStore.currentAccount;\n    const transactions = currentAccount ? (()=>{\n        var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n        // 获取所有链的地址\n        const addresses = [\n            (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n            (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n            (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n            (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n        ].filter(Boolean); // 过滤掉undefined/null值\n        // 使用新的去重方法获取交易记录\n        return transactionStore.getTransactionsByAddresses(addresses);\n    })() : [];\n    // 按日期分组交易\n    const groupTransactionsByDate = (transactions)=>{\n        const groups = {};\n        transactions.forEach((tx)=>{\n            const date = new Date(tx.timestamp);\n            const today = new Date();\n            const yesterday = new Date(today);\n            yesterday.setDate(yesterday.getDate() - 1);\n            let dateKey = \"\";\n            if (date.toDateString() === today.toDateString()) {\n                dateKey = t(\"time.today\") || \"今天\";\n            } else if (date.toDateString() === yesterday.toDateString()) {\n                dateKey = t(\"time.yesterday\") || \"昨天\";\n            } else {\n                dateKey = date.toLocaleDateString();\n            }\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push((0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_6__.formatTransaction)(tx));\n        });\n        return groups;\n    };\n    const groupedTransactions = groupTransactionsByDate(transactions);\n    // 获取链图标\n    const getChainIcon = (chain)=>{\n        switch(chain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            // 暂时使用同一个图标\n            case \"btc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            default:\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n        }\n    };\n    if (transactionStore.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n            \"data-at\": \"exchange-screen.tsx:88\",\n            \"data-in\": \"ExchangeScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                \"data-at\": \"exchange-screen.tsx:89\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"加载中...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n            lineNumber: 78,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n        \"data-at\": \"exchange-screen.tsx:95\",\n        \"data-in\": \"ExchangeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        px: 16,\n        py: 30,\n        minHeight: \"100vh\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                \"data-at\": \"exchange-screen.tsx:96\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                justifyContent: \"space-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.H3, {\n                        \"data-at\": \"exchange-screen.tsx:97\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"H3\",\n                        children: \"交易\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                        \"data-at\": \"exchange-screen.tsx:98\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"Text\",\n                        color: \"#4575FF\",\n                        fontSize: 14,\n                        fontWeight: 500,\n                        mr: 16,\n                        children: t(\"time.filter\") || \"筛选\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            Object.keys(groupedTransactions).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                \"data-at\": \"exchange-screen.tsx:105\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                mt: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"exchange-screen.tsx:106\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"center\",\n                        mt: 50,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                            \"data-at\": \"exchange-screen.tsx:107\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                            width: 173,\n                            height: 142\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:109\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        justifyContent: \"center\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:110\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$white1\",\n                                fontSize: 16,\n                                fontWeight: \"bold\",\n                                textAlign: \"center\",\n                                mb: 10,\n                                children: \"还没有交易\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:111\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$accent11\",\n                                width: 280,\n                                textAlign: \"center\",\n                                margin: \"auto\",\n                                children: \"一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 91,\n                columnNumber: 56\n            }, this) : Object.entries(groupedTransactions).map((param)=>{\n                let [date, txs] = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                    \"data-at\": \"exchange-screen.tsx:116\",\n                    \"data-in\": \"ExchangeScreen\",\n                    \"data-is\": \"YStack\",\n                    mt: 20,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                            \"data-at\": \"exchange-screen.tsx:117\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 14,\n                            fontWeight: \"bold\",\n                            color: \"$accent11\",\n                            mb: 10,\n                            children: date\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this),\n                        txs.map((tx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                \"data-at\": \"exchange-screen.tsx:121\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"space-between\",\n                                mt: 25,\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                        \"data-at\": \"exchange-screen.tsx:122\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"XStack\",\n                                        alignItems: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                \"data-at\": \"exchange-screen.tsx:123\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"View\",\n                                                position: \"relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                                    \"data-at\": \"exchange-screen.tsx:124\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"Image\",\n                                                    source: getChainIcon(tx.chain),\n                                                    width: 38,\n                                                    height: 38\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                \"data-at\": \"exchange-screen.tsx:126\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"View\",\n                                                ml: 10,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                        \"data-at\": \"exchange-screen.tsx:127\",\n                                                        \"data-in\": \"ExchangeScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        color: \"white\",\n                                                        children: (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_6__.getTypeDisplayText)(tx.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                        \"data-at\": \"exchange-screen.tsx:130\",\n                                                        \"data-in\": \"ExchangeScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        fontWeight: \"bold\",\n                                                        color: \"$accent11\",\n                                                        mt: 2,\n                                                        children: tx.displayAddress\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                        \"data-at\": \"exchange-screen.tsx:135\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        flexDirection: \"column\",\n                                        alignItems: \"flex-end\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                \"data-at\": \"exchange-screen.tsx:136\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                color: \"white\",\n                                                children: tx.displayAmount\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                \"data-at\": \"exchange-screen.tsx:139\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 12,\n                                                fontWeight: \"bold\",\n                                                color: \"$accent11\",\n                                                mt: 2,\n                                                children: tx.displayTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this),\n                                            tx.txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                \"data-at\": \"exchange-screen.tsx:143\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 10,\n                                                color: \"$accent11\",\n                                                mt: 1,\n                                                children: [\n                                                    tx.txHash.slice(0, 8),\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 32\n                                            }, this) : null\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, tx.id, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 28\n                            }, this))\n                    ]\n                }, date, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 78\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                \"data-at\": \"exchange-screen.tsx:154\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Button\",\n                rounded: 30,\n                position: \"absolute\",\n                bottom: 20,\n                width: \"90%\",\n                bg: \"$accent11\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                    \"data-at\": \"exchange-screen.tsx:155\",\n                    \"data-in\": \"ExchangeScreen\",\n                    \"data-is\": \"Text\",\n                    color: \"$white1\",\n                    children: \"将加密货币添加到您的钱包\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_10__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n        lineNumber: 82,\n        columnNumber: 10\n    }, this);\n}\n_s(ExchangeScreen, \"vh0iM1EJRpkThDqeY5xiYZCYW4I=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_5__.useTranslation,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_6__.useTransactionStore,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_7__.useWalletStore\n    ];\n});\n_c = ExchangeScreen;\nvar _c;\n$RefreshReg$(_c, \"ExchangeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/exchange-screen.tsx\n"));

/***/ })

});