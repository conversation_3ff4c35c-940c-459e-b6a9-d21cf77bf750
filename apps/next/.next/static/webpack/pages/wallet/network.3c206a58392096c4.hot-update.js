"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/network",{

/***/ "../../packages/app/features/wallet/network-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/network-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NetworkScreen: function() { return /* binding */ NetworkScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/net1.png */ \"../../packages/assets/images/wallet/net1.png\");\n/* harmony import */ var _assets_images_wallet_net2_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/net2.png */ \"../../packages/assets/images/wallet/net2.png\");\n/* harmony import */ var _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/net3.png */ \"../../packages/assets/images/wallet/net3.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 20\n});\n_c = Underline;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst ActiveUnderline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = ActiveUnderline;\nfunction NetworkScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const tabList = [\n        \"全部\",\n        \"交换\",\n        \"赚取\",\n        \"社交媒体\",\n        \"管理\",\n        \"监听\"\n    ];\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    // 全部应用数据\n    const allAppsData = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true,\n            url: \"https://aerodrome.finance/\",\n            icon: \"https://aerodrome.finance/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"通过汇集做市交换代币并赚取费用\",\n            isSelected: false,\n            url: \"https://app.uniswap.org/swap?disableNFTs=true\",\n            icon: \"https://app.uniswap.org/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Toshi Mart\",\n            desc: \"Base 上的模因币生成器和交易平台。\",\n            isSelected: true,\n            url: \"https://toshi.fun/\",\n            icon: \"https://toshi.fun/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Matcha\",\n            desc: \"0x 开发的 DEX 聚合器\",\n            isSelected: true,\n            url: \"https://matcha.xyz/\",\n            icon: \"https://matcha.xyz/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Alien Base\",\n            desc: \"为非传统地球人而打造的 Base 原生去中心化交易所\",\n            isSelected: false,\n            url: \"https://app.alienbase.xyz/\",\n            icon: \"https://app.alienbase.xyz/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"PancakeSwap\",\n            desc: \"交易并赚取加密货币\",\n            isSelected: false,\n            url: \"https://pancakeswap.finance/\",\n            icon: \"https://pancakeswap.finance/favicon.ico\"\n        },\n        {\n            id: 7,\n            name: \"Seamless Protocol\",\n            desc: \"实现收益最大化\",\n            isSelected: true,\n            url: \"https://seamlessprotocol.com/\",\n            icon: \"https://seamlessprotocol.com/favicon.ico\"\n        },\n        {\n            id: 8,\n            name: \"Plaza Finance\",\n            desc: \"债券和杠杆\",\n            isSelected: true,\n            url: \"https://plaza.finance/\",\n            icon: \"https://plaza.finance/favicon.ico\"\n        },\n        {\n            id: 9,\n            name: \"MetaLend\",\n            desc: \"在 5 分钟内实现加密货币收益最大化\",\n            isSelected: true,\n            url: \"https://metalend.fi/\",\n            icon: \"https://metalend.fi/favicon.ico\"\n        },\n        {\n            id: 10,\n            name: \"DIMO\",\n            desc: \"更聪明地驾驶并获得奖励\",\n            isSelected: false,\n            url: \"https://dimo.zone/\",\n            icon: \"https://dimo.zone/favicon.ico\"\n        },\n        {\n            id: 11,\n            name: \"ether.fi\",\n            desc: \"通过 ether.fi 质押、消费、赚钱\",\n            isSelected: false,\n            url: \"https://www.ether.fi/\",\n            icon: \"https://www.ether.fi/favicon.ico\"\n        },\n        {\n            id: 12,\n            name: \"Moonwell\",\n            desc: \"使贷款变得简单\",\n            isSelected: false,\n            url: \"https://moonwell.fi/\",\n            icon: \"https://moonwell.fi/favicon.ico\"\n        }\n    ];\n    // 交换应用数据\n    const exchangeAppsData = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true,\n            url: \"https://aerodrome.finance/\",\n            icon: \"https://aerodrome.finance/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"通过汇集做市交换代币并赚取费用\",\n            isSelected: false,\n            url: \"https://app.uniswap.org/swap?disableNFTs=true\",\n            icon: \"https://app.uniswap.org/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Toshi Mart\",\n            desc: \"Base 上的模因币生成器和交易平台。\",\n            isSelected: true,\n            url: \"https://toshi.fun/\",\n            icon: \"https://toshi.fun/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Matcha\",\n            desc: \"0x 开发的 DEX 聚合器\",\n            isSelected: true,\n            url: \"https://matcha.xyz/\",\n            icon: \"https://matcha.xyz/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Alien Base\",\n            desc: \"为非传统地球人而打造的 Base 原生去中心化交易所\",\n            isSelected: false,\n            url: \"https://app.alienbase.xyz/\",\n            icon: \"https://app.alienbase.xyz/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"PancakeSwap\",\n            desc: \"交易并赚取加密货币\",\n            isSelected: false,\n            url: \"https://pancakeswap.finance/\",\n            icon: \"https://pancakeswap.finance/favicon.ico\"\n        }\n    ];\n    // 赚取应用数据\n    const earnAppsData = [\n        {\n            id: 1,\n            name: \"Seamless Protocol\",\n            desc: \"实现收益最大化\",\n            isSelected: true,\n            url: \"https://seamlessprotocol.com/\",\n            icon: \"https://seamlessprotocol.com/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Plaza Finance\",\n            desc: \"债券和杠杆\",\n            isSelected: true,\n            url: \"https://plaza.finance/\",\n            icon: \"https://plaza.finance/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"MetaLend\",\n            desc: \"在 5 分钟内实现加密货币收益最大化\",\n            isSelected: true,\n            url: \"https://metalend.fi/\",\n            icon: \"https://metalend.fi/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"ether.fi\",\n            desc: \"通过 ether.fi 质押、消费、赚钱\",\n            isSelected: false,\n            url: \"https://www.ether.fi/\",\n            icon: \"https://www.ether.fi/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Moonwell\",\n            desc: \"使贷款变得简单\",\n            isSelected: false,\n            url: \"https://moonwell.fi/\",\n            icon: \"https://moonwell.fi/favicon.ico\"\n        }\n    ];\n    // 社交媒体应用数据\n    const socialMediaAppsData = [\n        {\n            id: 1,\n            name: \"moshi.cam\",\n            desc: \"链上照片共享应用程序\",\n            isSelected: false,\n            url: \"https://moshi.cam/\",\n            icon: \"https://moshi.cam/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Virtuals\",\n            desc: \"链上 AI 代理协会\",\n            isSelected: true,\n            url: \"https://virtuals.io/\",\n            icon: \"https://virtuals.io/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Hypersub\",\n            desc: \"订阅并赚取\",\n            isSelected: false,\n            url: \"https://hypersub.withfabric.xyz/\",\n            icon: \"https://hypersub.withfabric.xyz/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Soulbound TV\",\n            desc: \"实现电视去中心化，新一代直播平台。\",\n            isSelected: false,\n            url: \"https://soulbound.tv/\",\n            icon: \"https://soulbound.tv/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Rad TV\",\n            desc: \"为创作者和粉丝提供链上视频流\",\n            isSelected: false,\n            url: \"https://rad.tv/\",\n            icon: \"https://rad.tv/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"Aura\",\n            desc: \"个人链上克隆体\",\n            isSelected: false,\n            url: \"https://aura.network/\",\n            icon: \"https://aura.network/favicon.ico\"\n        }\n    ];\n    // 管理应用数据\n    const managementAppsData = [\n        {\n            id: 1,\n            name: \"Onboard\",\n            desc: \"成为 Onboard 商家，每天最多可赚取 100...\",\n            isSelected: true,\n            url: \"https://onboard.xyz/\",\n            icon: \"https://onboard.xyz/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Webacy\",\n            desc: \"管理钱包安全。\",\n            isSelected: false,\n            url: \"https://webacy.com/\",\n            icon: \"https://webacy.com/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Dune\",\n            desc: \"在 Base 上开发应用程序\",\n            isSelected: false,\n            url: \"https://dune.com/\",\n            icon: \"https://dune.com/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Venice\",\n            desc: \"私有且不受审查的人工智能\",\n            isSelected: false,\n            url: \"https://venice.ai/\",\n            icon: \"https://venice.ai/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Quip Network\",\n            desc: \"保护您的资产免受量子计算机黑客的攻击\",\n            isSelected: false,\n            url: \"https://quip.network/\",\n            icon: \"https://quip.network/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"Daos.world\",\n            desc: \"Dao 将吞噬世界\",\n            isSelected: false,\n            url: \"https://daos.world/\",\n            icon: \"https://daos.world/favicon.ico\"\n        }\n    ];\n    // 监听应用数据\n    const monitoringAppsData = [\n        {\n            id: 1,\n            name: \"Pods\",\n            desc: \"链上播客平台。发布、发现、拥有。\",\n            isSelected: false,\n            url: \"https://pods.media/\",\n            icon: \"https://pods.media/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Arpeggi\",\n            desc: \"采样、混音、上传和签署音乐\",\n            isSelected: false,\n            url: \"https://arpeggi.io/\",\n            icon: \"https://arpeggi.io/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Spinamp\",\n            desc: \"探索、管理、分享和聆听\",\n            isSelected: false,\n            url: \"https://spinamp.xyz/\",\n            icon: \"https://spinamp.xyz/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Spores\",\n            desc: \"与世界共同打造热门作品\",\n            isSelected: false,\n            url: \"https://spores.app/\",\n            icon: \"https://spores.app/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Staxe\",\n            desc: \"成为心仪艺术家的创作合作伙伴\",\n            isSelected: false,\n            url: \"https://staxe.xyz/\",\n            icon: \"https://staxe.xyz/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"anotherblock\",\n            desc: \"拥有您最喜爱歌曲的股权份额\",\n            isSelected: false,\n            url: \"https://anotherblock.io/\",\n            icon: \"https://anotherblock.io/favicon.ico\"\n        }\n    ];\n    // 根据当前标签获取对应的数据\n    const getCurrentTabData = ()=>{\n        switch(currentTab){\n            case 0:\n                return allAppsData;\n            case 1:\n                return exchangeAppsData;\n            case 2:\n                return earnAppsData;\n            case 3:\n                return socialMediaAppsData;\n            case 4:\n                return managementAppsData;\n            case 5:\n                return monitoringAppsData;\n            default:\n                return allAppsData;\n        }\n    };\n    const dataList = getCurrentTabData();\n    const handleOpenUrl = (url)=>{\n        window.location.href = url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"network-screen.tsx:420\",\n        \"data-in\": \"NetworkScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        minHeight: \"100vh\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"network-screen.tsx:421\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                alignItems: \"center\",\n                mb: 32,\n                justifyContent: \"space-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                        title: \"\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                        \"data-at\": \"network-screen.tsx:423\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Input\",\n                        placeholder: \"搜索或输入网址\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"network-screen.tsx:424\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"View\",\n                        flexDirection: \"row\",\n                        justifyContent: \"space-between\",\n                        ml: 30,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:426\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:429\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net2_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:432\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"network-screen.tsx:437-446\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                bg: \"#02A9DE\",\n                width: 343,\n                height: 80,\n                borderRadius: 20,\n                margin: \"auto\",\n                pt: 6,\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                        \"data-at\": \"network-screen.tsx:447\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 70,\n                        height: 70,\n                        mr: 10\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"network-screen.tsx:448\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"View\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"network-screen.tsx:449\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$black1\",\n                                fontWeight: \"bold\",\n                                fontSize: 14,\n                                children: \"用作资金的免费 NFT\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"network-screen.tsx:452\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$black1\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mt: 4,\n                                children: \"获取特别的 NFT 来为钱包注入资金。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"network-screen.tsx:459\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 20,\n                rowGap: 16,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                        \"data-at\": \"network-screen.tsx:460\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Text\",\n                        children: \"热门应用\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"network-screen.tsx:461\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        children: tabList.map((i, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>setCurrentTab(index),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                    \"data-at\": \"network-screen.tsx:464\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"View\",\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: i\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 41\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            \"data-at\": \"network-screen.tsx:468\",\n                                            \"data-in\": \"NetworkScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: i\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 72\n                                        }, this),\n                                        currentTab === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveUnderline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 42\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this)\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 38\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"network-screen.tsx:475-480\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"YStack\",\n                        py: 10,\n                        flex: 1,\n                        maxHeight: \"60vh\",\n                        overflow: \"scroll\",\n                        children: dataList.map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"network-screen.tsx:482\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                mb: 16,\n                                onPress: ()=>handleOpenUrl(i.url),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                        \"data-at\": \"network-screen.tsx:483-490\",\n                                        \"data-in\": \"NetworkScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: {\n                                            uri: i.icon\n                                        },\n                                        width: 40,\n                                        height: 40,\n                                        rounded: 20,\n                                        mr: 10,\n                                        bg: \"$white1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                        \"data-at\": \"network-screen.tsx:491\",\n                                        \"data-in\": \"NetworkScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                                \"data-at\": \"network-screen.tsx:492\",\n                                                \"data-in\": \"NetworkScreen\",\n                                                \"data-is\": \"XStack\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                        \"data-at\": \"network-screen.tsx:493\",\n                                                        \"data-in\": \"NetworkScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        mr: 20,\n                                                        children: i.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    i.isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                        \"data-at\": \"network-screen.tsx:495-503\",\n                                                        \"data-in\": \"NetworkScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        bg: \"#141519\",\n                                                        rounded: 10,\n                                                        width: 55,\n                                                        height: 22,\n                                                        fontSize: 12,\n                                                        textAlign: \"center\",\n                                                        lineHeight: 22,\n                                                        children: \"精选\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 36\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"network-screen.tsx:508\",\n                                                \"data-in\": \"NetworkScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"$color10\",\n                                                fontSize: 14,\n                                                mt: 11,\n                                                children: i.desc\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, i.id, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 30\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 403,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"network-screen.tsx:515\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"YStack\",\n                        mt: 50\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_9__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n        lineNumber: 361,\n        columnNumber: 10\n    }, this);\n}\n_s(NetworkScreen, \"k5oVBT4gVizGZi8FjIw9Gkdgvrs=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c3 = NetworkScreen;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"ActiveUnderline\");\n$RefreshReg$(_c3, \"NetworkScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/network-screen.tsx\n"));

/***/ })

});