"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/network",{

/***/ "../../packages/app/features/wallet/network-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/network-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NetworkScreen: function() { return /* binding */ NetworkScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/net1.png */ \"../../packages/assets/images/wallet/net1.png\");\n/* harmony import */ var _assets_images_wallet_net2_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/net2.png */ \"../../packages/assets/images/wallet/net2.png\");\n/* harmony import */ var _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/net3.png */ \"../../packages/assets/images/wallet/net3.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 20\n});\n_c = Underline;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst ActiveUnderline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = ActiveUnderline;\nfunction NetworkScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const tabList = [\n        \"全部\",\n        \"交换\",\n        \"赚取\",\n        \"社交媒体\",\n        \"管理\",\n        \"监听\"\n    ];\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    // 全部应用数据\n    const allAppsData = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true,\n            url: \"https://aerodrome.finance/\",\n            icon: \"https://aerodrome.finance/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"通过汇集做市交换代币并赚取费用\",\n            isSelected: false,\n            url: \"https://app.uniswap.org/swap?disableNFTs=true\",\n            icon: \"https://app.uniswap.org/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Toshi Mart\",\n            desc: \"Base 上的模因币生成器和交易平台。\",\n            isSelected: true,\n            url: \"https://toshi.fun/\",\n            icon: \"https://toshi.fun/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Matcha\",\n            desc: \"0x 开发的 DEX 聚合器\",\n            isSelected: true,\n            url: \"https://matcha.xyz/\",\n            icon: \"https://matcha.xyz/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Alien Base\",\n            desc: \"为非传统地球人而打造的 Base 原生去中心化交易所\",\n            isSelected: false,\n            url: \"https://app.alienbase.xyz/\",\n            icon: \"https://app.alienbase.xyz/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"PancakeSwap\",\n            desc: \"交易并赚取加密货币\",\n            isSelected: false,\n            url: \"https://pancakeswap.finance/\",\n            icon: \"https://pancakeswap.finance/favicon.ico\"\n        },\n        {\n            id: 7,\n            name: \"Seamless Protocol\",\n            desc: \"实现收益最大化\",\n            isSelected: true,\n            url: \"https://seamlessprotocol.com/\",\n            icon: \"https://seamlessprotocol.com/favicon.ico\"\n        },\n        {\n            id: 8,\n            name: \"Plaza Finance\",\n            desc: \"债券和杠杆\",\n            isSelected: true,\n            url: \"https://plaza.finance/\",\n            icon: \"https://plaza.finance/favicon.ico\"\n        },\n        {\n            id: 9,\n            name: \"MetaLend\",\n            desc: \"在 5 分钟内实现加密货币收益最大化\",\n            isSelected: true,\n            url: \"https://metalend.fi/\",\n            icon: \"https://metalend.fi/favicon.ico\"\n        },\n        {\n            id: 10,\n            name: \"DIMO\",\n            desc: \"更聪明地驾驶并获得奖励\",\n            isSelected: false,\n            url: \"https://dimo.zone/\",\n            icon: \"https://dimo.zone/favicon.ico\"\n        },\n        {\n            id: 11,\n            name: \"ether.fi\",\n            desc: \"通过 ether.fi 质押、消费、赚钱\",\n            isSelected: false,\n            url: \"https://www.ether.fi/\",\n            icon: \"https://www.ether.fi/favicon.ico\"\n        },\n        {\n            id: 12,\n            name: \"Moonwell\",\n            desc: \"使贷款变得简单\",\n            isSelected: false,\n            url: \"https://moonwell.fi/\",\n            icon: \"https://moonwell.fi/favicon.ico\"\n        }\n    ];\n    // 交换应用数据\n    const exchangeAppsData = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true,\n            url: \"https://aerodrome.finance/\",\n            icon: \"https://aerodrome.finance/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"通过汇集做市交换代币并赚取费用\",\n            isSelected: false,\n            url: \"https://app.uniswap.org/swap?disableNFTs=true\",\n            icon: \"https://app.uniswap.org/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Toshi Mart\",\n            desc: \"Base 上的模因币生成器和交易平台。\",\n            isSelected: true,\n            url: \"https://toshi.fun/\",\n            icon: \"https://toshi.fun/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Matcha\",\n            desc: \"0x 开发的 DEX 聚合器\",\n            isSelected: true,\n            url: \"https://matcha.xyz/\",\n            icon: \"https://matcha.xyz/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Alien Base\",\n            desc: \"为非传统地球人而打造的 Base 原生去中心化交易所\",\n            isSelected: false,\n            url: \"https://app.alienbase.xyz/\",\n            icon: \"https://app.alienbase.xyz/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"PancakeSwap\",\n            desc: \"交易并赚取加密货币\",\n            isSelected: false,\n            url: \"https://pancakeswap.finance/\",\n            icon: \"https://pancakeswap.finance/favicon.ico\"\n        }\n    ];\n    // 赚取应用数据\n    const earnAppsData = [\n        {\n            id: 1,\n            name: \"Seamless Protocol\",\n            desc: \"实现收益最大化\",\n            isSelected: true,\n            url: \"https://seamlessprotocol.com/\",\n            icon: \"https://seamlessprotocol.com/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Plaza Finance\",\n            desc: \"债券和杠杆\",\n            isSelected: true,\n            url: \"https://plaza.finance/\",\n            icon: \"https://plaza.finance/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"MetaLend\",\n            desc: \"在 5 分钟内实现加密货币收益最大化\",\n            isSelected: true,\n            url: \"https://metalend.fi/\",\n            icon: \"https://metalend.fi/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"ether.fi\",\n            desc: \"通过 ether.fi 质押、消费、赚钱\",\n            isSelected: false,\n            url: \"https://www.ether.fi/\",\n            icon: \"https://www.ether.fi/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Moonwell\",\n            desc: \"使贷款变得简单\",\n            isSelected: false,\n            url: \"https://moonwell.fi/\",\n            icon: \"https://moonwell.fi/favicon.ico\"\n        }\n    ];\n    // 社交媒体应用数据\n    const socialMediaAppsData = [\n        {\n            id: 1,\n            name: \"moshi.cam\",\n            desc: \"链上照片共享应用程序\",\n            isSelected: false,\n            url: \"https://moshi.cam/\",\n            icon: \"https://moshi.cam/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Virtuals\",\n            desc: \"链上 AI 代理协会\",\n            isSelected: true,\n            url: \"https://virtuals.io/\",\n            icon: \"https://virtuals.io/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Hypersub\",\n            desc: \"订阅并赚取\",\n            isSelected: false,\n            url: \"https://hypersub.withfabric.xyz/\",\n            icon: \"https://hypersub.withfabric.xyz/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Soulbound TV\",\n            desc: \"实现电视去中心化，新一代直播平台。\",\n            isSelected: false,\n            url: \"https://soulbound.tv/\",\n            icon: \"https://soulbound.tv/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Rad TV\",\n            desc: \"为创作者和粉丝提供链上视频流\",\n            isSelected: false,\n            url: \"https://rad.tv/\",\n            icon: \"https://rad.tv/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"Aura\",\n            desc: \"个人链上克隆体\",\n            isSelected: false,\n            url: \"https://aura.network/\",\n            icon: \"https://aura.network/favicon.ico\"\n        }\n    ];\n    // 管理应用数据\n    const managementAppsData = [\n        {\n            id: 1,\n            name: \"Onboard\",\n            desc: \"成为 Onboard 商家，每天最多可赚取 100...\",\n            isSelected: true,\n            url: \"https://onboard.xyz/\",\n            icon: \"https://onboard.xyz/favicon.ico\"\n        },\n        {\n            id: 2,\n            name: \"Webacy\",\n            desc: \"管理钱包安全。\",\n            isSelected: false,\n            url: \"https://webacy.com/\",\n            icon: \"https://webacy.com/favicon.ico\"\n        },\n        {\n            id: 3,\n            name: \"Dune\",\n            desc: \"在 Base 上开发应用程序\",\n            isSelected: false,\n            url: \"https://dune.com/\",\n            icon: \"https://dune.com/favicon.ico\"\n        },\n        {\n            id: 4,\n            name: \"Venice\",\n            desc: \"私有且不受审查的人工智能\",\n            isSelected: false,\n            url: \"https://venice.ai/\",\n            icon: \"https://venice.ai/favicon.ico\"\n        },\n        {\n            id: 5,\n            name: \"Quip Network\",\n            desc: \"保护您的资产免受量子计算机黑客的攻击\",\n            isSelected: false,\n            url: \"https://quip.network/\",\n            icon: \"https://quip.network/favicon.ico\"\n        },\n        {\n            id: 6,\n            name: \"Daos.world\",\n            desc: \"Dao 将吞噬世界\",\n            isSelected: false,\n            url: \"https://daos.world/\",\n            icon: \"https://daos.world/favicon.ico\"\n        }\n    ];\n    // 监听应用数据\n    const monitoringAppsData = [\n        {\n            id: 1,\n            name: \"DIMO\",\n            desc: \"更聪明地驾驶并获得奖励\",\n            isSelected: false,\n            url: \"https://dimo.zone/\",\n            icon: \"https://dimo.zone/favicon.ico\"\n        }\n    ];\n    // 根据当前标签获取对应的数据\n    const getCurrentTabData = ()=>{\n        switch(currentTab){\n            case 0:\n                return allAppsData;\n            case 1:\n                return exchangeAppsData;\n            case 2:\n                return earnAppsData;\n            case 3:\n                return socialMediaAppsData;\n            case 4:\n                return managementAppsData;\n            case 5:\n                return monitoringAppsData;\n            default:\n                return allAppsData;\n        }\n    };\n    const dataList = getCurrentTabData();\n    const onMnemonicClick = ()=>{\n        router.push(\"/wallet/password\");\n    };\n    const handleOpenUrl = (url)=>{\n        window.location.href = url;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"network-screen.tsx:390\",\n        \"data-in\": \"NetworkScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        minHeight: \"100vh\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"network-screen.tsx:391\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                alignItems: \"center\",\n                mb: 32,\n                justifyContent: \"space-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                        title: \"\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                        \"data-at\": \"network-screen.tsx:393\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Input\",\n                        placeholder: \"搜索或输入网址\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"network-screen.tsx:394\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"View\",\n                        flexDirection: \"row\",\n                        justifyContent: \"space-between\",\n                        ml: 30,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:396\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:399\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net2_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>{},\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                    \"data-at\": \"network-screen.tsx:402\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                    width: 16,\n                                    height: 16,\n                                    mr: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 330,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"network-screen.tsx:407-416\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                bg: \"#02A9DE\",\n                width: 343,\n                height: 80,\n                borderRadius: 20,\n                margin: \"auto\",\n                pt: 6,\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                        \"data-at\": \"network-screen.tsx:417\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_net1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 70,\n                        height: 70,\n                        mr: 10\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"network-screen.tsx:418\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"View\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"network-screen.tsx:419\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$black1\",\n                                fontWeight: \"bold\",\n                                fontSize: 14,\n                                children: \"用作资金的免费 NFT\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"network-screen.tsx:422\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$black1\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mt: 4,\n                                children: \"获取特别的 NFT 来为钱包注入资金。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"network-screen.tsx:429\",\n                \"data-in\": \"NetworkScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 20,\n                rowGap: 16,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                        \"data-at\": \"network-screen.tsx:430\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"Text\",\n                        children: \"热门应用\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"network-screen.tsx:431\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        children: tabList.map((i, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                                onPress: ()=>setCurrentTab(index),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                    \"data-at\": \"network-screen.tsx:434\",\n                                    \"data-in\": \"NetworkScreen\",\n                                    \"data-is\": \"View\",\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === index ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: i\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 41\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            \"data-at\": \"network-screen.tsx:438\",\n                                            \"data-in\": \"NetworkScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: i\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 72\n                                        }, this),\n                                        currentTab === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveUnderline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 42\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this)\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 38\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"network-screen.tsx:445\",\n                        \"data-in\": \"NetworkScreen\",\n                        \"data-is\": \"YStack\",\n                        py: 10,\n                        children: dataList.map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"network-screen.tsx:447\",\n                                \"data-in\": \"NetworkScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                mb: 16,\n                                onPress: ()=>handleOpenUrl(i.url),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                        \"data-at\": \"network-screen.tsx:448-455\",\n                                        \"data-in\": \"NetworkScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: {\n                                            uri: i.icon\n                                        },\n                                        width: 40,\n                                        height: 40,\n                                        rounded: 20,\n                                        mr: 10,\n                                        bg: \"$white1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                        \"data-at\": \"network-screen.tsx:456\",\n                                        \"data-in\": \"NetworkScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                                \"data-at\": \"network-screen.tsx:457\",\n                                                \"data-in\": \"NetworkScreen\",\n                                                \"data-is\": \"XStack\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                        \"data-at\": \"network-screen.tsx:458\",\n                                                        \"data-in\": \"NetworkScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        mr: 20,\n                                                        children: i.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    i.isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                        \"data-at\": \"network-screen.tsx:460-468\",\n                                                        \"data-in\": \"NetworkScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        bg: \"#141519\",\n                                                        rounded: 10,\n                                                        width: 55,\n                                                        height: 22,\n                                                        fontSize: 12,\n                                                        textAlign: \"center\",\n                                                        lineHeight: 22,\n                                                        children: \"精选\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 36\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"network-screen.tsx:473\",\n                                                \"data-in\": \"NetworkScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"$color10\",\n                                                fontSize: 14,\n                                                mt: 11,\n                                                children: i.desc\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, i.id, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 30\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_9__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/network-screen.tsx\",\n        lineNumber: 329,\n        columnNumber: 10\n    }, this);\n}\n_s(NetworkScreen, \"k5oVBT4gVizGZi8FjIw9Gkdgvrs=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c3 = NetworkScreen;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"ActiveUnderline\");\n$RefreshReg$(_c3, \"NetworkScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/network-screen.tsx\n"));

/***/ })

});