"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/exchange",{

/***/ "../../packages/app/features/wallet/exchange-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/exchange-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExchangeScreen: function() { return /* binding */ ExchangeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/exchange.png */ \"../../packages/assets/images/wallet/exchange.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ExchangeScreen() {\n    _s();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // 获取当前账户\n    const currentAccount = walletStore.currentAccount;\n    // 初始化钱包和交易数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // 确保钱包数据先加载\n        walletStore.init();\n        // 然后加载交易数据\n        transactionStore.loadTransactions();\n    }, []);\n    // 监听钱包状态变化，确保钱包加载完成后重新获取交易记录\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentAccount && currentAccount.accountId && !transactionStore.isLoading) {\n            var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n            // 当钱包账户加载完成且交易数据不在加载中时，重新获取交易记录\n            const addresses = [\n                (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n                (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n                (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n                (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n            ].filter(Boolean);\n            // 如果有地址但没有交易记录，可能需要重新加载\n            if (addresses.length > 0) {\n                const currentTransactions = transactionStore.getTransactionsByAddresses(addresses);\n                if (currentTransactions.length === 0 && transactionStore.transactions.length > 0) {\n                    // 有交易数据但当前账户没有匹配的交易，可能是账户刚加载完成\n                    console.log(\"钱包账户已加载，重新获取交易记录\");\n                }\n            }\n        }\n    }, [\n        currentAccount === null || currentAccount === void 0 ? void 0 : currentAccount.accountId,\n        transactionStore.isLoading\n    ]);\n    // 获取当前账户的所有链的交易\n    const transactions = currentAccount && currentAccount.accountId ? (()=>{\n        var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n        // 获取所有链的地址\n        const addresses = [\n            (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n            (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n            (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n            (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n        ].filter(Boolean); // 过滤掉undefined/null值\n        // 使用新的去重方法获取交易记录\n        return transactionStore.getTransactionsByAddresses(addresses);\n    })() : [];\n    // 按日期分组交易\n    const groupTransactionsByDate = (transactions)=>{\n        const groups = {};\n        transactions.forEach((tx)=>{\n            const date = new Date(tx.timestamp);\n            const today = new Date();\n            const yesterday = new Date(today);\n            yesterday.setDate(yesterday.getDate() - 1);\n            let dateKey = \"\";\n            if (date.toDateString() === today.toDateString()) {\n                dateKey = t(\"time.today\") || \"今天\";\n            } else if (date.toDateString() === yesterday.toDateString()) {\n                dateKey = t(\"time.yesterday\") || \"昨天\";\n            } else {\n                dateKey = date.toLocaleDateString();\n            }\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push((0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.formatTransaction)(tx));\n        });\n        return groups;\n    };\n    const groupedTransactions = groupTransactionsByDate(transactions);\n    // 获取链图标\n    const getChainIcon = (chain)=>{\n        switch(chain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            // 暂时使用同一个图标\n            case \"btc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            default:\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n        }\n    };\n    if (transactionStore.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n            \"data-at\": \"exchange-screen.tsx:115\",\n            \"data-in\": \"ExchangeScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                \"data-at\": \"exchange-screen.tsx:116\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"加载中...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n            lineNumber: 100,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n        \"data-at\": \"exchange-screen.tsx:122\",\n        \"data-in\": \"ExchangeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        flex: 1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                \"data-at\": \"exchange-screen.tsx:123\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 30,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"exchange-screen.tsx:124\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"XStack\",\n                        pl: 16,\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.H3, {\n                                \"data-at\": \"exchange-screen.tsx:125\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"H3\",\n                                children: \"交易\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:126\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"#4575FF\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mr: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:132\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        flex: 1,\n                        mb: currentAccount ? 80 : 140,\n                        children: Object.keys(groupedTransactions).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                            \"data-at\": \"exchange-screen.tsx:134\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"YStack\",\n                            flex: 1,\n                            alignItems: \"center\",\n                            mt: 100,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                    \"data-at\": \"exchange-screen.tsx:135\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"XStack\",\n                                    justifyContent: \"center\",\n                                    mt: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                        \"data-at\": \"exchange-screen.tsx:136\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                        width: 173,\n                                        height: 142\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                    \"data-at\": \"exchange-screen.tsx:138\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"YStack\",\n                                    justifyContent: \"center\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:139\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$white1\",\n                                            fontSize: 16,\n                                            fontWeight: \"bold\",\n                                            textAlign: \"center\",\n                                            mb: 10,\n                                            children: \"还没有交易\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:140\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$accent11\",\n                                            width: 280,\n                                            textAlign: \"center\",\n                                            margin: \"auto\",\n                                            children: \"一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 60\n                        }, this) : Object.entries(groupedTransactions).map((param)=>{\n                            let [date, txs] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                \"data-at\": \"exchange-screen.tsx:145\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"YStack\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                        \"data-at\": \"exchange-screen.tsx:146\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mb: 10,\n                                        children: date\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this),\n                                    txs.map((tx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                            \"data-at\": \"exchange-screen.tsx:150\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"XStack\",\n                                            justifyContent: \"space-between\",\n                                            mt: 25,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:151\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"XStack\",\n                                                    alignItems: \"center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:152\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            position: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                                                \"data-at\": \"exchange-screen.tsx:153\",\n                                                                \"data-in\": \"ExchangeScreen\",\n                                                                \"data-is\": \"Image\",\n                                                                source: getChainIcon(tx.chain),\n                                                                width: 38,\n                                                                height: 38\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:155\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            ml: 10,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:156\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 14,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"white\",\n                                                                    children: (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.getTypeDisplayText)(tx.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:159\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 12,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"$accent11\",\n                                                                    mt: 2,\n                                                                    children: tx.displayAddress\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:164\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"YStack\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"flex-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:165\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 14,\n                                                            fontWeight: \"bold\",\n                                                            color: \"white\",\n                                                            children: tx.displayAmount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:168\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 12,\n                                                            fontWeight: \"bold\",\n                                                            color: \"$accent11\",\n                                                            mt: 2,\n                                                            children: tx.displayTime\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        tx.txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:172\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 10,\n                                                            color: \"$accent11\",\n                                                            mt: 1,\n                                                            children: [\n                                                                tx.txHash.slice(0, 8),\n                                                                \"...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 36\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, tx.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 32\n                                        }, this))\n                                ]\n                            }, date, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 82\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    !currentAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:186\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        position: \"absolute\",\n                        bottom: 90,\n                        left: 16,\n                        right: 16,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            \"data-at\": \"exchange-screen.tsx:187\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Button\",\n                            rounded: 30,\n                            width: \"100%\",\n                            bg: \"$accent11\",\n                            onPress: ()=>router.push(\"/wallet/buyCoin\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:188\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$white1\",\n                                children: \"将加密货币添加到您的钱包\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 29\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_10__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n        lineNumber: 104,\n        columnNumber: 10\n    }, this);\n}\n_s(ExchangeScreen, \"bI91Hn7j8cjELTH2QtyxJakv90I=\", false, function() {\n    return [\n        app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = ExchangeScreen;\nvar _c;\n$RefreshReg$(_c, \"ExchangeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/exchange-screen.tsx\n"));

/***/ })

});