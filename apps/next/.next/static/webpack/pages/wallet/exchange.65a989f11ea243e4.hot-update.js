"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/exchange",{

/***/ "../../packages/app/features/wallet/exchange-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/exchange-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExchangeScreen: function() { return /* binding */ ExchangeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/exchange.png */ \"../../packages/assets/images/wallet/exchange.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ExchangeScreen() {\n    _s();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore)();\n    // 初始化交易数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        transactionStore.loadTransactions();\n    }, []);\n    // 获取当前账户的所有链的交易\n    const currentAccount = walletStore.currentAccount;\n    const transactions = currentAccount ? (()=>{\n        var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n        // 获取所有链的地址\n        const addresses = [\n            (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n            (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n            (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n            (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n        ].filter(Boolean); // 过滤掉undefined/null值\n        // 使用新的去重方法获取交易记录\n        return transactionStore.getTransactionsByAddresses(addresses);\n    })() : [];\n    // 按日期分组交易\n    const groupTransactionsByDate = (transactions)=>{\n        const groups = {};\n        transactions.forEach((tx)=>{\n            const date = new Date(tx.timestamp);\n            const today = new Date();\n            const yesterday = new Date(today);\n            yesterday.setDate(yesterday.getDate() - 1);\n            let dateKey = \"\";\n            if (date.toDateString() === today.toDateString()) {\n                dateKey = t(\"time.today\") || \"今天\";\n            } else if (date.toDateString() === yesterday.toDateString()) {\n                dateKey = t(\"time.yesterday\") || \"昨天\";\n            } else {\n                dateKey = date.toLocaleDateString();\n            }\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push((0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.formatTransaction)(tx));\n        });\n        return groups;\n    };\n    const groupedTransactions = groupTransactionsByDate(transactions);\n    // 获取链图标\n    const getChainIcon = (chain)=>{\n        switch(chain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            // 暂时使用同一个图标\n            case \"btc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            default:\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n        }\n    };\n    if (transactionStore.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n            \"data-at\": \"exchange-screen.tsx:86\",\n            \"data-in\": \"ExchangeScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                \"data-at\": \"exchange-screen.tsx:87\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"加载中...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n            lineNumber: 76,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"exchange-screen.tsx:93\",\n        \"data-in\": \"ExchangeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        flex: 1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"exchange-screen.tsx:94\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 30,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"exchange-screen.tsx:95\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"XStack\",\n                        pl: 16,\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.H3, {\n                                \"data-at\": \"exchange-screen.tsx:96\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"H3\",\n                                children: \"交易\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:97\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"#4575FF\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mr: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:103\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        flex: 1,\n                        mb: currentAccount ? 80 : 140,\n                        children: Object.keys(groupedTransactions).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                            \"data-at\": \"exchange-screen.tsx:105\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"YStack\",\n                            flex: 1,\n                            justifyContent: \"center\",\n                            alignItems: \"center\",\n                            mt: 0,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                    \"data-at\": \"exchange-screen.tsx:106\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"XStack\",\n                                    justifyContent: \"center\",\n                                    mt: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Image, {\n                                        \"data-at\": \"exchange-screen.tsx:107\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                        width: 173,\n                                        height: 142\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                                    \"data-at\": \"exchange-screen.tsx:109\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"YStack\",\n                                    justifyContent: \"center\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:110\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$white1\",\n                                            fontSize: 16,\n                                            fontWeight: \"bold\",\n                                            textAlign: \"center\",\n                                            mb: 10,\n                                            children: \"还没有交易\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:111\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$accent11\",\n                                            width: 280,\n                                            textAlign: \"center\",\n                                            margin: \"auto\",\n                                            children: \"一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 60\n                        }, this) : Object.entries(groupedTransactions).map((param)=>{\n                            let [date, txs] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                                \"data-at\": \"exchange-screen.tsx:116\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"YStack\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                        \"data-at\": \"exchange-screen.tsx:117\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mb: 10,\n                                        children: date\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    txs.map((tx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                            \"data-at\": \"exchange-screen.tsx:121\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"XStack\",\n                                            justifyContent: \"space-between\",\n                                            mt: 25,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:122\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"XStack\",\n                                                    alignItems: \"center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:123\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            position: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Image, {\n                                                                \"data-at\": \"exchange-screen.tsx:124\",\n                                                                \"data-in\": \"ExchangeScreen\",\n                                                                \"data-is\": \"Image\",\n                                                                source: getChainIcon(tx.chain),\n                                                                width: 38,\n                                                                height: 38\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:126\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            ml: 10,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:127\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 14,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"white\",\n                                                                    children: (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.getTypeDisplayText)(tx.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:130\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 12,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"$accent11\",\n                                                                    mt: 2,\n                                                                    children: tx.displayAddress\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 112,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:135\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"YStack\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"flex-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:136\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 14,\n                                                            fontWeight: \"bold\",\n                                                            color: \"white\",\n                                                            children: tx.displayAmount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:139\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 12,\n                                                            fontWeight: \"bold\",\n                                                            color: \"$accent11\",\n                                                            mt: 2,\n                                                            children: tx.displayTime\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        tx.txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:143\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 10,\n                                                            color: \"$accent11\",\n                                                            mt: 1,\n                                                            children: [\n                                                                tx.txHash.slice(0, 8),\n                                                                \"...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 36\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, tx.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 32\n                                        }, this))\n                                ]\n                            }, date, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 82\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    currentAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:157\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        position: \"absolute\",\n                        bottom: 80,\n                        left: 16,\n                        right: 16,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            \"data-at\": \"exchange-screen.tsx:158\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Button\",\n                            rounded: 30,\n                            width: \"100%\",\n                            bg: \"$accent11\",\n                            onPress: ()=>router.push(\"/wallet/buyCoin\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:159\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$white1\",\n                                children: \"将加密货币添加到您的钱包\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 28\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_9__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n        lineNumber: 80,\n        columnNumber: 10\n    }, this);\n}\n_s(ExchangeScreen, \"hBsHZ/8PvhK8/D5S2MIRbIxeP+g=\", false, function() {\n    return [\n        app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore\n    ];\n});\n_c = ExchangeScreen;\nvar _c;\n$RefreshReg$(_c, \"ExchangeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/exchange-screen.tsx\n"));

/***/ })

});