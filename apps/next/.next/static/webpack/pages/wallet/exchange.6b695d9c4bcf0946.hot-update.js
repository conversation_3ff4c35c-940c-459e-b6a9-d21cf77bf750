"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/exchange",{

/***/ "../../packages/app/features/wallet/exchange-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/exchange-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExchangeScreen: function() { return /* binding */ ExchangeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/exchange.png */ \"../../packages/assets/images/wallet/exchange.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ExchangeScreen() {\n    _s();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore)();\n    // 初始化交易数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        transactionStore.loadTransactions();\n    }, []);\n    // 获取当前账户的所有链的交易\n    const currentAccount = walletStore.currentAccount;\n    const transactions = currentAccount ? (()=>{\n        var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n        // 获取所有链的地址\n        const addresses = [\n            (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n            (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n            (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n            (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n        ].filter(Boolean); // 过滤掉undefined/null值\n        // 使用新的去重方法获取交易记录\n        return transactionStore.getTransactionsByAddresses(addresses);\n    })() : [];\n    // 按日期分组交易\n    const groupTransactionsByDate = (transactions)=>{\n        const groups = {};\n        transactions.forEach((tx)=>{\n            const date = new Date(tx.timestamp);\n            const today = new Date();\n            const yesterday = new Date(today);\n            yesterday.setDate(yesterday.getDate() - 1);\n            let dateKey = \"\";\n            if (date.toDateString() === today.toDateString()) {\n                dateKey = t(\"time.today\") || \"今天\";\n            } else if (date.toDateString() === yesterday.toDateString()) {\n                dateKey = t(\"time.yesterday\") || \"昨天\";\n            } else {\n                dateKey = date.toLocaleDateString();\n            }\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push((0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.formatTransaction)(tx));\n        });\n        return groups;\n    };\n    const groupedTransactions = groupTransactionsByDate(transactions);\n    // 获取链图标\n    const getChainIcon = (chain)=>{\n        switch(chain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            // 暂时使用同一个图标\n            case \"btc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            default:\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n        }\n    };\n    if (transactionStore.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n            \"data-at\": \"exchange-screen.tsx:86\",\n            \"data-in\": \"ExchangeScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                \"data-at\": \"exchange-screen.tsx:87\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"加载中...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n            lineNumber: 76,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"exchange-screen.tsx:93\",\n        \"data-in\": \"ExchangeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        px: 16,\n        py: 30,\n        minHeight: \"100vh\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"exchange-screen.tsx:94\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                justifyContent: \"space-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.H3, {\n                        \"data-at\": \"exchange-screen.tsx:95\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"H3\",\n                        children: \"交易\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                        \"data-at\": \"exchange-screen.tsx:96\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"Text\",\n                        color: \"#4575FF\",\n                        fontSize: 14,\n                        fontWeight: 500,\n                        mr: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            Object.keys(groupedTransactions).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"exchange-screen.tsx:103\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                mt: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"exchange-screen.tsx:104\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"center\",\n                        mt: 0,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Image, {\n                            \"data-at\": \"exchange-screen.tsx:105\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                            width: 173,\n                            height: 142\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:107\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        justifyContent: \"center\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:108\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$white1\",\n                                fontSize: 16,\n                                fontWeight: \"bold\",\n                                textAlign: \"center\",\n                                mb: 10,\n                                children: \"还没有交易\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:109\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$accent11\",\n                                width: 280,\n                                textAlign: \"center\",\n                                margin: \"auto\",\n                                children: \"一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 89,\n                columnNumber: 56\n            }, this) : Object.entries(groupedTransactions).map((param)=>{\n                let [date, txs] = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                    \"data-at\": \"exchange-screen.tsx:114\",\n                    \"data-in\": \"ExchangeScreen\",\n                    \"data-is\": \"YStack\",\n                    mt: 20,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                            \"data-at\": \"exchange-screen.tsx:115\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 14,\n                            fontWeight: \"bold\",\n                            color: \"$accent11\",\n                            mb: 10,\n                            children: date\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        txs.map((tx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"exchange-screen.tsx:119\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"space-between\",\n                                mt: 25,\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                        \"data-at\": \"exchange-screen.tsx:120\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"XStack\",\n                                        alignItems: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.View, {\n                                                \"data-at\": \"exchange-screen.tsx:121\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"View\",\n                                                position: \"relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Image, {\n                                                    \"data-at\": \"exchange-screen.tsx:122\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"Image\",\n                                                    source: getChainIcon(tx.chain),\n                                                    width: 38,\n                                                    height: 38\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.View, {\n                                                \"data-at\": \"exchange-screen.tsx:124\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"View\",\n                                                ml: 10,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        \"data-at\": \"exchange-screen.tsx:125\",\n                                                        \"data-in\": \"ExchangeScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        color: \"white\",\n                                                        children: (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.getTypeDisplayText)(tx.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                        \"data-at\": \"exchange-screen.tsx:128\",\n                                                        \"data-in\": \"ExchangeScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        fontWeight: \"bold\",\n                                                        color: \"$accent11\",\n                                                        mt: 2,\n                                                        children: tx.displayAddress\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                                        \"data-at\": \"exchange-screen.tsx:133\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        flexDirection: \"column\",\n                                        alignItems: \"flex-end\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                \"data-at\": \"exchange-screen.tsx:134\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                color: \"white\",\n                                                children: tx.displayAmount\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                \"data-at\": \"exchange-screen.tsx:137\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 12,\n                                                fontWeight: \"bold\",\n                                                color: \"$accent11\",\n                                                mt: 2,\n                                                children: tx.displayTime\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this),\n                                            tx.txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                                                \"data-at\": \"exchange-screen.tsx:141\",\n                                                \"data-in\": \"ExchangeScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 10,\n                                                color: \"$accent11\",\n                                                mt: 1,\n                                                children: [\n                                                    tx.txHash.slice(0, 8),\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 32\n                                            }, this) : null\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, tx.id, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 28\n                            }, this))\n                    ]\n                }, date, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 78\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                \"data-at\": \"exchange-screen.tsx:152\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Button\",\n                rounded: 30,\n                position: \"absolute\",\n                bottom: 20,\n                width: \"90%\",\n                bg: \"$accent11\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_8__.Text, {\n                    \"data-at\": \"exchange-screen.tsx:153\",\n                    \"data-in\": \"ExchangeScreen\",\n                    \"data-is\": \"Text\",\n                    color: \"$white1\",\n                    children: \"将加密货币添加到您的钱包\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_9__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n        lineNumber: 80,\n        columnNumber: 10\n    }, this);\n}\n_s(ExchangeScreen, \"hBsHZ/8PvhK8/D5S2MIRbIxeP+g=\", false, function() {\n    return [\n        app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore\n    ];\n});\n_c = ExchangeScreen;\nvar _c;\n$RefreshReg$(_c, \"ExchangeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/exchange-screen.tsx\n"));

/***/ })

});