"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/setting",{

/***/ "../../packages/app/features/wallet/setting-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/setting-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingScreen: function() { return /* binding */ SettingScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_set1_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/set1.png */ \"../../packages/assets/images/wallet/set1.png\");\n/* harmony import */ var _assets_images_wallet_set3_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/set3.png */ \"../../packages/assets/images/wallet/set3.png\");\n/* harmony import */ var _assets_images_wallet_set4_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/set4.png */ \"../../packages/assets/images/wallet/set4.png\");\n/* harmony import */ var _assets_images_wallet_set5_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/wallet/set5.png */ \"../../packages/assets/images/wallet/set5.png\");\n/* harmony import */ var _assets_images_wallet_set6_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/wallet/set6.png */ \"../../packages/assets/images/wallet/set6.png\");\n/* harmony import */ var _assets_images_wallet_set7_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/wallet/set7.png */ \"../../packages/assets/images/wallet/set7.png\");\n/* harmony import */ var _assets_images_wallet_user_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/wallet/user.png */ \"../../packages/assets/images/wallet/user.png\");\n/* harmony import */ var _assets_images_wallet_add_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/wallet/add.png */ \"../../packages/assets/images/wallet/add.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SettingScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n        \"data-at\": \"setting-screen.tsx:28\",\n        \"data-in\": \"SettingScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.NavBar, {\n                title: \"Settings\",\n                onBack: ()=>router.back()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:30\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                width: \"100%\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Input, {\n                    \"data-at\": \"setting-screen.tsx:31\",\n                    \"data-in\": \"SettingScreen\",\n                    \"data-is\": \"Input\",\n                    placeholder: \"Search\",\n                    width: \"100%\",\n                    rounded: 30\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:33\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                mt: 32,\n                pl: 16,\n                items: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Image, {\n                        \"data-at\": \"setting-screen.tsx:34\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_set1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 12,\n                        height: 12,\n                        mr: 6\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.H5, {\n                        \"data-at\": \"setting-screen.tsx:35\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"H5\",\n                        children: \"钱包\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:37-46\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                bg: \"#001032\",\n                rounded: 20,\n                height: 66,\n                my: 20,\n                justify: \"space-between\",\n                px: 16,\n                items: \"center\",\n                onPress: ()=>router.push(\"/user/profile\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"setting-screen.tsx:47\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"XStack\",\n                        items: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Image, {\n                                    src: _assets_images_wallet_user_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"].src,\n                                    width: 36,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                \"data-at\": \"setting-screen.tsx:51\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"YStack\",\n                                pl: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:52\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        color: \"#8CA5F7\",\n                                        children: \"地址 1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:55\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        color: \"#3873F3\",\n                                        children: \"恢复短语、个人资料、人脉等\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_12__.ChevronRight, {\n                        size: 20,\n                        color: \"$white6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:62-69\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                justify: \"space-between\",\n                px: 16,\n                items: \"center\",\n                bg: \"#141519\",\n                rounded: 20,\n                height: 66,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"setting-screen.tsx:70\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"XStack\",\n                        items: \"center\",\n                        onPress: ()=>router.push(\"/wallet/manager\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Image, {\n                                \"data-at\": \"setting-screen.tsx:71\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_add_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"].src,\n                                width: 20,\n                                height: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                \"data-at\": \"setting-screen.tsx:72\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"YStack\",\n                                pl: 16,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                    \"data-at\": \"setting-screen.tsx:73\",\n                                    \"data-in\": \"SettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    children: \"管理所有钱包\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_12__.ChevronRight, {\n                        size: 20,\n                        color: \"$white6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:78-84\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                my: 20,\n                justify: \"space-between\",\n                px: 16,\n                items: \"center\",\n                onPress: ()=>router.push(\"/wallet/chooseNet\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"setting-screen.tsx:85\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"XStack\",\n                        items: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Image, {\n                                \"data-at\": \"setting-screen.tsx:86\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_set3_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 12,\n                                height: 12\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                \"data-at\": \"setting-screen.tsx:87\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"YStack\",\n                                pl: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:88\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        children: \"网络\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:89\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        color: \"$accent11\",\n                                        children: \"编辑或添加网络\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_12__.ChevronRight, {\n                        size: 20,\n                        color: \"$white6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:96-102\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                my: 10,\n                justify: \"space-between\",\n                px: 16,\n                items: \"center\",\n                onPress: ()=>router.push(\"/wallet/safe\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"setting-screen.tsx:103\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"XStack\",\n                        items: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Image, {\n                                \"data-at\": \"setting-screen.tsx:104\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_set4_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                width: 12,\n                                height: 12\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                \"data-at\": \"setting-screen.tsx:105\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"YStack\",\n                                pl: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:106\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        children: \"安全\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:107\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        color: \"$accent11\",\n                                        children: \"密码、备份、面容 ID\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_12__.ChevronRight, {\n                        size: 20,\n                        color: \"$white6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:114-120\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                my: 10,\n                justify: \"space-between\",\n                px: 16,\n                items: \"center\",\n                onPress: ()=>router.push(\"/wallet/display\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"setting-screen.tsx:121\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"XStack\",\n                        items: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Image, {\n                                \"data-at\": \"setting-screen.tsx:122\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_set5_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                width: 12,\n                                height: 12\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                \"data-at\": \"setting-screen.tsx:123\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"YStack\",\n                                pl: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:124\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        children: \"显示\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:125\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        color: \"$accent11\",\n                                        children: \"深色模式、货币、余额、语言\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_12__.ChevronRight, {\n                        size: 20,\n                        color: \"$white6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:132-138\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                my: 10,\n                justify: \"space-between\",\n                px: 16,\n                items: \"center\",\n                onPress: ()=>router.push(\"/wallet/buyTransfer\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"setting-screen.tsx:139\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"XStack\",\n                        items: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Image, {\n                                \"data-at\": \"setting-screen.tsx:140\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_set6_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                width: 12,\n                                height: 12\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                \"data-at\": \"setting-screen.tsx:141\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"YStack\",\n                                pl: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:142\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        children: \"连接到 Coinbase\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:143\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        color: \"$accent11\",\n                                        children: \"从 Coinbase 购买或转移\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_12__.ChevronRight, {\n                        size: 20,\n                        color: \"$white6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                \"data-at\": \"setting-screen.tsx:150-156\",\n                \"data-in\": \"SettingScreen\",\n                \"data-is\": \"XStack\",\n                my: 10,\n                justify: \"space-between\",\n                px: 16,\n                items: \"center\",\n                onPress: ()=>router.push(\"/wallet/notice\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.XStack, {\n                        \"data-at\": \"setting-screen.tsx:157\",\n                        \"data-in\": \"SettingScreen\",\n                        \"data-is\": \"XStack\",\n                        items: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Image, {\n                                \"data-at\": \"setting-screen.tsx:158\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_set7_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src,\n                                width: 12,\n                                height: 12\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_10__.YStack, {\n                                \"data-at\": \"setting-screen.tsx:159\",\n                                \"data-in\": \"SettingScreen\",\n                                \"data-is\": \"YStack\",\n                                pl: 16,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:160\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        children: \"通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                                        \"data-at\": \"setting-screen.tsx:161\",\n                                        \"data-in\": \"SettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        color: \"$accent11\",\n                                        children: \"偏好设置、通知类型\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_12__.ChevronRight, {\n                        size: 20,\n                        color: \"$white6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/setting-screen.tsx\",\n        lineNumber: 15,\n        columnNumber: 10\n    }, this);\n}\n_s(SettingScreen, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter\n    ];\n});\n_c = SettingScreen;\nvar _c;\n$RefreshReg$(_c, \"SettingScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/setting-screen.tsx\n"));

/***/ })

});