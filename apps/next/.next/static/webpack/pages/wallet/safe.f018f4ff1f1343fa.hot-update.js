"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/wallet/safe",{

/***/ "../../packages/app/features/wallet/safe-screen.tsx":
/*!**********************************************************!*\
  !*** ../../packages/app/features/wallet/safe-screen.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SafeScreen: function() { return /* binding */ SafeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/arrowright.png */ \"../../packages/assets/images/wallet/arrowright.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Line = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Line;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = Underline;\nconst Underlineblock = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2\n});\n_c3 = Underlineblock;\nfunction SafeScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [openAppLock, setOpenAppLock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [transactionLock, setTransactionLock] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [netList, setNetList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Ethereum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 2,\n            name: \"Arbitrum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 3,\n            name: \"Avalanche C-Chain\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 4,\n            name: \"Base\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 5,\n            name: \"BNB (Binance Smart) Chain\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 6,\n            name: \"Fantom Opera\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 7,\n            name: \"Gnosis\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 8,\n            name: \"OP Mainnet\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 9,\n            name: \"Ethereum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 10,\n            name: \"Ethereum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"safe-screen.tsx:113\",\n        \"data-in\": \"SafeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"safe-screen.tsx:114\",\n                \"data-in\": \"SafeScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                pr: 16,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                    title: t(\"navigation.security\") || \"安全\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"safe-screen.tsx:118\",\n                \"data-in\": \"SafeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"safe-screen.tsx:119\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"备份\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"safe-screen.tsx:122\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"safe-screen.tsx:123\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: \"钱包1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"safe-screen.tsx:126\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"safe-screen.tsx:127-137\",\n                                        \"data-in\": \"SafeScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#C7545E\",\n                                        fontSize: 12,\n                                        mr: 10,\n                                        bg: \"rgba(199,84,94,0.16)\",\n                                        width: 45,\n                                        height: 20,\n                                        rounded: 4,\n                                        textAlign: \"center\",\n                                        lineHeight: 20,\n                                        children: \"未备份\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                        \"data-at\": \"safe-screen.tsx:140\",\n                                        \"data-in\": \"SafeScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                        width: 6,\n                                        height: 9\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"safe-screen.tsx:146\",\n                \"data-in\": \"SafeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"safe-screen.tsx:147\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"安全锁定\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"safe-screen.tsx:150\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"safe-screen.tsx:151\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: \"锁定方式\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"safe-screen.tsx:154\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"safe-screen.tsx:155\",\n                                    \"data-in\": \"SafeScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: 500,\n                                    color: \"$accent11\",\n                                    children: \"密码\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"safe-screen.tsx:162\",\n                \"data-in\": \"SafeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"safe-screen.tsx:163\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"以下情况需要解锁\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"safe-screen.tsx:166\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 14,\n                        color: \"$accent11\",\n                        mt: 10,\n                        children: \"必须至少选择一个选项。\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"safe-screen.tsx:169\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"safe-screen.tsx:170\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: \"打开应用\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"safe-screen.tsx:173-182\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$4\",\n                                backgroundColor: openAppLock ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: openAppLock ? \"#3873F5\" : \"#3A3D44\",\n                                checked: openAppLock,\n                                onCheckedChange: setOpenAppLock,\n                                style: {\n                                    backgroundColor: openAppLock ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"safe-screen.tsx:186\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"safe-screen.tsx:187\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"safe-screen.tsx:188\",\n                                        \"data-in\": \"SafeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"进行交易\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"safe-screen.tsx:191\",\n                                        \"data-in\": \"SafeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"仅影响标准钱包\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"safe-screen.tsx:195-204\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$4\",\n                                backgroundColor: transactionLock ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: transactionLock ? \"#3873F5\" : \"#3A3D44\",\n                                checked: transactionLock,\n                                onCheckedChange: setTransactionLock,\n                                style: {\n                                    backgroundColor: transactionLock ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n        lineNumber: 86,\n        columnNumber: 10\n    }, this);\n}\n_s(SafeScreen, \"q1jO75GIFRvHUDOfHmFToMRmJAo=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c4 = SafeScreen;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Line\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"Underline\");\n$RefreshReg$(_c3, \"Underlineblock\");\n$RefreshReg$(_c4, \"SafeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/safe-screen.tsx\n"));

/***/ })

});