"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/home",{

/***/ "../../packages/app/features/user/screen.tsx":
/*!***************************************************!*\
  !*** ../../packages/app/features/user/screen.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserScreen: function() { return /* binding */ UserScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/search.png */ \"../../packages/assets/images/search.png\");\n/* harmony import */ var _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/copy.png */ \"../../packages/assets/images/copy.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/wallet/bitcoin.png */ \"../../packages/assets/images/wallet/bitcoin.png\");\n/* harmony import */ var _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/wallet/bnb_smart.png */ \"../../packages/assets/images/wallet/bnb_smart.png\");\n/* harmony import */ var _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/wallet/solana.png */ \"../../packages/assets/images/wallet/solana.png\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\n/* harmony import */ var app_utils_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! app/utils/constants */ \"../../packages/app/utils/constants.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nfunction UserScreen() {\n    _s();\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWalletId, setSelectedWalletId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentAccount, setCurrentAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // NFT相关状态\n    const [selectedNetwork, setSelectedNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // 网络选项\n    const networkOptions = [\n        {\n            value: \"all\",\n            label: \"All networks\"\n        },\n        {\n            value: \"ethereum\",\n            label: \"Ethereum\"\n        },\n        {\n            value: \"polygon\",\n            label: \"Polygon\"\n        },\n        {\n            value: \"bsc\",\n            label: \"BNB Smart Chain\"\n        },\n        {\n            value: \"solana\",\n            label: \"Solana\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        walletStore.init();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (walletStore.currentAccount && walletStore.currentAccount.accountId) {\n            // 从 store 中获取当前账户，并设置显示名称\n            const account = walletStore.currentAccount;\n            // 如果账户有自定义名称，使用自定义名称；否则使用默认格式\n            let accountName = account.name;\n            if (!accountName) {\n                // 查找账户在钱包列表中的索引来生成默认名称\n                let accountIndex = 1;\n                for (const wallet of walletStore.walletList){\n                    const foundIndex = wallet.accounts.findIndex((acc)=>acc.accountId === account.accountId);\n                    if (foundIndex !== -1) {\n                        accountIndex = foundIndex + 1;\n                        break;\n                    }\n                }\n                accountName = (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(accountIndex));\n            }\n            setCurrentAccount({\n                ...account,\n                accountName\n            });\n            setSelectedWalletId(account.accountId);\n        }\n    }, [\n        walletStore.currentAccount,\n        walletStore.walletList,\n        t,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18nStore.getState().translations\n    ]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function(action) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        if (action === \"search\") {\n            router.push(\"/wallet/search\");\n        }\n        if (action === \"copy\") {\n            try {\n                await navigator.clipboard.writeText(currentAccount.eth.address);\n                toast.show(t(\"success.addressCopied\") || \"地址已复制到剪贴板\", {\n                    duration: 2000\n                });\n            } catch (err) {\n                toast.show(t(\"home.copyFailed\") || \"复制失败，请手动复制\", {\n                    duration: 2000\n                });\n            }\n        }\n        if (action === \"setting\") {\n            router.push(\"/wallet/setting\");\n        }\n        if (action === \"exchange\") {\n            router.push(\"/wallet/convert\");\n        }\n        if (action === \"buy\") {\n            router.push(\"/wallet/buy\");\n        }\n        if (action === \"buyRise\") {\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_16__[\"default\"].setItem(\"buyRiseAccount\", JSON.stringify(data));\n            router.push(\"/wallet/buyRise\");\n        }\n        if (action === \"tixian\") {\n            window.open(app_utils_constants__WEBPACK_IMPORTED_MODULE_17__.TIXIAN_URL, \"_blank\");\n        }\n        if (action === \"learnDefi\") {\n            window.open(\"https://www.coinbase.com/zh-sg/learn/wallet/how-to-get-started-in-defi\", \"_blank\");\n        }\n    }, [\n        currentAccount\n    ]);\n    // 获取当前地址有余额的链\n    const balanceFilterList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const keys = Object.keys(walletStore.currentAccount);\n        let _accountList = [];\n        keys.forEach((key)=>{\n            const _item = walletStore.currentAccount[key];\n            if (Number(_item === null || _item === void 0 ? void 0 : _item.balance) > 0) {\n                const _account = walletStore.currentAccount[key];\n                if (_account.accountType === \"btc\") {\n                    _account.name = \"Bitcoin\";\n                    _account.logo = _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                }\n                if (_account.accountType === \"eth\") {\n                    _account.name = \"Ethereum\";\n                    _account.logo = _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                }\n                if (_account.accountType === \"bsc\") {\n                    _account.name = \"BNB Smart\";\n                    _account.logo = _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                }\n                if (_account.accountType === \"solana\") {\n                    _account.name = \"Solana\";\n                    _account.logo = _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                }\n                _accountList.push(_account);\n            }\n        });\n        return _accountList;\n    }, [\n        walletStore.currentAccount\n    ]);\n    console.log(balanceFilterList);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n        \"data-at\": \"screen.tsx:173\",\n        \"data-in\": \"UserScreen\",\n        \"data-is\": \"YStack\",\n        height: \"100vh\",\n        bg: \"#0A0B0D\",\n        width: \"100%\",\n        maxW: 640,\n        margin: \"auto\",\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                \"data-at\": \"screen.tsx:174\",\n                \"data-in\": \"UserScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                gap: \"$3\",\n                p: \"$4\",\n                overflow: \"scroll\",\n                pb: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:175\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:176-182\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                space: \"$2\",\n                                onPress: ()=>{\n                                    setIsOpen(true);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        circular: true,\n                                        size: 24,\n                                        mr: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Image, {\n                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(currentAccount.accountId),\n                                                accessibilityLabel: currentAccount.accountId\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Fallback, {\n                                                backgroundColor: \"$blue10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                        \"data-at\": \"screen.tsx:191\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 14,\n                                        children: currentAccount.accountName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronDown, {\n                                        color: \"#8B8F9A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:196\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                gap: \"$3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:197\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"search\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:198\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:200\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"copy\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:201\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20,\n                                                marginHorizontal: 8\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:203\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"setting\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:204\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                            width: 18,\n                                            style: {\n                                                width: 18,\n                                                height: 18\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:208\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$2\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.H2, {\n                            \"data-at\": \"screen.tsx:209\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"H2\",\n                            textAlign: \"left\",\n                            color: \"#fff\",\n                            children: [\n                                \"$ \",\n                                walletStore.getCurrentAccountBalance().toFixed(4)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:234\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        gap: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:235-246\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#333\",\n                                    background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                                },\n                                onPress: ()=>handleAction(\"buy\"),\n                                children: \"买入\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:249-257\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#fff\",\n                                    background: \"#282B32\"\n                                },\n                                onPress: ()=>handleAction(\"exchange\"),\n                                children: \"兑换\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:260-268\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#8B8F9A\",\n                                    background: \"#15161A\"\n                                },\n                                onPress: ()=>handleAction(\"tixian\"),\n                                children: \"提现\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:272\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        height: 1,\n                        bg: \"#212224\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:273\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        height: 26,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(0),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:279\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(1),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:286\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 66\n                                        }, this),\n                                        currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(2),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:292\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: balanceFilterList.length > 0 ? balanceFilterList.map((item)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:303\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                mt: 30,\n                                items: \"center\",\n                                justifyContent: \"space-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                        \"data-at\": \"screen.tsx:304\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"XStack\",\n                                        items: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                                \"data-at\": \"screen.tsx:305\",\n                                                \"data-in\": \"UserScreen\",\n                                                \"data-is\": \"Image\",\n                                                source: item.logo.src,\n                                                width: 32,\n                                                height: 32,\n                                                mr: 6\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                \"data-at\": \"screen.tsx:306\",\n                                                \"data-in\": \"UserScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                        \"data-at\": \"screen.tsx:310\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"XStack\",\n                                        justifyContent: \"flex-end\",\n                                        onPress: ()=>handleAction(\"buyRise\", item),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"screen.tsx:312\",\n                                                        \"data-in\": \"UserScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        fontWeight: \"bold\",\n                                                        children: item.balance ? parseFloat(item.balance).toFixed(4) : \"0.0000\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"screen.tsx:315\",\n                                                        \"data-in\": \"UserScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        color: \"#8B8F9A\",\n                                                        children: \"可用\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronRight, {\n                                                size: 20,\n                                                color: \"$white6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 18\n                            }, this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                            \"data-at\": \"screen.tsx:325-333\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"YStack\",\n                            cursor: \"pointer\",\n                            margin: \"auto\",\n                            width: 300,\n                            flex: 1,\n                            alignContent: \"center\",\n                            alignItems: \"center\",\n                            mt: \"$15\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                    \"data-at\": \"screen.tsx:334\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                    style: {\n                                        width: 174,\n                                        height: 91\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:335\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 16,\n                                    color: \"#fff\",\n                                    fontWeight: \"bold\",\n                                    mt: \"$4\",\n                                    text: \"center\",\n                                    children: \"添加加密货币以开始使用\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:338\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    color: \"#8B8F9A\",\n                                    mt: \"$2\",\n                                    text: \"center\",\n                                    children: \"您可以使用您的 Coinbase 账户或其他钱包添加资产。\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 14\n                        }, this)\n                    }, void 0, false),\n                    currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:348\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        mt: 20,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                            value: selectedNetwork,\n                            onValueChange: setSelectedNetwork,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Trigger, {\n                                    width: \"100%\",\n                                    backgroundColor: \"#282B32\",\n                                    borderColor: \"#3A3D44\",\n                                    borderRadius: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Value, {\n                                        placeholder: \"选择网络\",\n                                        color: \"white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Content, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ScrollUpButton, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Viewport, {\n                                            children: networkOptions.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Item, {\n                                                    index: index,\n                                                    value: option.value,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ItemText, {\n                                                        color: \"white\",\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 58\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ScrollDownButton, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 30\n                    }, this),\n                    currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:370-378\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        cursor: \"pointer\",\n                        margin: \"auto\",\n                        width: 300,\n                        flex: 1,\n                        alignContent: \"center\",\n                        alignItems: \"center\",\n                        mt: \"$15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                \"data-at\": \"screen.tsx:379\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                style: {\n                                    width: 174,\n                                    height: 91\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:380\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 16,\n                                color: \"#fff\",\n                                fontWeight: \"bold\",\n                                mt: \"$4\",\n                                text: \"center\",\n                                children: \"开始通过 DeFi 赚取收益\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:383\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                color: \"#8B8F9A\",\n                                mt: \"$2\",\n                                text: \"center\",\n                                children: \"了解如何使用去中心化应用赚取加密货币。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:386-392\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                mt: 100,\n                                width: \"100%\",\n                                backgroundColor: \"#282B32\",\n                                borderRadius: 30,\n                                onPress: ()=>handleAction(\"learnDefi\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:393\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 16,\n                                    fontWeight: \"bold\",\n                                    children: \"了解 DeFi\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 30\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_19__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.WalletSheet, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                wallets: walletStore.walletList,\n                selectedId: selectedWalletId,\n                onSelect: (wallet, index)=>{\n                    if (wallet === \"addWallet\") {\n                        router.push(\"/wallet/manager\");\n                    } else {\n                        // 使用 store 的方法设置当前账户\n                        walletStore.setCurrentAccount(wallet);\n                        setSelectedWalletId(wallet.accountId);\n                        // 设置显示名称\n                        const accountName = wallet.name || (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(Number(index) + 1));\n                        setCurrentAccount({\n                            ...wallet,\n                            accountName\n                        });\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n        lineNumber: 160,\n        columnNumber: 10\n    }, this);\n}\n_s(UserScreen, \"ot1nT94MhNFp7Gws0egrubOF89o=\", false, function() {\n    return [\n        _my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore\n    ];\n});\n_c2 = UserScreen;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"UserScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/user/screen.tsx\n"));

/***/ })

});