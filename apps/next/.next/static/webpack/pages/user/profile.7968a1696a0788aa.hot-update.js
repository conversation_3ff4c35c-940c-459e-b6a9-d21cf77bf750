"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/profile",{

/***/ "../../packages/app/features/user/profile-screen.tsx":
/*!***********************************************************!*\
  !*** ../../packages/app/features/user/profile-screen.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfileScreen: function() { return /* binding */ ProfileScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_user_user1_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/user/user1.png */ \"../../packages/assets/images/user/user1.png\");\n/* harmony import */ var _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/arrowright.png */ \"../../packages/assets/images/wallet/arrowright.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// import { Edit } from '@tamagui/lucide-icons'\n\n\nconst Line = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Line;\nfunction ProfileScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const params = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_7__.useTranslation)();\n    // 从路由参数获取钱包信息\n    const walletId = params === null || params === void 0 ? void 0 : params.get(\"walletId\");\n    const accountId = params === null || params === void 0 ? void 0 : params.get(\"accountId\");\n    const accountName = params === null || params === void 0 ? void 0 : params.get(\"accountName\");\n    const [checked, setChecked4] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    console.log(\"Profile params:\", {\n        walletId,\n        accountId,\n        accountName\n    });\n    // 获取当前钱包账户信息\n    const getCurrentWallet = ()=>{\n        try {\n            if (!walletId || !accountId) {\n                console.log(\"No wallet params, using current account\");\n                return walletStore.currentAccount;\n            }\n            const wallet = walletStore.walletList.find((w)=>w.walletId === walletId);\n            if (wallet) {\n                const account = wallet.accounts.find((a)=>a.accountId === accountId);\n                console.log(\"Found account:\", account);\n                return account || walletStore.currentAccount;\n            }\n            console.log(\"Wallet not found, using current account\");\n            return walletStore.currentAccount;\n        } catch (error) {\n            console.error(\"Error getting current wallet:\", error);\n            return walletStore.currentAccount;\n        }\n    };\n    const currentWallet = getCurrentWallet();\n    const displayName = accountName || (currentWallet === null || currentWallet === void 0 ? void 0 : currentWallet.name) || \"地址1\";\n    console.log(\"Current wallet:\", currentWallet);\n    console.log(\"Display name:\", displayName);\n    // 如果没有钱包数据，显示加载状态\n    if (!currentWallet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n            \"data-at\": \"profile-screen.tsx:68\",\n            \"data-in\": \"ProfileScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            height: 800,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                \"data-at\": \"profile-screen.tsx:69\",\n                \"data-in\": \"ProfileScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"加载中...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n            lineNumber: 63,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n        \"data-at\": \"profile-screen.tsx:75\",\n        \"data-in\": \"ProfileScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                \"data-at\": \"profile-screen.tsx:76\",\n                \"data-in\": \"ProfileScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                pr: 16,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.NavBar, {\n                    title: \"\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                \"data-at\": \"profile-screen.tsx:79\",\n                \"data-in\": \"ProfileScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"profile-screen.tsx:80\",\n                        \"data-in\": \"ProfileScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        gap: \"$2\",\n                        position: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Avatar, {\n                                circular: true,\n                                size: \"$4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Avatar.Image, {\n                                        src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat((currentWallet === null || currentWallet === void 0 ? void 0 : currentWallet.accountId) || \"default\"),\n                                        accessibilityLabel: (currentWallet === null || currentWallet === void 0 ? void 0 : currentWallet.accountId) || \"wallet\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Avatar.Fallback, {\n                                        backgroundColor: \"$blue10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                                onPress: ()=>{\n                                    const params = new URLSearchParams({\n                                        walletId: walletId || (currentWallet === null || currentWallet === void 0 ? void 0 : currentWallet.walletId) || \"\",\n                                        accountId: accountId || (currentWallet === null || currentWallet === void 0 ? void 0 : currentWallet.accountId) || \"\",\n                                        currentName: displayName\n                                    });\n                                    console.log(\"Edit wallet name params:\", params.toString());\n                                    router.push(\"/user/edit-wallet-name?\".concat(params.toString()));\n                                },\n                                style: {\n                                    flexDirection: \"row\",\n                                    alignItems: \"center\",\n                                    flex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"profile-screen.tsx:100\",\n                                        \"data-in\": \"ProfileScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        ml: 10,\n                                        fontWeight: \"600\",\n                                        color: \"white\",\n                                        children: displayName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"profile-screen.tsx:103\",\n                                        \"data-in\": \"ProfileScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 12,\n                                        color: \"$color11\",\n                                        style: {\n                                            marginLeft: 8\n                                        },\n                                        children: \"✏️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"profile-screen.tsx:105\",\n                                \"data-in\": \"ProfileScreen\",\n                                \"data-is\": \"View\",\n                                position: \"absolute\",\n                                left: 30,\n                                top: 26,\n                                zIndex: 99,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                    \"data-at\": \"profile-screen.tsx:106\",\n                                    \"data-in\": \"ProfileScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_user_user1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                    width: 16,\n                                    height: 16\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                        \"data-at\": \"profile-screen.tsx:109\",\n                        \"data-in\": \"ProfileScreen\",\n                        \"data-is\": \"Button\",\n                        my: 20,\n                        rounded: 30,\n                        onPress: ()=>{\n                            router.push(\"/user/settingAccount\");\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            \"data-at\": \"profile-screen.tsx:110\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            children: \"设置个人资料\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                \"data-at\": \"profile-screen.tsx:114\",\n                \"data-in\": \"ProfileScreen\",\n                \"data-is\": \"XStack\",\n                px: 16,\n                mt: 30,\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"profile-screen.tsx:115\",\n                        \"data-in\": \"ProfileScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"查看个人资料\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                        onPress: ()=>{\n                            console.log(11);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                            \"data-at\": \"profile-screen.tsx:117\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                            width: 5,\n                            height: 9\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                \"data-at\": \"profile-screen.tsx:120\",\n                \"data-in\": \"ProfileScreen\",\n                \"data-is\": \"XStack\",\n                px: 16,\n                mt: 30,\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"profile-screen.tsx:121\",\n                        \"data-in\": \"ProfileScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"恢复短语\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                        onPress: ()=>{\n                            console.log(11);\n                        },\n                        style: {\n                            alignItems: \"center\",\n                            flexDirection: \"row\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"profile-screen.tsx:123\",\n                                \"data-in\": \"ProfileScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"#C7545E\",\n                                fontSize: 12,\n                                mr: 10,\n                                bg: \"rgba(199,84,94,0.16)\",\n                                width: 45,\n                                height: 20,\n                                rounded: 4,\n                                textAlign: \"center\",\n                                lineHeight: 20,\n                                children: \"未备份\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                \"data-at\": \"profile-screen.tsx:124\",\n                                \"data-in\": \"ProfileScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 5,\n                                height: 9\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                onPress: ()=>{\n                    router.push(\"/wallet/connectDapp\");\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                    \"data-at\": \"profile-screen.tsx:128\",\n                    \"data-in\": \"ProfileScreen\",\n                    \"data-is\": \"XStack\",\n                    px: 16,\n                    mt: 30,\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            \"data-at\": \"profile-screen.tsx:129\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            children: \"连接的 dapp\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                            \"data-at\": \"profile-screen.tsx:130\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"View\",\n                            style: {\n                                alignItems: \"center\",\n                                flexDirection: \"row\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"profile-screen.tsx:131\",\n                                    \"data-in\": \"ProfileScreen\",\n                                    \"data-is\": \"Text\",\n                                    color: \"$accent11\",\n                                    fontSize: 16,\n                                    mr: 10,\n                                    children: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                    \"data-at\": \"profile-screen.tsx:132\",\n                                    \"data-in\": \"ProfileScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                    width: 5,\n                                    height: 9\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                onPress: ()=>{\n                    router.push(\"/wallet/exportPublicAddress\");\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                    \"data-at\": \"profile-screen.tsx:137\",\n                    \"data-in\": \"ProfileScreen\",\n                    \"data-is\": \"XStack\",\n                    px: 16,\n                    mt: 30,\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            \"data-at\": \"profile-screen.tsx:138\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            children: \"导出公共地址\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                            \"data-at\": \"profile-screen.tsx:139\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"View\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                \"data-at\": \"profile-screen.tsx:140\",\n                                \"data-in\": \"ProfileScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 5,\n                                height: 9\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                \"data-at\": \"profile-screen.tsx:144\",\n                \"data-in\": \"ProfileScreen\",\n                \"data-is\": \"XStack\",\n                px: 16,\n                mt: 30,\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"profile-screen.tsx:145\",\n                        \"data-in\": \"ProfileScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"显示私钥\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                        onPress: ()=>{\n                            console.log(11);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                            \"data-at\": \"profile-screen.tsx:147\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                            width: 5,\n                            height: 9\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                onPress: ()=>{\n                    router.push(\"/wallet/hideAssets\");\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                    \"data-at\": \"profile-screen.tsx:151\",\n                    \"data-in\": \"ProfileScreen\",\n                    \"data-is\": \"XStack\",\n                    px: 16,\n                    mt: 30,\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            \"data-at\": \"profile-screen.tsx:152\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            children: \"隐藏资产\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                            \"data-at\": \"profile-screen.tsx:153\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"View\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                \"data-at\": \"profile-screen.tsx:154\",\n                                \"data-in\": \"ProfileScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 5,\n                                height: 9\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                onPress: ()=>{\n                    router.push(\"/wallet/tokenAuth\");\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                    \"data-at\": \"profile-screen.tsx:159\",\n                    \"data-in\": \"ProfileScreen\",\n                    \"data-is\": \"XStack\",\n                    px: 16,\n                    mt: 30,\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                            \"data-at\": \"profile-screen.tsx:160\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            children: \"代币授权\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                            \"data-at\": \"profile-screen.tsx:161\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"View\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                \"data-at\": \"profile-screen.tsx:162\",\n                                \"data-in\": \"ProfileScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 5,\n                                height: 9\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                \"data-at\": \"profile-screen.tsx:166\",\n                \"data-in\": \"ProfileScreen\",\n                \"data-is\": \"XStack\",\n                px: 16,\n                mt: 30,\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"profile-screen.tsx:167\",\n                        \"data-in\": \"ProfileScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"颜色\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_9__.Pressable, {\n                        onPress: ()=>{\n                            console.log(11);\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                            \"data-at\": \"profile-screen.tsx:169\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                            width: 5,\n                            height: 9\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                \"data-at\": \"profile-screen.tsx:172\",\n                \"data-in\": \"ProfileScreen\",\n                \"data-is\": \"XStack\",\n                px: 16,\n                mt: 30,\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"profile-screen.tsx:173\",\n                        \"data-in\": \"ProfileScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"隐藏地址\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                        \"data-at\": \"profile-screen.tsx:174\",\n                        \"data-in\": \"ProfileScreen\",\n                        \"data-is\": \"View\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                            \"data-at\": \"profile-screen.tsx:175-182\",\n                            \"data-in\": \"ProfileScreen\",\n                            \"data-is\": \"Switch\",\n                            size: \"$3\",\n                            backgroundColor: checked ? \"#3873F5\" : \"#3A3D44\",\n                            borderColor: checked ? \"#3873F5\" : \"#3A3D44\",\n                            checked: checked,\n                            onCheckedChange: setChecked4,\n                            style: {\n                                backgroundColor: checked ? \"#3873F5\" : \"#3A3D44\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                animation: \"bouncy\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/profile-screen.tsx\",\n        lineNumber: 67,\n        columnNumber: 10\n    }, this);\n}\n_s(ProfileScreen, \"EPLtlyCy8KvpxvneUbooohW7Nyc=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore,\n        app_i18n__WEBPACK_IMPORTED_MODULE_7__.useTranslation\n    ];\n});\n_c1 = ProfileScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"Line\");\n$RefreshReg$(_c1, \"ProfileScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/user/profile-screen.tsx\n"));

/***/ })

});