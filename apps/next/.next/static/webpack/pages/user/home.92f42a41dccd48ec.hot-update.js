"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/home",{

/***/ "../../packages/app/features/user/screen.tsx":
/*!***************************************************!*\
  !*** ../../packages/app/features/user/screen.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserScreen: function() { return /* binding */ UserScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/search.png */ \"../../packages/assets/images/search.png\");\n/* harmony import */ var _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/copy.png */ \"../../packages/assets/images/copy.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/wallet/bitcoin.png */ \"../../packages/assets/images/wallet/bitcoin.png\");\n/* harmony import */ var _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/wallet/bnb_smart.png */ \"../../packages/assets/images/wallet/bnb_smart.png\");\n/* harmony import */ var _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/wallet/solana.png */ \"../../packages/assets/images/wallet/solana.png\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\n/* harmony import */ var app_utils_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! app/utils/constants */ \"../../packages/app/utils/constants.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nfunction UserScreen() {\n    _s();\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWalletId, setSelectedWalletId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentAccount, setCurrentAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // NFT相关状态\n    const [selectedNetwork, setSelectedNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // 网络选项\n    const networkOptions = [\n        {\n            value: \"all\",\n            label: \"All networks\"\n        },\n        {\n            value: \"ethereum\",\n            label: \"Ethereum\"\n        },\n        {\n            value: \"polygon\",\n            label: \"Polygon\"\n        },\n        {\n            value: \"bsc\",\n            label: \"BNB Smart Chain\"\n        },\n        {\n            value: \"solana\",\n            label: \"Solana\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        walletStore.init();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (walletStore.currentAccount && walletStore.currentAccount.accountId) {\n            // 从 store 中获取当前账户，并设置显示名称\n            const account = walletStore.currentAccount;\n            // 如果账户有自定义名称，使用自定义名称；否则使用默认格式\n            let accountName = account.name;\n            if (!accountName) {\n                // 查找账户在钱包列表中的索引来生成默认名称\n                let accountIndex = 1;\n                for (const wallet of walletStore.walletList){\n                    const foundIndex = wallet.accounts.findIndex((acc)=>acc.accountId === account.accountId);\n                    if (foundIndex !== -1) {\n                        accountIndex = foundIndex + 1;\n                        break;\n                    }\n                }\n                accountName = (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(accountIndex));\n            }\n            setCurrentAccount({\n                ...account,\n                accountName\n            });\n            setSelectedWalletId(account.accountId);\n        }\n    }, [\n        walletStore.currentAccount,\n        walletStore.walletList,\n        t,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18nStore.getState().translations\n    ]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function(action) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        if (action === \"search\") {\n            router.push(\"/wallet/search\");\n        }\n        if (action === \"copy\") {\n            try {\n                await navigator.clipboard.writeText(currentAccount.eth.address);\n                toast.show(t(\"success.addressCopied\") || \"地址已复制到剪贴板\", {\n                    duration: 2000\n                });\n            } catch (err) {\n                toast.show(t(\"home.copyFailed\") || \"复制失败，请手动复制\", {\n                    duration: 2000\n                });\n            }\n        }\n        if (action === \"setting\") {\n            router.push(\"/wallet/setting\");\n        }\n        if (action === \"exchange\") {\n            router.push(\"/wallet/convert\");\n        }\n        if (action === \"buy\") {\n            router.push(\"/wallet/buy\");\n        }\n        if (action === \"buyRise\") {\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_16__[\"default\"].setItem(\"buyRiseAccount\", JSON.stringify(data));\n            router.push(\"/wallet/buyRise\");\n        }\n        if (action === \"tixian\") {\n            window.open(app_utils_constants__WEBPACK_IMPORTED_MODULE_17__.TIXIAN_URL, \"_blank\");\n        }\n        if (action === \"learnDefi\") {\n            window.open(\"https://www.coinbase.com/zh-sg/learn/wallet/how-to-get-started-in-defi\", \"_blank\");\n        }\n    }, [\n        currentAccount\n    ]);\n    // 获取当前地址有余额的链\n    const balanceFilterList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const keys = Object.keys(walletStore.currentAccount);\n        let _accountList = [];\n        keys.forEach((key)=>{\n            const _item = walletStore.currentAccount[key];\n            if (Number(_item === null || _item === void 0 ? void 0 : _item.balance) > 0) {\n                const _account = walletStore.currentAccount[key];\n                if (_account.accountType === \"btc\") {\n                    _account.name = \"Bitcoin\";\n                    _account.logo = _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                }\n                if (_account.accountType === \"eth\") {\n                    _account.name = \"Ethereum\";\n                    _account.logo = _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                }\n                if (_account.accountType === \"bsc\") {\n                    _account.name = \"BNB Smart\";\n                    _account.logo = _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                }\n                if (_account.accountType === \"solana\") {\n                    _account.name = \"Solana\";\n                    _account.logo = _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                }\n                _accountList.push(_account);\n            }\n        });\n        return _accountList;\n    }, [\n        walletStore.currentAccount\n    ]);\n    console.log(balanceFilterList);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n        \"data-at\": \"screen.tsx:173\",\n        \"data-in\": \"UserScreen\",\n        \"data-is\": \"YStack\",\n        height: \"100vh\",\n        bg: \"#0A0B0D\",\n        width: \"100%\",\n        maxW: 640,\n        margin: \"auto\",\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                \"data-at\": \"screen.tsx:174\",\n                \"data-in\": \"UserScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                gap: \"$3\",\n                p: \"$4\",\n                overflow: \"scroll\",\n                pb: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:175\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:176-182\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                space: \"$2\",\n                                onPress: ()=>{\n                                    setIsOpen(true);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        circular: true,\n                                        size: 24,\n                                        mr: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Image, {\n                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(currentAccount.accountId),\n                                                accessibilityLabel: currentAccount.accountId\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Fallback, {\n                                                backgroundColor: \"$blue10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                        \"data-at\": \"screen.tsx:191\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 14,\n                                        children: currentAccount.accountName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronDown, {\n                                        color: \"#8B8F9A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:196\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                gap: \"$3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:197\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"search\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:198\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:200\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"copy\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:201\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20,\n                                                marginHorizontal: 8\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:203\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"setting\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:204\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                            width: 18,\n                                            style: {\n                                                width: 18,\n                                                height: 18\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:208\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$2\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.H2, {\n                            \"data-at\": \"screen.tsx:209\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"H2\",\n                            textAlign: \"left\",\n                            color: \"#fff\",\n                            children: [\n                                \"$ \",\n                                walletStore.getCurrentAccountBalance().toFixed(4)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:234\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        gap: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:235-246\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#333\",\n                                    background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                                },\n                                onPress: ()=>handleAction(\"buy\"),\n                                children: \"买入\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:249-257\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#fff\",\n                                    background: \"#282B32\"\n                                },\n                                onPress: ()=>handleAction(\"exchange\"),\n                                children: \"兑换\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:260-268\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#8B8F9A\",\n                                    background: \"#15161A\"\n                                },\n                                onPress: ()=>handleAction(\"tixian\"),\n                                children: \"提现\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:272\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        height: 1,\n                        bg: \"#212224\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:273\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        height: 26,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(0),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:279\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(1),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:286\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 66\n                                        }, this),\n                                        currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(2),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:292\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: balanceFilterList.length > 0 ? balanceFilterList.map((item)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:303\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                mt: 30,\n                                items: \"center\",\n                                justifyContent: \"space-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                        \"data-at\": \"screen.tsx:304\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"XStack\",\n                                        items: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                                \"data-at\": \"screen.tsx:305\",\n                                                \"data-in\": \"UserScreen\",\n                                                \"data-is\": \"Image\",\n                                                source: item.logo.src,\n                                                width: 32,\n                                                height: 32,\n                                                mr: 6\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                \"data-at\": \"screen.tsx:306\",\n                                                \"data-in\": \"UserScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                        \"data-at\": \"screen.tsx:310\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"XStack\",\n                                        justifyContent: \"flex-end\",\n                                        onPress: ()=>handleAction(\"buyRise\", item),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"screen.tsx:312\",\n                                                        \"data-in\": \"UserScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        fontWeight: \"bold\",\n                                                        children: item.balance ? parseFloat(item.balance).toFixed(4) : \"0.0000\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"screen.tsx:315\",\n                                                        \"data-in\": \"UserScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        color: \"#8B8F9A\",\n                                                        children: \"可用\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronRight, {\n                                                size: 20,\n                                                color: \"$white6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 18\n                            }, this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                            \"data-at\": \"screen.tsx:325-333\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"YStack\",\n                            cursor: \"pointer\",\n                            margin: \"auto\",\n                            width: 300,\n                            flex: 1,\n                            alignContent: \"center\",\n                            alignItems: \"center\",\n                            mt: \"$15\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                    onPress: ()=>setCurrentPage(4),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"screen.tsx:335\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                        style: {\n                                            width: 174,\n                                            height: 91\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:337\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 16,\n                                    color: \"#fff\",\n                                    fontWeight: \"bold\",\n                                    mt: \"$4\",\n                                    text: \"center\",\n                                    children: \"添加加密货币以开始使用\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:340\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    color: \"#8B8F9A\",\n                                    mt: \"$2\",\n                                    text: \"center\",\n                                    children: \"您可以使用您的 Coinbase 账户或其他钱包添加资产。\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 14\n                        }, this)\n                    }, void 0, false),\n                    currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:350\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        mt: 20,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                            value: selectedNetwork,\n                            onValueChange: setSelectedNetwork,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Trigger, {\n                                    width: \"100%\",\n                                    backgroundColor: \"#282B32\",\n                                    borderColor: \"#3A3D44\",\n                                    borderRadius: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Value, {\n                                        placeholder: \"选择网络\",\n                                        color: \"white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Content, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ScrollUpButton, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Viewport, {\n                                            children: networkOptions.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Item, {\n                                                    index: index,\n                                                    value: option.value,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ItemText, {\n                                                        color: \"white\",\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 58\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ScrollDownButton, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 30\n                    }, this),\n                    currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:372-380\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        cursor: \"pointer\",\n                        margin: \"auto\",\n                        width: 300,\n                        flex: 1,\n                        alignContent: \"center\",\n                        alignItems: \"center\",\n                        mt: \"$15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentPage(4),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                    \"data-at\": \"screen.tsx:382\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                    style: {\n                                        width: 174,\n                                        height: 91\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:384\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 16,\n                                color: \"#fff\",\n                                fontWeight: \"bold\",\n                                mt: \"$4\",\n                                text: \"center\",\n                                children: \"开始通过 DeFi 赚取收益\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:387\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                color: \"#8B8F9A\",\n                                mt: \"$2\",\n                                text: \"center\",\n                                children: \"了解如何使用去中心化应用赚取加密货币。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:390-396\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                mt: \"$4\",\n                                width: \"100%\",\n                                backgroundColor: \"#282B32\",\n                                borderRadius: 30,\n                                onPress: ()=>handleAction(\"learnDefi\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:397\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 16,\n                                    fontWeight: \"bold\",\n                                    children: \"了解 DeFi\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 30\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_19__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.WalletSheet, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                wallets: walletStore.walletList,\n                selectedId: selectedWalletId,\n                onSelect: (wallet, index)=>{\n                    if (wallet === \"addWallet\") {\n                        router.push(\"/wallet/manager\");\n                    } else {\n                        // 使用 store 的方法设置当前账户\n                        walletStore.setCurrentAccount(wallet);\n                        setSelectedWalletId(wallet.accountId);\n                        // 设置显示名称\n                        const accountName = wallet.name || (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(Number(index) + 1));\n                        setCurrentAccount({\n                            ...wallet,\n                            accountName\n                        });\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n        lineNumber: 160,\n        columnNumber: 10\n    }, this);\n}\n_s(UserScreen, \"ot1nT94MhNFp7Gws0egrubOF89o=\", false, function() {\n    return [\n        _my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore\n    ];\n});\n_c2 = UserScreen;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"UserScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/user/screen.tsx\n"));

/***/ })

});