"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/home",{

/***/ "../../packages/app/features/user/screen.tsx":
/*!***************************************************!*\
  !*** ../../packages/app/features/user/screen.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserScreen: function() { return /* binding */ UserScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/search.png */ \"../../packages/assets/images/search.png\");\n/* harmony import */ var _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/copy.png */ \"../../packages/assets/images/copy.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/wallet/bitcoin.png */ \"../../packages/assets/images/wallet/bitcoin.png\");\n/* harmony import */ var _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/wallet/bnb_smart.png */ \"../../packages/assets/images/wallet/bnb_smart.png\");\n/* harmony import */ var _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/wallet/solana.png */ \"../../packages/assets/images/wallet/solana.png\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\n/* harmony import */ var app_utils_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! app/utils/constants */ \"../../packages/app/utils/constants.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nfunction UserScreen() {\n    _s();\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWalletId, setSelectedWalletId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentAccount, setCurrentAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // NFT相关状态\n    const [selectedNetwork, setSelectedNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // 网络选项\n    const networkOptions = [\n        {\n            value: \"all\",\n            label: \"All networks\"\n        },\n        {\n            value: \"ethereum\",\n            label: \"Ethereum\"\n        },\n        {\n            value: \"polygon\",\n            label: \"Polygon\"\n        },\n        {\n            value: \"bsc\",\n            label: \"BNB Smart Chain\"\n        },\n        {\n            value: \"solana\",\n            label: \"Solana\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        walletStore.init();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (walletStore.currentAccount && walletStore.currentAccount.accountId) {\n            // 从 store 中获取当前账户，并设置显示名称\n            const account = walletStore.currentAccount;\n            // 如果账户有自定义名称，使用自定义名称；否则使用默认格式\n            let accountName = account.name;\n            if (!accountName) {\n                // 查找账户在钱包列表中的索引来生成默认名称\n                let accountIndex = 1;\n                for (const wallet of walletStore.walletList){\n                    const foundIndex = wallet.accounts.findIndex((acc)=>acc.accountId === account.accountId);\n                    if (foundIndex !== -1) {\n                        accountIndex = foundIndex + 1;\n                        break;\n                    }\n                }\n                accountName = (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(accountIndex));\n            }\n            setCurrentAccount({\n                ...account,\n                accountName\n            });\n            setSelectedWalletId(account.accountId);\n        }\n    }, [\n        walletStore.currentAccount,\n        walletStore.walletList,\n        t,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18nStore.getState().translations\n    ]);\n    // 手动刷新余额\n    const handleRefreshBalance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (isRefreshing) return;\n        setIsRefreshing(true);\n        try {\n            await walletStore.fetchAllBalances();\n            toast.show(t(\"success.balanceRefreshed\") || \"余额已刷新\", {\n                duration: 2000\n            });\n        } catch (error) {\n            toast.show(t(\"error.refreshFailed\") || \"刷新失败，请稍后重试\", {\n                duration: 2000\n            });\n        } finally{\n            setIsRefreshing(false);\n        }\n    }, [\n        isRefreshing,\n        walletStore,\n        toast,\n        t\n    ]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function(action) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        if (action === \"search\") {\n            router.push(\"/wallet/search\");\n        }\n        if (action === \"copy\") {\n            try {\n                await navigator.clipboard.writeText(currentAccount.eth.address);\n                toast.show(t(\"success.addressCopied\") || \"地址已复制到剪贴板\", {\n                    duration: 2000\n                });\n            } catch (err) {\n                toast.show(t(\"home.copyFailed\") || \"复制失败，请手动复制\", {\n                    duration: 2000\n                });\n            }\n        }\n        if (action === \"setting\") {\n            router.push(\"/wallet/setting\");\n        }\n        if (action === \"exchange\") {\n            router.push(\"/wallet/convert\");\n        }\n        if (action === \"buy\") {\n            router.push(\"/wallet/buy\");\n        }\n        if (action === \"buyRise\") {\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_16__[\"default\"].setItem(\"buyRiseAccount\", JSON.stringify(data));\n            router.push(\"/wallet/buyRise\");\n        }\n        if (action === \"tixian\") {\n            window.open(app_utils_constants__WEBPACK_IMPORTED_MODULE_17__.TIXIAN_URL, \"_blank\");\n        }\n        if (action === \"learnDefi\") {\n            window.open(\"https://www.coinbase.com/zh-sg/learn/wallet/how-to-get-started-in-defi\", \"_blank\");\n        }\n    }, [\n        currentAccount\n    ]);\n    // 获取当前地址有余额的链\n    const balanceFilterList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const keys = Object.keys(walletStore.currentAccount);\n        let _accountList = [];\n        keys.forEach((key)=>{\n            const _item = walletStore.currentAccount[key];\n            if (Number(_item === null || _item === void 0 ? void 0 : _item.balance) > 0) {\n                const _account = walletStore.currentAccount[key];\n                if (_account.accountType === \"btc\") {\n                    _account.name = \"Bitcoin\";\n                    _account.logo = _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                }\n                if (_account.accountType === \"eth\") {\n                    _account.name = \"Ethereum\";\n                    _account.logo = _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                }\n                if (_account.accountType === \"bsc\") {\n                    _account.name = \"BNB Smart\";\n                    _account.logo = _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                }\n                if (_account.accountType === \"solana\") {\n                    _account.name = \"Solana\";\n                    _account.logo = _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                }\n                _accountList.push(_account);\n            }\n        });\n        return _accountList;\n    }, [\n        walletStore.currentAccount\n    ]);\n    console.log(balanceFilterList);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n        \"data-at\": \"screen.tsx:187\",\n        \"data-in\": \"UserScreen\",\n        \"data-is\": \"YStack\",\n        height: \"100vh\",\n        bg: \"#0A0B0D\",\n        width: \"100%\",\n        maxW: 640,\n        margin: \"auto\",\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                \"data-at\": \"screen.tsx:188\",\n                \"data-in\": \"UserScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                gap: \"$3\",\n                p: \"$4\",\n                overflow: \"scroll\",\n                pb: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:189\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:190-196\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                space: \"$2\",\n                                onPress: ()=>{\n                                    setIsOpen(true);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        circular: true,\n                                        size: 24,\n                                        mr: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Image, {\n                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(currentAccount.accountId),\n                                                accessibilityLabel: currentAccount.accountId\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Fallback, {\n                                                backgroundColor: \"$blue10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                        \"data-at\": \"screen.tsx:205\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 14,\n                                        children: currentAccount.accountName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronDown, {\n                                        color: \"#8B8F9A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:210\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                gap: \"$3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:211\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"search\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:212\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:214\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"copy\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:215\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20,\n                                                marginHorizontal: 8\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:217\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"setting\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:218\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                            width: 18,\n                                            style: {\n                                                width: 18,\n                                                height: 18\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:222\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$2\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.H2, {\n                            \"data-at\": \"screen.tsx:223\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"H2\",\n                            textAlign: \"left\",\n                            color: \"#fff\",\n                            children: [\n                                \"$ \",\n                                walletStore.getCurrentAccountBalance().toFixed(4)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:248\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        gap: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:249-260\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#333\",\n                                    background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                                },\n                                onPress: ()=>handleAction(\"buy\"),\n                                children: \"买入\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:263-271\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#fff\",\n                                    background: \"#282B32\"\n                                },\n                                onPress: ()=>handleAction(\"exchange\"),\n                                children: \"兑换\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:274-282\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#8B8F9A\",\n                                    background: \"#15161A\"\n                                },\n                                onPress: ()=>handleAction(\"tixian\"),\n                                children: \"提现\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:286\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        height: 1,\n                        bg: \"#212224\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:287\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        height: 26,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(0),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:293\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(1),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:300\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 66\n                                        }, this),\n                                        currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(2),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:306\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, this),\n                    currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: balanceFilterList.length > 0 ? balanceFilterList.map((item)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:317\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                mt: 30,\n                                items: \"center\",\n                                justifyContent: \"space-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                        \"data-at\": \"screen.tsx:318\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"XStack\",\n                                        items: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                                \"data-at\": \"screen.tsx:319\",\n                                                \"data-in\": \"UserScreen\",\n                                                \"data-is\": \"Image\",\n                                                source: item.logo.src,\n                                                width: 32,\n                                                height: 32,\n                                                mr: 6\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                \"data-at\": \"screen.tsx:320\",\n                                                \"data-in\": \"UserScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                        \"data-at\": \"screen.tsx:324\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"XStack\",\n                                        justifyContent: \"flex-end\",\n                                        onPress: ()=>handleAction(\"buyRise\", item),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"screen.tsx:326\",\n                                                        \"data-in\": \"UserScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        fontWeight: \"bold\",\n                                                        children: item.balance ? parseFloat(item.balance).toFixed(4) : \"0.0000\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"screen.tsx:329\",\n                                                        \"data-in\": \"UserScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        color: \"#8B8F9A\",\n                                                        children: \"可用\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronRight, {\n                                                size: 20,\n                                                color: \"$white6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 18\n                            }, this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                            \"data-at\": \"screen.tsx:339-347\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"YStack\",\n                            cursor: \"pointer\",\n                            margin: \"auto\",\n                            width: 300,\n                            flex: 1,\n                            alignContent: \"center\",\n                            alignItems: \"center\",\n                            mt: \"$15\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                    onPress: ()=>setCurrentPage(4),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"screen.tsx:349\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                        style: {\n                                            width: 174,\n                                            height: 91\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:351\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 16,\n                                    color: \"#fff\",\n                                    fontWeight: \"bold\",\n                                    mt: \"$4\",\n                                    text: \"center\",\n                                    children: \"添加加密货币以开始使用\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:354\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    color: \"#8B8F9A\",\n                                    mt: \"$2\",\n                                    text: \"center\",\n                                    children: \"您可以使用您的 Coinbase 账户或其他钱包添加资产。\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 14\n                        }, this)\n                    }, void 0, false),\n                    currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:364\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        mt: 20,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                            value: selectedNetwork,\n                            onValueChange: setSelectedNetwork,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Trigger, {\n                                    width: \"100%\",\n                                    backgroundColor: \"#282B32\",\n                                    borderColor: \"#3A3D44\",\n                                    borderRadius: 12,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Value, {\n                                        placeholder: \"选择网络\",\n                                        color: \"white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Content, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ScrollUpButton, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Viewport, {\n                                            children: networkOptions.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Item, {\n                                                    index: index,\n                                                    value: option.value,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ItemText, {\n                                                        color: \"white\",\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 58\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ScrollDownButton, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 30\n                    }, this),\n                    currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:386-394\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        cursor: \"pointer\",\n                        margin: \"auto\",\n                        width: 300,\n                        flex: 1,\n                        alignContent: \"center\",\n                        alignItems: \"center\",\n                        mt: \"$15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentPage(4),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                    \"data-at\": \"screen.tsx:396\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                    style: {\n                                        width: 174,\n                                        height: 91\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:398\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 16,\n                                color: \"#fff\",\n                                fontWeight: \"bold\",\n                                mt: \"$4\",\n                                text: \"center\",\n                                children: \"开始通过 DeFi 赚取收益\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:401\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                color: \"#8B8F9A\",\n                                mt: \"$2\",\n                                text: \"center\",\n                                children: \"了解如何使用去中心化应用赚取加密货币。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:404-410\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                mt: \"$4\",\n                                width: \"100%\",\n                                backgroundColor: \"#282B32\",\n                                borderRadius: 30,\n                                onPress: ()=>handleAction(\"learnDefi\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:411\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 16,\n                                    fontWeight: \"bold\",\n                                    children: \"了解 DeFi\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 30\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_19__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.WalletSheet, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                wallets: walletStore.walletList,\n                selectedId: selectedWalletId,\n                onSelect: (wallet, index)=>{\n                    if (wallet === \"addWallet\") {\n                        router.push(\"/wallet/manager\");\n                    } else {\n                        // 使用 store 的方法设置当前账户\n                        walletStore.setCurrentAccount(wallet);\n                        setSelectedWalletId(wallet.accountId);\n                        // 设置显示名称\n                        const accountName = wallet.name || (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(Number(index) + 1));\n                        setCurrentAccount({\n                            ...wallet,\n                            accountName\n                        });\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n        lineNumber: 179,\n        columnNumber: 10\n    }, this);\n}\n_s(UserScreen, \"FQJG9CmfHNjI6K3Ad42ud5MlBQw=\", false, function() {\n    return [\n        _my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore\n    ];\n});\n_c2 = UserScreen;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"UserScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/user/screen.tsx\n"));

/***/ })

});