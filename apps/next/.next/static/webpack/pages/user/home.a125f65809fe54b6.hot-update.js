"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/user/home",{

/***/ "../../packages/app/features/user/screen.tsx":
/*!***************************************************!*\
  !*** ../../packages/app/features/user/screen.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserScreen: function() { return /* binding */ UserScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/search.png */ \"../../packages/assets/images/search.png\");\n/* harmony import */ var _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/copy.png */ \"../../packages/assets/images/copy.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/wallet/bitcoin.png */ \"../../packages/assets/images/wallet/bitcoin.png\");\n/* harmony import */ var _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/wallet/bnb_smart.png */ \"../../packages/assets/images/wallet/bnb_smart.png\");\n/* harmony import */ var _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/wallet/solana.png */ \"../../packages/assets/images/wallet/solana.png\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\n/* harmony import */ var app_utils_constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! app/utils/constants */ \"../../packages/app/utils/constants.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nfunction UserScreen() {\n    _s();\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWalletId, setSelectedWalletId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentAccount, setCurrentAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // NFT相关状态\n    const [selectedNetwork, setSelectedNetwork] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // 网络选项\n    const networkOptions = [\n        {\n            value: \"all\",\n            label: \"All networks\"\n        },\n        {\n            value: \"ethereum\",\n            label: \"Ethereum\"\n        },\n        {\n            value: \"polygon\",\n            label: \"Polygon\"\n        },\n        {\n            value: \"bsc\",\n            label: \"BNB Smart Chain\"\n        },\n        {\n            value: \"solana\",\n            label: \"Solana\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        walletStore.init();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (walletStore.currentAccount && walletStore.currentAccount.accountId) {\n            // 从 store 中获取当前账户，并设置显示名称\n            const account = walletStore.currentAccount;\n            // 如果账户有自定义名称，使用自定义名称；否则使用默认格式\n            let accountName = account.name;\n            if (!accountName) {\n                // 查找账户在钱包列表中的索引来生成默认名称\n                let accountIndex = 1;\n                for (const wallet of walletStore.walletList){\n                    const foundIndex = wallet.accounts.findIndex((acc)=>acc.accountId === account.accountId);\n                    if (foundIndex !== -1) {\n                        accountIndex = foundIndex + 1;\n                        break;\n                    }\n                }\n                accountName = (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(accountIndex));\n            }\n            setCurrentAccount({\n                ...account,\n                accountName\n            });\n            setSelectedWalletId(account.accountId);\n        }\n    }, [\n        walletStore.currentAccount,\n        walletStore.walletList,\n        t,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18nStore.getState().translations\n    ]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function(action) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        if (action === \"search\") {\n            router.push(\"/wallet/search\");\n        }\n        if (action === \"copy\") {\n            try {\n                await navigator.clipboard.writeText(currentAccount.eth.address);\n                toast.show(t(\"success.addressCopied\") || \"地址已复制到剪贴板\", {\n                    duration: 2000\n                });\n            } catch (err) {\n                toast.show(t(\"home.copyFailed\") || \"复制失败，请手动复制\", {\n                    duration: 2000\n                });\n            }\n        }\n        if (action === \"setting\") {\n            router.push(\"/wallet/setting\");\n        }\n        if (action === \"exchange\") {\n            router.push(\"/wallet/convert\");\n        }\n        if (action === \"buy\") {\n            router.push(\"/wallet/buy\");\n        }\n        if (action === \"buyRise\") {\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_16__[\"default\"].setItem(\"buyRiseAccount\", JSON.stringify(data));\n            router.push(\"/wallet/buyRise\");\n        }\n        if (action === \"tixian\") {\n            window.open(app_utils_constants__WEBPACK_IMPORTED_MODULE_17__.TIXIAN_URL, \"_blank\");\n        }\n        if (action === \"learnDefi\") {\n            // window.open('https://www.coinbase.com/zh-sg/learn/wallet/how-to-get-started-in-defi', '_blank')\n            window.location.href = \"https://www.coinbase.com/zh-sg/learn/wallet/how-to-get-started-in-defi\";\n        }\n    }, [\n        currentAccount\n    ]);\n    // 获取当前地址有余额的链\n    const balanceFilterList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const keys = Object.keys(walletStore.currentAccount);\n        let _accountList = [];\n        keys.forEach((key)=>{\n            const _item = walletStore.currentAccount[key];\n            if (Number(_item === null || _item === void 0 ? void 0 : _item.balance) > 0) {\n                const _account = walletStore.currentAccount[key];\n                if (_account.accountType === \"btc\") {\n                    _account.name = \"Bitcoin\";\n                    _account.logo = _assets_images_wallet_bitcoin_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n                }\n                if (_account.accountType === \"eth\") {\n                    _account.name = \"Ethereum\";\n                    _account.logo = _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                }\n                if (_account.accountType === \"bsc\") {\n                    _account.name = \"BNB Smart\";\n                    _account.logo = _assets_images_wallet_bnb_smart_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n                }\n                if (_account.accountType === \"solana\") {\n                    _account.name = \"Solana\";\n                    _account.logo = _assets_images_wallet_solana_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n                }\n                _accountList.push(_account);\n            }\n        });\n        return _accountList;\n    }, [\n        walletStore.currentAccount\n    ]);\n    console.log(balanceFilterList);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n        \"data-at\": \"screen.tsx:174\",\n        \"data-in\": \"UserScreen\",\n        \"data-is\": \"YStack\",\n        height: \"100vh\",\n        bg: \"#0A0B0D\",\n        width: \"100%\",\n        maxW: 640,\n        margin: \"auto\",\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                \"data-at\": \"screen.tsx:175\",\n                \"data-in\": \"UserScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                gap: \"$3\",\n                p: \"$4\",\n                overflow: \"scroll\",\n                pb: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:176\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:177-183\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                space: \"$2\",\n                                onPress: ()=>{\n                                    setIsOpen(true);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar, {\n                                        circular: true,\n                                        size: 24,\n                                        mr: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Image, {\n                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(currentAccount.accountId),\n                                                accessibilityLabel: currentAccount.accountId\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Avatar.Fallback, {\n                                                backgroundColor: \"$blue10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                        \"data-at\": \"screen.tsx:192\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 14,\n                                        children: currentAccount.accountName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronDown, {\n                                        color: \"#8B8F9A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:197\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                gap: \"$3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:198\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"search\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:199\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:201\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"copy\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:202\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20,\n                                                marginHorizontal: 8\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                                        \"data-at\": \"screen.tsx:204\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"setting\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"screen.tsx:205\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                            width: 18,\n                                            style: {\n                                                width: 18,\n                                                height: 18\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:209\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$2\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.H2, {\n                            \"data-at\": \"screen.tsx:210\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"H2\",\n                            textAlign: \"left\",\n                            color: \"#fff\",\n                            children: [\n                                \"$ \",\n                                walletStore.getCurrentAccountBalance().toFixed(4)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:235\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        gap: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:236-247\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#333\",\n                                    background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                                },\n                                onPress: ()=>handleAction(\"buy\"),\n                                children: \"买入\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:250-258\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#fff\",\n                                    background: \"#282B32\"\n                                },\n                                onPress: ()=>handleAction(\"exchange\"),\n                                children: \"兑换\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:261-269\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                size: \"$6\",\n                                width: 109,\n                                height: 36,\n                                fontSize: 14,\n                                fontWeight: 500,\n                                style: {\n                                    color: \"#8B8F9A\",\n                                    background: \"#15161A\"\n                                },\n                                onPress: ()=>handleAction(\"tixian\"),\n                                children: \"提现\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:273\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        height: 1,\n                        bg: \"#212224\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                        \"data-at\": \"screen.tsx:274\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$5\",\n                        height: 26,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(0),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:280\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"加密货币\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(1),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:287\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 66\n                                        }, this),\n                                        currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.Pressable, {\n                                onPress: ()=>setCurrentTab(2),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                    style: {\n                                        position: \"relative\"\n                                    },\n                                    children: [\n                                        currentTab === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveText, {\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"screen.tsx:293\",\n                                            \"data-in\": \"UserScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"#fff\",\n                                            children: \"DeFi\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 67\n                                        }, this),\n                                        currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    currentTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: balanceFilterList.length > 0 ? balanceFilterList.map((item)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                \"data-at\": \"screen.tsx:304\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"XStack\",\n                                mt: 30,\n                                items: \"center\",\n                                justifyContent: \"space-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                        \"data-at\": \"screen.tsx:305\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"XStack\",\n                                        items: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                                \"data-at\": \"screen.tsx:306\",\n                                                \"data-in\": \"UserScreen\",\n                                                \"data-is\": \"Image\",\n                                                source: item.logo.src,\n                                                width: 32,\n                                                height: 32,\n                                                mr: 6\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                \"data-at\": \"screen.tsx:307\",\n                                                \"data-in\": \"UserScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.XStack, {\n                                        \"data-at\": \"screen.tsx:311\",\n                                        \"data-in\": \"UserScreen\",\n                                        \"data-is\": \"XStack\",\n                                        justifyContent: \"flex-end\",\n                                        onPress: ()=>handleAction(\"buyRise\", item),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_11__.View, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"screen.tsx:313\",\n                                                        \"data-in\": \"UserScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        fontWeight: \"bold\",\n                                                        children: item.balance ? parseFloat(item.balance).toFixed(4) : \"0.0000\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"screen.tsx:316\",\n                                                        \"data-in\": \"UserScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 12,\n                                                        color: \"#8B8F9A\",\n                                                        children: \"可用\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronRight, {\n                                                size: 20,\n                                                color: \"$white6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 18\n                            }, this);\n                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                            \"data-at\": \"screen.tsx:326-334\",\n                            \"data-in\": \"UserScreen\",\n                            \"data-is\": \"YStack\",\n                            cursor: \"pointer\",\n                            margin: \"auto\",\n                            width: 300,\n                            flex: 1,\n                            alignContent: \"center\",\n                            alignItems: \"center\",\n                            mt: \"$15\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                    \"data-at\": \"screen.tsx:335\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                    style: {\n                                        width: 174,\n                                        height: 91\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:336\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 16,\n                                    color: \"#fff\",\n                                    fontWeight: \"bold\",\n                                    mt: \"$4\",\n                                    text: \"center\",\n                                    children: \"添加加密货币以开始使用\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:339\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    color: \"#8B8F9A\",\n                                    mt: \"$2\",\n                                    text: \"center\",\n                                    children: \"您可以使用您的 Coinbase 账户或其他钱包添加资产。\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 14\n                        }, this)\n                    }, void 0, false),\n                    currentTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:349\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        mt: 20,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                            value: selectedNetwork,\n                            onValueChange: setSelectedNetwork,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Trigger, {\n                                    width: 160,\n                                    height: 36,\n                                    backgroundColor: \"#282B32\",\n                                    borderColor: \"#3A3D44\",\n                                    borderWidth: 2,\n                                    borderRadius: 12,\n                                    paddingHorizontal: 12,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Value, {\n                                            placeholder: \"选择网络\",\n                                            color: \"white\",\n                                            fontSize: 14\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Icon, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronDown, {\n                                                size: 16,\n                                                color: \"white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Content, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ScrollUpButton, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Viewport, {\n                                            children: networkOptions.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.Item, {\n                                                    index: index,\n                                                    value: option.value,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ItemText, {\n                                                        color: \"white\",\n                                                        children: option.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, option.value, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 58\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Select.ScrollDownButton, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 30\n                    }, this),\n                    currentTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.YStack, {\n                        \"data-at\": \"screen.tsx:382-390\",\n                        \"data-in\": \"UserScreen\",\n                        \"data-is\": \"YStack\",\n                        cursor: \"pointer\",\n                        margin: \"auto\",\n                        width: 300,\n                        flex: 1,\n                        alignContent: \"center\",\n                        alignItems: \"center\",\n                        mt: \"$15\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                \"data-at\": \"screen.tsx:391\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                style: {\n                                    width: 174,\n                                    height: 91\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:392\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 16,\n                                color: \"#fff\",\n                                fontWeight: \"bold\",\n                                mt: \"$4\",\n                                text: \"center\",\n                                children: \"开始通过 DeFi 赚取收益\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"screen.tsx:395\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                color: \"#8B8F9A\",\n                                mt: \"$2\",\n                                text: \"center\",\n                                children: \"了解如何使用去中心化应用赚取加密货币。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.Button, {\n                                \"data-at\": \"screen.tsx:398-404\",\n                                \"data-in\": \"UserScreen\",\n                                \"data-is\": \"Button\",\n                                mt: 100,\n                                width: \"100%\",\n                                backgroundColor: \"#282B32\",\n                                borderRadius: 30,\n                                onPress: ()=>handleAction(\"learnDefi\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"screen.tsx:405\",\n                                    \"data-in\": \"UserScreen\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 16,\n                                    fontWeight: \"bold\",\n                                    children: \"了解 DeFi\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 30\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_19__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_12__.WalletSheet, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                wallets: walletStore.walletList,\n                selectedId: selectedWalletId,\n                onSelect: (wallet, index)=>{\n                    if (wallet === \"addWallet\") {\n                        router.push(\"/wallet/manager\");\n                    } else {\n                        // 使用 store 的方法设置当前账户\n                        walletStore.setCurrentAccount(wallet);\n                        setSelectedWalletId(wallet.accountId);\n                        // 设置显示名称\n                        const accountName = wallet.name || (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(Number(index) + 1));\n                        setCurrentAccount({\n                            ...wallet,\n                            accountName\n                        });\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/screen.tsx\",\n        lineNumber: 161,\n        columnNumber: 10\n    }, this);\n}\n_s(UserScreen, \"ot1nT94MhNFp7Gws0egrubOF89o=\", false, function() {\n    return [\n        _my_ui__WEBPACK_IMPORTED_MODULE_12__.useToastController,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_15__.useWalletStore\n    ];\n});\n_c2 = UserScreen;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"UserScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/user/screen.tsx\n"));

/***/ })

});