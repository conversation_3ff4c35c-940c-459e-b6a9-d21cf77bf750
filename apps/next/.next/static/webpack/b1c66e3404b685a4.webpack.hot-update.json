{"c": ["pages/wallet/buyTransfer", "webpack"], "r": ["pages/wallet/buy"], "m": ["../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fbuy.tsx&page=%2Fwallet%2Fbuy!", "../../packages/app/features/home/<USER>", "../../packages/app/features/home/<USER>", "../../packages/app/features/wallet/buy-screen.tsx", "../../packages/assets/images/close.png", "../../packages/assets/images/copy.png", "../../packages/assets/images/fot-icon-1-active.png", "../../packages/assets/images/fot-icon-1.png", "../../packages/assets/images/fot-icon-2-active.png", "../../packages/assets/images/fot-icon-2.png", "../../packages/assets/images/fot-icon-3-active.png", "../../packages/assets/images/fot-icon-3.png", "../../packages/assets/images/fot-icon-4-active.png", "../../packages/assets/images/fot-icon-4.png", "../../packages/assets/images/fot-icon-5-active.png", "../../packages/assets/images/fot-icon-5.png", "../../packages/assets/images/main-connect.png", "../../packages/assets/images/mint1.png", "../../packages/assets/images/mint2.png", "../../packages/assets/images/search.png", "../../packages/assets/images/setting.png", "../../packages/assets/images/wallet/change.png", "../../packages/assets/images/wallet/eth.png", "../../packages/assets/images/wallet/qrcode.png", "./pages/wallet/buy.tsx"]}