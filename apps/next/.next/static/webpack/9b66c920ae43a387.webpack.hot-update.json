{"c": ["pages/wallet/noticeSetting", "webpack"], "r": ["pages/wallet/chooseNet", "pages/wallet/safe", "pages/wallet/setting"], "m": ["../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FchooseNet.tsx&page=%2Fwallet%2FchooseNet!", "../../packages/app/features/wallet/choose-net-screen.tsx", "../../packages/assets/images/wallet/eth.png", "./pages/wallet/chooseNet.tsx", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsafe.tsx&page=%2Fwallet%2Fsafe!", "../../packages/app/features/wallet/safe-screen.tsx", "../../packages/assets/images/wallet/arrowright.png", "./pages/wallet/safe.tsx", "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsetting.tsx&page=%2Fwallet%2Fsetting!", "../../packages/app/features/wallet/setting-screen.tsx", "../../packages/assets/images/wallet/add.png", "../../packages/assets/images/wallet/set1.png", "../../packages/assets/images/wallet/set3.png", "../../packages/assets/images/wallet/set4.png", "../../packages/assets/images/wallet/set5.png", "../../packages/assets/images/wallet/set6.png", "../../packages/assets/images/wallet/set7.png", "../../packages/assets/images/wallet/user.png", "./pages/wallet/setting.tsx"]}