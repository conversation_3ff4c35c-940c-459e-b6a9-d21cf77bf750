/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/display"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fdisplay.tsx&page=%2Fwallet%2Fdisplay!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fdisplay.tsx&page=%2Fwallet%2Fdisplay! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/display\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/display.tsx */ \"./pages/wallet/display.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/display\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRmRpc3BsYXkudHN4JnBhZ2U9JTJGd2FsbGV0JTJGZGlzcGxheSEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw4REFBNEI7QUFDbkQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2ZkNjAiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvZGlzcGxheVwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L2Rpc3BsYXkudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvZGlzcGxheVwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fdisplay.tsx&page=%2Fwallet%2Fdisplay!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/arrowright.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/wallet/arrowright.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/arrowright.f0d2eb68.png\",\"height\":22,\"width\":14,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Farrowright.f0d2eb68.png&w=5&q=70\",\"blurWidth\":5,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYXJyb3dyaWdodC5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9hcnJvd3JpZ2h0LnBuZz8wNDgwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9hcnJvd3JpZ2h0LmYwZDJlYjY4LnBuZ1wiLFwiaGVpZ2h0XCI6MjIsXCJ3aWR0aFwiOjE0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFycm93cmlnaHQuZjBkMmViNjgucG5nJnc9NSZxPTcwXCIsXCJibHVyV2lkdGhcIjo1LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/arrowright.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/choose1.png":
/*!*******************************************************!*\
  !*** ../../packages/assets/images/wallet/choose1.png ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/choose1.ffca884a.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fchoose1.ffca884a.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvY2hvb3NlMS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsZ01BQWdNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9jaG9vc2UxLnBuZz84YWZjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9jaG9vc2UxLmZmY2E4ODRhLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmNob29zZTEuZmZjYTg4NGEucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/choose1.png\n"));

/***/ }),

/***/ "./pages/wallet/display.tsx":
/*!**********************************!*\
  !*** ./pages/wallet/display.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_display_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/display-screen */ \"../../packages/app/features/wallet/display-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_display_screen__WEBPACK_IMPORTED_MODULE_1__.DisplayScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/display.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvZGlzcGxheS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDa0U7QUFFbkQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDZFQUFhQTs7Ozs7QUFDdkI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3dhbGxldC9kaXNwbGF5LnRzeD83ZTc4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgRGlzcGxheVNjcmVlbiB9IGZyb20gJ2FwcC9mZWF0dXJlcy93YWxsZXQvZGlzcGxheS1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8RGlzcGxheVNjcmVlbiAvPlxufSJdLCJuYW1lcyI6WyJEaXNwbGF5U2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/display.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/display-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/display-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DisplayScreen: function() { return /* binding */ DisplayScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_choose1_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/choose1.png */ \"../../packages/assets/images/wallet/choose1.png\");\n/* harmony import */ var _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/arrowright.png */ \"../../packages/assets/images/wallet/arrowright.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst Line = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Line;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = Underline;\nconst Underlineblock = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2\n});\n_c3 = Underlineblock;\nfunction DisplayScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { t, currentLanguage } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const handleLanguagePress = ()=>{\n        router.push(\"/wallet/language\");\n    };\n    const getLanguageDisplayText = ()=>{\n        return currentLanguage === \"zh\" ? \"简体中文\" : \"English\";\n    };\n    const [checked1, setChecked1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [checked2, setChecked2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [checked3, setChecked3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [checked4, setChecked4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [themeType, setThemeType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"display-screen.tsx:73\",\n        \"data-in\": \"DisplayScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        pb: 100,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"display-screen.tsx:74\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                pr: 16,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                        title: t(\"navigation.display\") || \"显示\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                        \"data-at\": \"display-screen.tsx:76\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_choose1_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                        width: 16,\n                        height: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"display-screen.tsx:79\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"display-screen.tsx:80\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"外观\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.RadioGroup, {\n                        \"data-at\": \"display-screen.tsx:83-85\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"RadioGroup\",\n                        onValueChange: (value)=>{\n                            setThemeType(value);\n                        },\n                        value: themeType,\n                        gap: \"$2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"display-screen.tsx:86\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"space-between\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                        \"data-at\": \"display-screen.tsx:87\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                \"data-at\": \"display-screen.tsx:88\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: \"深色\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                \"data-at\": \"display-screen.tsx:91\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                color: \"$accent11\",\n                                                children: \"始终使用深色模式\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.RadioGroup.Item, {\n                                        value: \"1\",\n                                        id: \"foo-radio-item\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.RadioGroup.Indicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"display-screen.tsx:101\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                        \"data-at\": \"display-screen.tsx:102\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                \"data-at\": \"display-screen.tsx:103\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: \"浅色\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                \"data-at\": \"display-screen.tsx:106\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                color: \"$accent11\",\n                                                children: \"始终使用浅色模式\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.RadioGroup.Item, {\n                                        value: \"2\",\n                                        id: \"foo-radio-item\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.RadioGroup.Indicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"display-screen.tsx:116\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                        \"data-at\": \"display-screen.tsx:117\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                \"data-at\": \"display-screen.tsx:118\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: \"自动\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                                \"data-at\": \"display-screen.tsx:121\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                color: \"$accent11\",\n                                                children: \"使用设备的默认模式\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.RadioGroup.Item, {\n                                        value: \"3\",\n                                        id: \"foo-radio-item\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.RadioGroup.Indicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"display-screen.tsx:135\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"display-screen.tsx:136\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: t(\"navigation.language\") || \"语言\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_8__.Pressable, {\n                        onPress: handleLanguagePress,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                            \"data-at\": \"display-screen.tsx:140\",\n                            \"data-in\": \"DisplayScreen\",\n                            \"data-is\": \"XStack\",\n                            justifyContent: \"space-between\",\n                            mt: 20,\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"display-screen.tsx:141\",\n                                    \"data-in\": \"DisplayScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: \"bold\",\n                                    children: t(\"navigation.language\") || \"语言\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                    \"data-at\": \"display-screen.tsx:144\",\n                                    \"data-in\": \"DisplayScreen\",\n                                    \"data-is\": \"XStack\",\n                                    items: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                            \"data-at\": \"display-screen.tsx:145\",\n                                            \"data-in\": \"DisplayScreen\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 14,\n                                            fontWeight: 500,\n                                            mr: 10,\n                                            children: getLanguageDisplayText()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                            \"data-at\": \"display-screen.tsx:148\",\n                                            \"data-in\": \"DisplayScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            width: 5,\n                                            height: 9\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"display-screen.tsx:154\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"display-screen.tsx:155\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"余额\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"display-screen.tsx:158\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"display-screen.tsx:159\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: \"本地货币\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"display-screen.tsx:162\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:163\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: 500,\n                                        mr: 10,\n                                        children: \"USD\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                        \"data-at\": \"display-screen.tsx:166\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                        width: 5,\n                                        height: 9\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"display-screen.tsx:169\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"display-screen.tsx:170\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:171\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"私密模式\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:174\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"隐藏所有余额\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"display-screen.tsx:178-179\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    \"data-at\": \"display-screen.tsx:180-182\",\n                                    \"data-in\": \"DisplayScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$3\",\n                                    onCheckedChange: (checked)=>{\n                                        setChecked1(checked);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                        animation: \"bouncy\",\n                                        backgroundColor: checked1 ? \"$blue9\" : \"$color3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"display-screen.tsx:187\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"display-screen.tsx:188\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:189\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"隐藏小余额\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:192\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"低于 1.00 美元的余额\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"display-screen.tsx:196-197\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    \"data-at\": \"display-screen.tsx:198-200\",\n                                    \"data-in\": \"DisplayScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$3\",\n                                    onCheckedChange: (checked)=>{\n                                        setChecked2(checked);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                        animation: \"bouncy\",\n                                        backgroundColor: checked2 ? \"$blue9\" : \"$color3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"display-screen.tsx:205\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"display-screen.tsx:206\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:207\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"测试网\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:210\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"显示测试网余额\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"display-screen.tsx:214-215\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    \"data-at\": \"display-screen.tsx:216-218\",\n                                    \"data-in\": \"DisplayScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$3\",\n                                    onCheckedChange: (checked)=>{\n                                        setChecked3(checked);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                        animation: \"bouncy\",\n                                        backgroundColor: checked3 ? \"$blue9\" : \"$color3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"display-screen.tsx:225\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"display-screen.tsx:226\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"选项卡\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"display-screen.tsx:229\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"display-screen.tsx:230\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                width: \"80%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:231\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"粘性选项卡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"display-screen.tsx:234\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"即使应用程序关闭后，最后选择的选项卡也始 终保持选中状态\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"display-screen.tsx:238-239\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    \"data-at\": \"display-screen.tsx:240-242\",\n                                    \"data-in\": \"DisplayScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$3\",\n                                    onCheckedChange: (checked)=>{\n                                        setChecked4(checked);\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                        animation: \"bouncy\",\n                                        backgroundColor: checked4 ? \"$blue9\" : \"$color3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n        lineNumber: 54,\n        columnNumber: 10\n    }, this);\n}\n_s(DisplayScreen, \"TkSeYWv8zbQlKc8qB1qnwKcR+rU=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c4 = DisplayScreen;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Line\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"Underline\");\n$RefreshReg$(_c3, \"Underlineblock\");\n$RefreshReg$(_c4, \"DisplayScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL3dhbGxldC9kaXNwbGF5LXNjcmVlbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFXZTtBQUM4QjtBQUNrQztBQUV2QztBQUNSO0FBRVM7QUFFMEI7QUFFTTtBQUV6RSxNQUFNZSxPQUFPUiwrQ0FBTUEsQ0FBQ0gseUNBQUlBLEVBQUU7SUFDeEJZLE9BQU87SUFDUEMsUUFBUTtJQUNSQyxpQkFBaUI7SUFDakJDLElBQUk7QUFDTjtLQUxNSjtBQU9OLE1BQU1LLGFBQWFiLCtDQUFNQSxDQUFDRix5Q0FBSUEsRUFBRTtJQUM5QmdCLE9BQU87SUFDUEMsY0FBYztBQUNoQjs7QUFFQSxNQUFNQyxZQUFZaEIsK0NBQU1BLENBQUNILHlDQUFJQSxFQUFFO0lBQzdCb0IsVUFBVTtJQUNWQyxRQUFRLENBQUM7SUFDVEMsTUFBTTtJQUNOQyxPQUFPO0lBQ1BWLFFBQVE7SUFDUkMsaUJBQWlCO0FBQ25COztBQUNBLE1BQU1VLGlCQUFpQnJCLCtDQUFNQSxDQUFDSCx5Q0FBSUEsRUFBRTtJQUNsQ29CLFVBQVU7SUFDVkMsUUFBUSxDQUFDO0lBQ1RDLE1BQU07SUFDTkMsT0FBTztJQUNQVixRQUFRO0FBQ1Y7O0FBRU8sU0FBU1k7O0lBQ2QsTUFBTUMsU0FBUzNCLDREQUFTQTtJQUN4QixNQUFNLEVBQUU0QixDQUFDLEVBQUVDLGVBQUFBLEVBQWlCLEdBQUdwQix3REFBY0E7SUFFN0MsTUFBTXFCLHNCQUFzQkE7UUFDMUJILE9BQU9JLElBQUksQ0FBQztJQUNkO0lBRUEsTUFBTUMseUJBQXlCQTtRQUM3QixPQUFPSCxvQkFBb0IsT0FBTyxTQUFTO0lBQzdDO0lBRUEsTUFBTSxDQUFDSSxVQUFVQyxZQUFZLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUMyQixVQUFVQyxZQUFZLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUM2QixVQUFVQyxZQUFZLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUMrQixVQUFVQyxZQUFZLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUV6QyxNQUFNLENBQUNpQyxXQUFXQyxhQUFhLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUUzQyxxQkFDRSw4REFBQ1QsMENBQU1BO1FBQUE0QyxXQUFBO1FBQUFDLFdBQUE7UUFBQUMsV0FBQTtRQUFDQyxJQUFHO1FBQWNDLElBQUk7OzBCQUMzQiw4REFBQ2pELDBDQUFNQTtnQkFBQTZDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNHLGdCQUFlO2dCQUFnQkMsT0FBTTtnQkFBU0MsSUFBSTs7a0NBQ3hELDhEQUFDckQsMENBQU1BO3dCQUFDc0QsT0FBT3ZCLEVBQUUseUJBQXlCO3dCQUFNd0IsUUFBUSxJQUFNekIsT0FBTzBCLElBQUk7Ozs7OztrQ0FDekUsOERBQUNsRCwwQ0FBS0E7d0JBQUF3QyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDUyxRQUFRNUMsNkVBQWU7d0JBQUVHLE9BQU87d0JBQUlDLFFBQVE7Ozs7Ozs7Ozs7OzswQkFHckQsOERBQUNmLDBDQUFNQTtnQkFBQTRDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNXLElBQUk7Z0JBQUl4QyxJQUFJO2dCQUFJeUMsSUFBSTs7a0NBQzFCLDhEQUFDdkQseUNBQUlBO3dCQUFBeUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ2EsVUFBVTt3QkFBSUMsWUFBVztrQ0FBTTs7Ozs7O2tDQUdyQyw4REFBQ3JELCtDQUFVQTt3QkFBQXFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNlLGVBQWVDLENBQUFBOzRCQUN6Qm5CLGFBQWFtQjt3QkFDZjt3QkFBR0EsT0FBT3BCO3dCQUFXcUIsS0FBSTs7MENBQ3ZCLDhEQUFDaEUsMENBQU1BO2dDQUFBNkMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ0csZ0JBQWU7Z0NBQWdCaEMsSUFBSTs7a0RBQ3pDLDhEQUFDZix5Q0FBSUE7d0NBQUEwQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBOzswREFDSCw4REFBQzNDLHlDQUFJQTtnREFBQXlDLFdBQUE7Z0RBQUFDLFdBQUE7Z0RBQUFDLFdBQUE7Z0RBQUNhLFVBQVU7Z0RBQUlDLFlBQVc7MERBQU07Ozs7OzswREFHckMsOERBQUN6RCx5Q0FBSUE7Z0RBQUF5QyxXQUFBO2dEQUFBQyxXQUFBO2dEQUFBQyxXQUFBO2dEQUFDYSxVQUFVO2dEQUFJQyxZQUFXO2dEQUFPekMsT0FBTTswREFBVzs7Ozs7Ozs7Ozs7O2tEQUt6RCw4REFBQ1osK0NBQVVBLENBQUN5RCxJQUFJO3dDQUFDRixPQUFNO3dDQUFJRyxJQUFHO2tEQUM1Qiw0RUFBQzFELCtDQUFVQSxDQUFDMkQsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJekIsOERBQUNuRSwwQ0FBTUE7Z0NBQUE2QyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFDRyxnQkFBZTtnQ0FBZ0JrQixZQUFXO2dDQUFTbEQsSUFBSTs7a0RBQzdELDhEQUFDZix5Q0FBSUE7d0NBQUEwQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBOzswREFDSCw4REFBQzNDLHlDQUFJQTtnREFBQXlDLFdBQUE7Z0RBQUFDLFdBQUE7Z0RBQUFDLFdBQUE7Z0RBQUNhLFVBQVU7Z0RBQUlDLFlBQVc7MERBQU07Ozs7OzswREFHckMsOERBQUN6RCx5Q0FBSUE7Z0RBQUF5QyxXQUFBO2dEQUFBQyxXQUFBO2dEQUFBQyxXQUFBO2dEQUFDYSxVQUFVO2dEQUFJQyxZQUFXO2dEQUFPekMsT0FBTTswREFBVzs7Ozs7Ozs7Ozs7O2tEQUt6RCw4REFBQ1osK0NBQVVBLENBQUN5RCxJQUFJO3dDQUFDRixPQUFNO3dDQUFJRyxJQUFHO2tEQUM1Qiw0RUFBQzFELCtDQUFVQSxDQUFDMkQsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJekIsOERBQUNuRSwwQ0FBTUE7Z0NBQUE2QyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFDRyxnQkFBZTtnQ0FBZ0JrQixZQUFXO2dDQUFTbEQsSUFBSTs7a0RBQzdELDhEQUFDZix5Q0FBSUE7d0NBQUEwQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBOzswREFDSCw4REFBQzNDLHlDQUFJQTtnREFBQXlDLFdBQUE7Z0RBQUFDLFdBQUE7Z0RBQUFDLFdBQUE7Z0RBQUNhLFVBQVU7Z0RBQUlDLFlBQVc7MERBQU07Ozs7OzswREFHckMsOERBQUN6RCx5Q0FBSUE7Z0RBQUF5QyxXQUFBO2dEQUFBQyxXQUFBO2dEQUFBQyxXQUFBO2dEQUFDYSxVQUFVO2dEQUFJQyxZQUFXO2dEQUFPekMsT0FBTTswREFBVzs7Ozs7Ozs7Ozs7O2tEQUt6RCw4REFBQ1osK0NBQVVBLENBQUN5RCxJQUFJO3dDQUFDRixPQUFNO3dDQUFJRyxJQUFHO2tEQUM1Qiw0RUFBQzFELCtDQUFVQSxDQUFDMkQsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNN0IsOERBQUNyRDs7Ozs7MEJBRUQsOERBQUNiLDBDQUFNQTtnQkFBQTRDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNXLElBQUk7Z0JBQUl4QyxJQUFJO2dCQUFJeUMsSUFBSTs7a0NBQzFCLDhEQUFDdkQseUNBQUlBO3dCQUFBeUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ2EsVUFBVTt3QkFBSUMsWUFBVztrQ0FDNUIvQixFQUFFLDBCQUEwQjs7Ozs7O2tDQUUvQiw4REFBQ3JCLG1EQUFTQTt3QkFBQzRELFNBQVNyQztrQ0FDbEIsNEVBQUNoQywwQ0FBTUE7NEJBQUE2QyxXQUFBOzRCQUFBQyxXQUFBOzRCQUFBQyxXQUFBOzRCQUFDRyxnQkFBZTs0QkFBZ0JoQyxJQUFJOzRCQUFJa0QsWUFBVzs7OENBQ3hELDhEQUFDaEUseUNBQUlBO29DQUFBeUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQ2EsVUFBVTtvQ0FBSUMsWUFBVzs4Q0FDNUIvQixFQUFFLDBCQUEwQjs7Ozs7OzhDQUUvQiw4REFBQzlCLDBDQUFNQTtvQ0FBQTZDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUNJLE9BQU07O3NEQUNaLDhEQUFDL0MseUNBQUlBOzRDQUFBeUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQ2EsVUFBVTs0Q0FBSUMsWUFBWTs0Q0FBS1MsSUFBSTtzREFDdENwQzs7Ozs7O3NEQUVILDhEQUFDN0IsMENBQUtBOzRDQUFBd0MsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQ1MsUUFBUTNDLGdGQUFrQjs0Q0FBRUUsT0FBTzs0Q0FBR0MsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSzdELDhEQUFDRjs7Ozs7MEJBQ0QsOERBQUNiLDBDQUFNQTtnQkFBQTRDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNXLElBQUk7Z0JBQUl4QyxJQUFJO2dCQUFJeUMsSUFBSTs7a0NBQzFCLDhEQUFDdkQseUNBQUlBO3dCQUFBeUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ2EsVUFBVTt3QkFBSUMsWUFBVztrQ0FBTTs7Ozs7O2tDQUdyQyw4REFBQzdELDBDQUFNQTt3QkFBQTZDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNHLGdCQUFlO3dCQUFnQmhDLElBQUk7OzBDQUN6Qyw4REFBQ2QseUNBQUlBO2dDQUFBeUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ2EsVUFBVTtnQ0FBSUMsWUFBVzswQ0FBTTs7Ozs7OzBDQUdyQyw4REFBQzdELDBDQUFNQTtnQ0FBQTZDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUNJLE9BQU07O2tEQUNaLDhEQUFDL0MseUNBQUlBO3dDQUFBeUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ2EsVUFBVTt3Q0FBSUMsWUFBWTt3Q0FBS1MsSUFBSTtrREFBRzs7Ozs7O2tEQUc1Qyw4REFBQ2pFLDBDQUFLQTt3Q0FBQXdDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNTLFFBQVEzQyxnRkFBa0I7d0NBQUVFLE9BQU87d0NBQUdDLFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FHekQsOERBQUNoQiwwQ0FBTUE7d0JBQUE2QyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDRyxnQkFBZTt3QkFBZ0JoQyxJQUFJOzswQ0FDekMsOERBQUNmLHlDQUFJQTtnQ0FBQTBDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7O2tEQUNILDhEQUFDM0MseUNBQUlBO3dDQUFBeUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ2EsVUFBVTt3Q0FBSUMsWUFBVztrREFBTTs7Ozs7O2tEQUdyQyw4REFBQ3pELHlDQUFJQTt3Q0FBQXlDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNhLFVBQVU7d0NBQUlDLFlBQVc7d0NBQU96QyxPQUFNO3dDQUFZRixJQUFJO2tEQUFFOzs7Ozs7Ozs7Ozs7MENBSWhFLDhEQUFDZix5Q0FBSUE7Z0NBQUEwQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBOzBDQUVILDRFQUFDeEMsMkNBQU1BO29DQUFBc0MsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQ3dCLE1BQUs7b0NBQUtDLGlCQUFrQkMsQ0FBQUE7d0NBQ2xDckMsWUFBWXFDO29DQUNkOzhDQUNFLDRFQUFDbEUsMkNBQU1BLENBQUNtRSxLQUFLO3dDQUFDQyxXQUFVO3dDQUFTMUQsaUJBQWlCa0IsV0FBVyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUk5RSw4REFBQ25DLDBDQUFNQTt3QkFBQTZDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNHLGdCQUFlO3dCQUFnQmhDLElBQUk7OzBDQUN6Qyw4REFBQ2YseUNBQUlBO2dDQUFBMEMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTs7a0RBQ0gsOERBQUMzQyx5Q0FBSUE7d0NBQUF5QyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDYSxVQUFVO3dDQUFJQyxZQUFXO2tEQUFNOzs7Ozs7a0RBR3JDLDhEQUFDekQseUNBQUlBO3dDQUFBeUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ2EsVUFBVTt3Q0FBSUMsWUFBVzt3Q0FBT3pDLE9BQU07d0NBQVlGLElBQUk7a0RBQUU7Ozs7Ozs7Ozs7OzswQ0FJaEUsOERBQUNmLHlDQUFJQTtnQ0FBQTBDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7MENBRUgsNEVBQUN4QywyQ0FBTUE7b0NBQUFzQyxXQUFBO29DQUFBQyxXQUFBO29DQUFBQyxXQUFBO29DQUFDd0IsTUFBSztvQ0FBS0MsaUJBQWtCQyxDQUFBQTt3Q0FDbENuQyxZQUFZbUM7b0NBQ2Q7OENBQ0UsNEVBQUNsRSwyQ0FBTUEsQ0FBQ21FLEtBQUs7d0NBQUNDLFdBQVU7d0NBQVMxRCxpQkFBaUJvQixXQUFXLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSTlFLDhEQUFDckMsMENBQU1BO3dCQUFBNkMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ0csZ0JBQWU7d0JBQWdCaEMsSUFBSTs7MENBQ3pDLDhEQUFDZix5Q0FBSUE7Z0NBQUEwQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBOztrREFDSCw4REFBQzNDLHlDQUFJQTt3Q0FBQXlDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNhLFVBQVU7d0NBQUlDLFlBQVc7a0RBQU07Ozs7OztrREFHckMsOERBQUN6RCx5Q0FBSUE7d0NBQUF5QyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDYSxVQUFVO3dDQUFJQyxZQUFXO3dDQUFPekMsT0FBTTt3Q0FBWUYsSUFBSTtrREFBRTs7Ozs7Ozs7Ozs7OzBDQUloRSw4REFBQ2YseUNBQUlBO2dDQUFBMEMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTswQ0FFSCw0RUFBQ3hDLDJDQUFNQTtvQ0FBQXNDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUN3QixNQUFLO29DQUFLQyxpQkFBa0JDLENBQUFBO3dDQUNsQ2pDLFlBQVlpQztvQ0FDZDs4Q0FDRSw0RUFBQ2xFLDJDQUFNQSxDQUFDbUUsS0FBSzt3Q0FBQ0MsV0FBVTt3Q0FBUzFELGlCQUFpQnNCLFdBQVcsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLaEYsOERBQUN6Qjs7Ozs7MEJBQ0QsOERBQUNiLDBDQUFNQTtnQkFBQTRDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNXLElBQUk7Z0JBQUl4QyxJQUFJO2dCQUFJeUMsSUFBSTs7a0NBQzFCLDhEQUFDdkQseUNBQUlBO3dCQUFBeUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ2EsVUFBVTt3QkFBSUMsWUFBVztrQ0FBTTs7Ozs7O2tDQUdyQyw4REFBQzdELDBDQUFNQTt3QkFBQTZDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNHLGdCQUFlO3dCQUFnQmhDLElBQUk7OzBDQUN6Qyw4REFBQ2YseUNBQUlBO2dDQUFBMEMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ2hDLE9BQU07O2tEQUNWLDhEQUFDWCx5Q0FBSUE7d0NBQUF5QyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDYSxVQUFVO3dDQUFJQyxZQUFXO2tEQUFNOzs7Ozs7a0RBR3JDLDhEQUFDekQseUNBQUlBO3dDQUFBeUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ2EsVUFBVTt3Q0FBSUMsWUFBVzt3Q0FBT3pDLE9BQU07d0NBQVlGLElBQUk7a0RBQUU7Ozs7Ozs7Ozs7OzswQ0FJaEUsOERBQUNmLHlDQUFJQTtnQ0FBQTBDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7MENBRUgsNEVBQUN4QywyQ0FBTUE7b0NBQUFzQyxXQUFBO29DQUFBQyxXQUFBO29DQUFBQyxXQUFBO29DQUFDd0IsTUFBSztvQ0FBS0MsaUJBQWtCQyxDQUFBQTt3Q0FDbEMvQixZQUFZK0I7b0NBQ2Q7OENBQ0UsNEVBQUNsRSwyQ0FBTUEsQ0FBQ21FLEtBQUs7d0NBQUNDLFdBQVU7d0NBQVMxRCxpQkFBaUJ3QixXQUFXLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPdEY7R0FyTWdCYjs7UUFDQzFCLHdEQUFTQTtRQUNPUyxvREFBY0E7OztNQUYvQmlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hcHAvZmVhdHVyZXMvd2FsbGV0L2Rpc3BsYXktc2NyZWVuLnRzeD84Mjg1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIEF2YXRhcixcbiAgQnV0dG9uLFxuICBIMyxcbiAgSDUsXG4gIE5hdkJhcixcbiAgUGFyYWdyYXBoLFxuICBTd2l0Y2hUaGVtZUJ1dHRvbixcbiAgWFN0YWNrLFxuICBZU3RhY2ssXG4gIElucHV0LFxufSBmcm9tICdAbXkvdWknXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICdzb2xpdG8vbmF2aWdhdGlvbidcbmltcG9ydCB7IFZpZXcsIFRleHQsIEltYWdlLCBIMSwgSDIsIHN0eWxlZCwgU3dpdGNoLCBSYWRpb0dyb3VwIH0gZnJvbSAndGFtYWd1aSdcblxuaW1wb3J0IHsgUHJlc3NhYmxlIH0gZnJvbSAncmVhY3QtbmF0aXZlJ1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENoZXZyb25SaWdodCB9IGZyb20gJ0B0YW1hZ3VpL2x1Y2lkZS1pY29ucydcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnYXBwL2kxOG4nXG5pbXBvcnQgbWFpbkNvbm5ldEljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy9tYWluLWNvbm5lY3QucG5nJ1xuaW1wb3J0IGNob29zZTFJY29uIGZyb20gJy4uLy4uLy4uL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2Nob29zZTEucG5nJ1xuaW1wb3J0IGV0aEljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy93YWxsZXQvZXRoLnBuZydcbmltcG9ydCBhcnJvd1JpZ2h0SWNvbiBmcm9tICcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL3dhbGxldC9hcnJvd3JpZ2h0LnBuZydcblxuY29uc3QgTGluZSA9IHN0eWxlZChWaWV3LCB7XG4gIHdpZHRoOiAnMTAwJScsXG4gIGhlaWdodDogMSxcbiAgYmFja2dyb3VuZENvbG9yOiAnIzIxMjIyNCcsXG4gIG10OiAxMCxcbn0pXG5cbmNvbnN0IEFjdGl2ZVRleHQgPSBzdHlsZWQoVGV4dCwge1xuICBjb2xvcjogJyM0NTc1RkYnLFxuICBtYXJnaW5Cb3R0b206IDIsXG59KVxuXG5jb25zdCBVbmRlcmxpbmUgPSBzdHlsZWQoVmlldywge1xuICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgYm90dG9tOiAtMixcbiAgbGVmdDogMCxcbiAgcmlnaHQ6IDAsXG4gIGhlaWdodDogMixcbiAgYmFja2dyb3VuZENvbG9yOiAnIzQ1NzVGRicsXG59KVxuY29uc3QgVW5kZXJsaW5lYmxvY2sgPSBzdHlsZWQoVmlldywge1xuICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgYm90dG9tOiAtMixcbiAgbGVmdDogMCxcbiAgcmlnaHQ6IDAsXG4gIGhlaWdodDogMixcbn0pXG5cbmV4cG9ydCBmdW5jdGlvbiBEaXNwbGF5U2NyZWVuKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCB7IHQsIGN1cnJlbnRMYW5ndWFnZSB9ID0gdXNlVHJhbnNsYXRpb24oKVxuXG4gIGNvbnN0IGhhbmRsZUxhbmd1YWdlUHJlc3MgPSAoKSA9PiB7XG4gICAgcm91dGVyLnB1c2goJy93YWxsZXQvbGFuZ3VhZ2UnKVxuICB9XG5cbiAgY29uc3QgZ2V0TGFuZ3VhZ2VEaXNwbGF5VGV4dCA9ICgpID0+IHtcbiAgICByZXR1cm4gY3VycmVudExhbmd1YWdlID09PSAnemgnID8gJ+eugOS9k+S4reaWhycgOiAnRW5nbGlzaCdcbiAgfVxuXG4gIGNvbnN0IFtjaGVja2VkMSwgc2V0Q2hlY2tlZDFdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtjaGVja2VkMiwgc2V0Q2hlY2tlZDJdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtjaGVja2VkMywgc2V0Q2hlY2tlZDNdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtjaGVja2VkNCwgc2V0Q2hlY2tlZDRdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3QgW3RoZW1lVHlwZSwgc2V0VGhlbWVUeXBlXSA9IHVzZVN0YXRlKCcxJylcblxuICByZXR1cm4gKFxuICAgIDxZU3RhY2sgYmc9XCIkYmFja2dyb3VuZFwiIHBiPXsxMDB9PlxuICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIiBpdGVtcz1cImNlbnRlclwiIHByPXsxNn0+XG4gICAgICAgIDxOYXZCYXIgdGl0bGU9e3QoJ25hdmlnYXRpb24uZGlzcGxheScpIHx8ICfmmL7npLonfSBvbkJhY2s9eygpID0+IHJvdXRlci5iYWNrKCl9IC8+XG4gICAgICAgIDxJbWFnZSBzb3VyY2U9e2Nob29zZTFJY29uLnNyY30gd2lkdGg9ezE2fSBoZWlnaHQ9ezE2fSAvPlxuICAgICAgPC9YU3RhY2s+XG5cbiAgICAgIDxZU3RhY2sgcHg9ezE2fSBtdD17MzB9IG1iPXsyMH0+XG4gICAgICAgIDxUZXh0IGZvbnRTaXplPXsxOH0gZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICDlpJbop4JcbiAgICAgICAgPC9UZXh0PlxuICAgICAgICA8UmFkaW9Hcm91cCBvblZhbHVlQ2hhbmdlPXt2YWx1ZSA9PiB7XG4gICAgICAgICAgc2V0VGhlbWVUeXBlKHZhbHVlKVxuICAgICAgICB9fSB2YWx1ZT17dGhlbWVUeXBlfSBnYXA9XCIkMlwiPlxuICAgICAgICAgIDxYU3RhY2sganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezIwfT5cbiAgICAgICAgICAgIDxWaWV3PlxuICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCI+XG4gICAgICAgICAgICAgICAg5rex6ImyXG4gICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiIGNvbG9yPVwiJGFjY2VudDExXCI+XG4gICAgICAgICAgICAgICAg5aeL57uI5L2/55So5rex6Imy5qih5byPXG4gICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICAgIHsvKiA8UmFkaW9Hcm91cCB2YWx1ZT1cImZvb1wiIGdhcD1cIiQyXCI+ICovfVxuICAgICAgICAgICAgPFJhZGlvR3JvdXAuSXRlbSB2YWx1ZT1cIjFcIiBpZD1cImZvby1yYWRpby1pdGVtXCI+XG4gICAgICAgICAgICAgIDxSYWRpb0dyb3VwLkluZGljYXRvciAvPlxuICAgICAgICAgICAgPC9SYWRpb0dyb3VwLkl0ZW0+XG4gICAgICAgICAgICB7LyogPC9SYWRpb0dyb3VwPiAqL31cbiAgICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIGFsaWduSXRlbXM9XCJjZW50ZXJcIiBtdD17MjB9PlxuICAgICAgICAgICAgPFZpZXc+XG4gICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICAgICAgICDmtYXoibJcbiAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCIkYWNjZW50MTFcIj5cbiAgICAgICAgICAgICAgICDlp4vnu4jkvb/nlKjmtYXoibLmqKHlvI9cbiAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgICAgey8qIDxSYWRpb0dyb3VwIHZhbHVlPVwiZm9vXCIgZ2FwPVwiJDJcIj4gKi99XG4gICAgICAgICAgICA8UmFkaW9Hcm91cC5JdGVtIHZhbHVlPVwiMlwiIGlkPVwiZm9vLXJhZGlvLWl0ZW1cIj5cbiAgICAgICAgICAgICAgPFJhZGlvR3JvdXAuSW5kaWNhdG9yIC8+XG4gICAgICAgICAgICA8L1JhZGlvR3JvdXAuSXRlbT5cbiAgICAgICAgICAgIHsvKiA8L1JhZGlvR3JvdXA+ICovfVxuICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgIDxYU3RhY2sganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgYWxpZ25JdGVtcz1cImNlbnRlclwiIG10PXsyMH0+XG4gICAgICAgICAgICA8Vmlldz5cbiAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICAgIOiHquWKqFxuICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cIiRhY2NlbnQxMVwiPlxuICAgICAgICAgICAgICAgIOS9v+eUqOiuvuWkh+eahOm7mOiupOaooeW8j1xuICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICB7LyogPFJhZGlvR3JvdXAgdmFsdWU9XCJmb29cIiBnYXA9XCIkMlwiPiAqL31cbiAgICAgICAgICAgIDxSYWRpb0dyb3VwLkl0ZW0gdmFsdWU9XCIzXCIgaWQ9XCJmb28tcmFkaW8taXRlbVwiPlxuICAgICAgICAgICAgICA8UmFkaW9Hcm91cC5JbmRpY2F0b3IgLz5cbiAgICAgICAgICAgIDwvUmFkaW9Hcm91cC5JdGVtPlxuICAgICAgICAgICAgey8qIDwvUmFkaW9Hcm91cD4gKi99XG4gICAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgIDwvUmFkaW9Hcm91cD5cbiAgICAgIDwvWVN0YWNrPlxuICAgICAgPExpbmUgLz5cblxuICAgICAgPFlTdGFjayBweD17MTZ9IG10PXszMH0gbWI9ezIwfT5cbiAgICAgICAgPFRleHQgZm9udFNpemU9ezE4fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgIHt0KCduYXZpZ2F0aW9uLmxhbmd1YWdlJykgfHwgJ+ivreiogCd9XG4gICAgICAgIDwvVGV4dD5cbiAgICAgICAgPFByZXNzYWJsZSBvblByZXNzPXtoYW5kbGVMYW5ndWFnZVByZXNzfT5cbiAgICAgICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIG10PXsyMH0gYWxpZ25JdGVtcz1cImNlbnRlclwiPlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICB7dCgnbmF2aWdhdGlvbi5sYW5ndWFnZScpIHx8ICfor63oqIAnfVxuICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgPFhTdGFjayBpdGVtcz1cImNlbnRlclwiPlxuICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9ezUwMH0gbXI9ezEwfT5cbiAgICAgICAgICAgICAgICB7Z2V0TGFuZ3VhZ2VEaXNwbGF5VGV4dCgpfVxuICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e2Fycm93UmlnaHRJY29uLnNyY30gd2lkdGg9ezV9IGhlaWdodD17OX0gLz5cbiAgICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICA8L1ByZXNzYWJsZT5cbiAgICAgIDwvWVN0YWNrPlxuICAgICAgPExpbmUgLz5cbiAgICAgIDxZU3RhY2sgcHg9ezE2fSBtdD17MzB9IG1iPXsyMH0+XG4gICAgICAgIDxUZXh0IGZvbnRTaXplPXsxOH0gZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICDkvZnpop1cbiAgICAgICAgPC9UZXh0PlxuICAgICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIG10PXsyMH0+XG4gICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAg5pys5Zyw6LSn5biBXG4gICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDxYU3RhY2sgaXRlbXM9XCJjZW50ZXJcIj5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD17NTAwfSBtcj17MTB9PlxuICAgICAgICAgICAgICBVU0RcbiAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e2Fycm93UmlnaHRJY29uLnNyY30gd2lkdGg9ezV9IGhlaWdodD17OX0gLz5cbiAgICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgIDxYU3RhY2sganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezIwfT5cbiAgICAgICAgICA8Vmlldz5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICAgICAg56eB5a+G5qih5byPXG4gICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCIkYWNjZW50MTFcIiBtdD17Mn0+XG4gICAgICAgICAgICAgIOmakOiXj+aJgOacieS9meminVxuICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICA8Vmlld1xuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTd2l0Y2ggc2l6ZT1cIiQzXCIgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4ge1xuICAgICAgICAgICAgICBzZXRDaGVja2VkMShjaGVja2VkKVxuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxTd2l0Y2guVGh1bWIgYW5pbWF0aW9uPVwiYm91bmN5XCIgYmFja2dyb3VuZENvbG9yPXtjaGVja2VkMSA/ICckYmx1ZTknIDogJyRjb2xvcjMnfSAvPlxuICAgICAgICAgICAgPC9Td2l0Y2g+XG4gICAgICAgICAgPC9WaWV3PlxuICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIiBtdD17MjB9PlxuICAgICAgICAgIDxWaWV3PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAgICDpmpDol4/lsI/kvZnpop1cbiAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cIiRhY2NlbnQxMVwiIG10PXsyfT5cbiAgICAgICAgICAgICAg5L2O5LqOIDEuMDAg576O5YWD55qE5L2Z6aKdXG4gICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgIDxWaWV3XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFN3aXRjaCBzaXplPVwiJDNcIiBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiB7XG4gICAgICAgICAgICAgIHNldENoZWNrZWQyKGNoZWNrZWQpXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPFN3aXRjaC5UaHVtYiBhbmltYXRpb249XCJib3VuY3lcIiBiYWNrZ3JvdW5kQ29sb3I9e2NoZWNrZWQyID8gJyRibHVlOScgOiAnJGNvbG9yMyd9IC8+XG4gICAgICAgICAgICA8L1N3aXRjaD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIG10PXsyMH0+XG4gICAgICAgICAgPFZpZXc+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCI+XG4gICAgICAgICAgICAgIOa1i+ivlee9kVxuICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiIGNvbG9yPVwiJGFjY2VudDExXCIgbXQ9ezJ9PlxuICAgICAgICAgICAgICDmmL7npLrmtYvor5XnvZHkvZnpop1cbiAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgPFZpZXdcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U3dpdGNoIHNpemU9XCIkM1wiIG9uQ2hlY2tlZENoYW5nZT17KGNoZWNrZWQpID0+IHtcbiAgICAgICAgICAgICAgc2V0Q2hlY2tlZDMoY2hlY2tlZClcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8U3dpdGNoLlRodW1iIGFuaW1hdGlvbj1cImJvdW5jeVwiIGJhY2tncm91bmRDb2xvcj17Y2hlY2tlZDMgPyAnJGJsdWU5JyA6ICckY29sb3IzJ30gLz5cbiAgICAgICAgICAgIDwvU3dpdGNoPlxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgPC9YU3RhY2s+XG4gICAgICA8L1lTdGFjaz5cbiAgICAgIDxMaW5lIC8+XG4gICAgICA8WVN0YWNrIHB4PXsxNn0gbXQ9ezMwfSBtYj17MjB9PlxuICAgICAgICA8VGV4dCBmb250U2l6ZT17MTh9IGZvbnRXZWlnaHQ9XCJib2xkXCI+XG4gICAgICAgICAg6YCJ6aG55Y2hXG4gICAgICAgIDwvVGV4dD5cbiAgICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIiBtdD17MjB9PlxuICAgICAgICAgIDxWaWV3IHdpZHRoPVwiODAlXCI+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCI+XG4gICAgICAgICAgICAgIOeymOaAp+mAiemhueWNoVxuICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiIGNvbG9yPVwiJGFjY2VudDExXCIgbXQ9ezJ9PlxuICAgICAgICAgICAgICDljbPkvb/lupTnlKjnqIvluo/lhbPpl63lkI7vvIzmnIDlkI7pgInmi6nnmoTpgInpobnljaHkuZ/lp4sg57uI5L+d5oyB6YCJ5Lit54q25oCBXG4gICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgIDxWaWV3XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFN3aXRjaCBzaXplPVwiJDNcIiBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiB7XG4gICAgICAgICAgICAgIHNldENoZWNrZWQ0KGNoZWNrZWQpXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPFN3aXRjaC5UaHVtYiBhbmltYXRpb249XCJib3VuY3lcIiBiYWNrZ3JvdW5kQ29sb3I9e2NoZWNrZWQ0ID8gJyRibHVlOScgOiAnJGNvbG9yMyd9IC8+XG4gICAgICAgICAgICA8L1N3aXRjaD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgPC9ZU3RhY2s+XG4gICAgPC9ZU3RhY2s+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJOYXZCYXIiLCJYU3RhY2siLCJZU3RhY2siLCJ1c2VSb3V0ZXIiLCJWaWV3IiwiVGV4dCIsIkltYWdlIiwic3R5bGVkIiwiU3dpdGNoIiwiUmFkaW9Hcm91cCIsIlByZXNzYWJsZSIsInVzZVN0YXRlIiwidXNlVHJhbnNsYXRpb24iLCJjaG9vc2UxSWNvbiIsImFycm93UmlnaHRJY29uIiwiTGluZSIsIndpZHRoIiwiaGVpZ2h0IiwiYmFja2dyb3VuZENvbG9yIiwibXQiLCJBY3RpdmVUZXh0IiwiY29sb3IiLCJtYXJnaW5Cb3R0b20iLCJVbmRlcmxpbmUiLCJwb3NpdGlvbiIsImJvdHRvbSIsImxlZnQiLCJyaWdodCIsIlVuZGVybGluZWJsb2NrIiwiRGlzcGxheVNjcmVlbiIsInJvdXRlciIsInQiLCJjdXJyZW50TGFuZ3VhZ2UiLCJoYW5kbGVMYW5ndWFnZVByZXNzIiwicHVzaCIsImdldExhbmd1YWdlRGlzcGxheVRleHQiLCJjaGVja2VkMSIsInNldENoZWNrZWQxIiwiY2hlY2tlZDIiLCJzZXRDaGVja2VkMiIsImNoZWNrZWQzIiwic2V0Q2hlY2tlZDMiLCJjaGVja2VkNCIsInNldENoZWNrZWQ0IiwidGhlbWVUeXBlIiwic2V0VGhlbWVUeXBlIiwiZGF0YS1hdCIsImRhdGEtaW4iLCJkYXRhLWlzIiwiYmciLCJwYiIsImp1c3RpZnlDb250ZW50IiwiaXRlbXMiLCJwciIsInRpdGxlIiwib25CYWNrIiwiYmFjayIsInNvdXJjZSIsInNyYyIsInB4IiwibWIiLCJmb250U2l6ZSIsImZvbnRXZWlnaHQiLCJvblZhbHVlQ2hhbmdlIiwidmFsdWUiLCJnYXAiLCJJdGVtIiwiaWQiLCJJbmRpY2F0b3IiLCJhbGlnbkl0ZW1zIiwib25QcmVzcyIsIm1yIiwic2l6ZSIsIm9uQ2hlY2tlZENoYW5nZSIsImNoZWNrZWQiLCJUaHVtYiIsImFuaW1hdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/display-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fdisplay.tsx&page=%2Fwallet%2Fdisplay!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);