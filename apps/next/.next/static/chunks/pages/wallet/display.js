/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/display"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fdisplay.tsx&page=%2Fwallet%2Fdisplay!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fdisplay.tsx&page=%2Fwallet%2Fdisplay! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/display\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/display.tsx */ \"./pages/wallet/display.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/display\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRmRpc3BsYXkudHN4JnBhZ2U9JTJGd2FsbGV0JTJGZGlzcGxheSEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw4REFBNEI7QUFDbkQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2ZkNjAiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvZGlzcGxheVwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L2Rpc3BsYXkudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvZGlzcGxheVwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fdisplay.tsx&page=%2Fwallet%2Fdisplay!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/arrowright.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/wallet/arrowright.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/arrowright.f0d2eb68.png\",\"height\":22,\"width\":14,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Farrowright.f0d2eb68.png&w=5&q=70\",\"blurWidth\":5,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYXJyb3dyaWdodC5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9hcnJvd3JpZ2h0LnBuZz8wNDgwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9hcnJvd3JpZ2h0LmYwZDJlYjY4LnBuZ1wiLFwiaGVpZ2h0XCI6MjIsXCJ3aWR0aFwiOjE0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFycm93cmlnaHQuZjBkMmViNjgucG5nJnc9NSZxPTcwXCIsXCJibHVyV2lkdGhcIjo1LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/arrowright.png\n"));

/***/ }),

/***/ "./pages/wallet/display.tsx":
/*!**********************************!*\
  !*** ./pages/wallet/display.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_display_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/display-screen */ \"../../packages/app/features/wallet/display-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_display_screen__WEBPACK_IMPORTED_MODULE_1__.DisplayScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/display.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvZGlzcGxheS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDa0U7QUFFbkQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDZFQUFhQTs7Ozs7QUFDdkI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3dhbGxldC9kaXNwbGF5LnRzeD83ZTc4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgRGlzcGxheVNjcmVlbiB9IGZyb20gJ2FwcC9mZWF0dXJlcy93YWxsZXQvZGlzcGxheS1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8RGlzcGxheVNjcmVlbiAvPlxufSJdLCJuYW1lcyI6WyJEaXNwbGF5U2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/display.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/display-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/display-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DisplayScreen: function() { return /* binding */ DisplayScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/arrowright.png */ \"../../packages/assets/images/wallet/arrowright.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Line = (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Line;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = Underline;\nconst Underlineblock = (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2\n});\n_c3 = Underlineblock;\nfunction DisplayScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { t, currentLanguage } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const handleLanguagePress = ()=>{\n        router.push(\"/wallet/language\");\n    };\n    const getLanguageDisplayText = ()=>{\n        return currentLanguage === \"zh\" ? \"简体中文\" : \"English\";\n    };\n    const [checked1, setChecked1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [checked2, setChecked2] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [checked3, setChecked3] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [checked4, setChecked4] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [themeType, setThemeType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.YStack, {\n        \"data-at\": \"display-screen.tsx:73\",\n        \"data-in\": \"DisplayScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        pb: 100,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                \"data-at\": \"display-screen.tsx:74\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                pr: 16,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.NavBar, {\n                    title: t(\"navigation.display\") || \"显示\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.YStack, {\n                \"data-at\": \"display-screen.tsx:79\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"display-screen.tsx:80\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"外观\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n                        \"data-at\": \"display-screen.tsx:83-85\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"RadioGroup\",\n                        onValueChange: (value)=>{\n                            setThemeType(value);\n                        },\n                        value: themeType,\n                        gap: \"$2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                                \"data-at\": \"display-screen.tsx:86\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"space-between\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                        \"data-at\": \"display-screen.tsx:87\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"display-screen.tsx:88\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 16,\n                                                fontWeight: \"bold\",\n                                                children: \"深色\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"display-screen.tsx:91\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 16,\n                                                fontWeight: \"bold\",\n                                                color: \"$accent11\",\n                                                children: \"始终使用深色模式\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.RadioGroup.Item, {\n                                        value: \"1\",\n                                        id: \"foo-radio-item\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.RadioGroup.Indicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                                \"data-at\": \"display-screen.tsx:101\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                        \"data-at\": \"display-screen.tsx:102\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"display-screen.tsx:103\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 16,\n                                                fontWeight: \"bold\",\n                                                children: \"浅色\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"display-screen.tsx:106\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 16,\n                                                fontWeight: \"bold\",\n                                                color: \"$accent11\",\n                                                children: \"始终使用浅色模式\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.RadioGroup.Item, {\n                                        value: \"2\",\n                                        id: \"foo-radio-item\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.RadioGroup.Indicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                                \"data-at\": \"display-screen.tsx:116\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                        \"data-at\": \"display-screen.tsx:117\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"display-screen.tsx:118\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 16,\n                                                fontWeight: \"bold\",\n                                                children: \"自动\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                \"data-at\": \"display-screen.tsx:121\",\n                                                \"data-in\": \"DisplayScreen\",\n                                                \"data-is\": \"Text\",\n                                                fontSize: 16,\n                                                fontWeight: \"bold\",\n                                                color: \"$accent11\",\n                                                children: \"使用设备的默认模式\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.RadioGroup.Item, {\n                                        value: \"3\",\n                                        id: \"foo-radio-item\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.RadioGroup.Indicator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.YStack, {\n                \"data-at\": \"display-screen.tsx:135\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"display-screen.tsx:136\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: t(\"navigation.language\") || \"语言\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_7__.Pressable, {\n                        onPress: handleLanguagePress,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                            \"data-at\": \"display-screen.tsx:140\",\n                            \"data-in\": \"DisplayScreen\",\n                            \"data-is\": \"XStack\",\n                            justifyContent: \"space-between\",\n                            mt: 20,\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    \"data-at\": \"display-screen.tsx:141\",\n                                    \"data-in\": \"DisplayScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 16,\n                                    fontWeight: \"bold\",\n                                    children: t(\"navigation.language\") || \"语言\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                                    \"data-at\": \"display-screen.tsx:144\",\n                                    \"data-in\": \"DisplayScreen\",\n                                    \"data-is\": \"XStack\",\n                                    items: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                            \"data-at\": \"display-screen.tsx:145\",\n                                            \"data-in\": \"DisplayScreen\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 16,\n                                            fontWeight: 500,\n                                            mr: 10,\n                                            children: getLanguageDisplayText()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                            \"data-at\": \"display-screen.tsx:148\",\n                                            \"data-in\": \"DisplayScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            width: 5,\n                                            height: 9\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.YStack, {\n                \"data-at\": \"display-screen.tsx:154\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"display-screen.tsx:155\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"余额\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                        \"data-at\": \"display-screen.tsx:158\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                \"data-at\": \"display-screen.tsx:159\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 16,\n                                fontWeight: \"bold\",\n                                children: \"本地货币\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                                \"data-at\": \"display-screen.tsx:162\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:163\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: 500,\n                                        mr: 10,\n                                        children: \"USD\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                        \"data-at\": \"display-screen.tsx:166\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                        width: 5,\n                                        height: 9\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                        \"data-at\": \"display-screen.tsx:169\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"display-screen.tsx:170\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:171\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: \"bold\",\n                                        children: \"私密模式\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:174\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"隐藏所有余额\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                \"data-at\": \"display-screen.tsx:178-187\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: checked1 ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: checked1 ? \"#3873F5\" : \"#3A3D44\",\n                                checked: checked1,\n                                onCheckedChange: setChecked1,\n                                style: {\n                                    backgroundColor: checked1 ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                        \"data-at\": \"display-screen.tsx:191\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"display-screen.tsx:192\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:193\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: \"bold\",\n                                        children: \"隐藏小余额\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:196\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"低于 1.00 美元的余额\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                \"data-at\": \"display-screen.tsx:200-209\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: checked2 ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: checked2 ? \"#3873F5\" : \"#3A3D44\",\n                                checked: checked2,\n                                onCheckedChange: setChecked2,\n                                style: {\n                                    backgroundColor: checked2 ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                        \"data-at\": \"display-screen.tsx:213\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"display-screen.tsx:214\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:215\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: \"bold\",\n                                        children: \"测试网\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:218\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"显示测试网余额\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                \"data-at\": \"display-screen.tsx:222-231\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: checked3 ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: checked3 ? \"#3873F5\" : \"#3A3D44\",\n                                checked: checked3,\n                                onCheckedChange: setChecked3,\n                                style: {\n                                    backgroundColor: checked3 ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.YStack, {\n                \"data-at\": \"display-screen.tsx:237\",\n                \"data-in\": \"DisplayScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"display-screen.tsx:238\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"选项卡\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_6__.XStack, {\n                        \"data-at\": \"display-screen.tsx:241\",\n                        \"data-in\": \"DisplayScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"display-screen.tsx:242\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"View\",\n                                width: \"80%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:243\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: \"bold\",\n                                        children: \"粘性选项卡\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"display-screen.tsx:246\",\n                                        \"data-in\": \"DisplayScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 16,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"即使应用程序关闭后，最后选择的选项卡也始 终保持选中状态\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                \"data-at\": \"display-screen.tsx:250-259\",\n                                \"data-in\": \"DisplayScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: checked4 ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: checked4 ? \"#3873F5\" : \"#3A3D44\",\n                                checked: checked4,\n                                onCheckedChange: setChecked4,\n                                style: {\n                                    backgroundColor: checked4 ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/display-screen.tsx\",\n        lineNumber: 54,\n        columnNumber: 10\n    }, this);\n}\n_s(DisplayScreen, \"TkSeYWv8zbQlKc8qB1qnwKcR+rU=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c4 = DisplayScreen;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Line\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"Underline\");\n$RefreshReg$(_c3, \"Underlineblock\");\n$RefreshReg$(_c4, \"DisplayScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/display-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fdisplay.tsx&page=%2Fwallet%2Fdisplay!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);