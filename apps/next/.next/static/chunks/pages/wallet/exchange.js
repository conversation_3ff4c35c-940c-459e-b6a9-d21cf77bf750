/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/exchange"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fexchange.tsx&page=%2Fwallet%2Fexchange!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fexchange.tsx&page=%2Fwallet%2Fexchange! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/exchange\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/exchange.tsx */ \"./pages/wallet/exchange.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/exchange\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRmV4Y2hhbmdlLnRzeCZwYWdlPSUyRndhbGxldCUyRmV4Y2hhbmdlISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLGdFQUE2QjtBQUNwRDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NDZmNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL3dhbGxldC9leGNoYW5nZVwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L2V4Y2hhbmdlLnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvd2FsbGV0L2V4Y2hhbmdlXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fexchange.tsx&page=%2Fwallet%2Fexchange!\n"));

/***/ }),

/***/ "../../packages/assets/images/close.png":
/*!**********************************************!*\
  !*** ../../packages/assets/images/close.png ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/close.41136e88.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fclose.41136e88.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9jbG9zZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsNExBQTRMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2Nsb3NlLnBuZz81NzdkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9jbG9zZS40MTEzNmU4OC5wbmdcIixcImhlaWdodFwiOjI0LFwid2lkdGhcIjoyNCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZjbG9zZS40MTEzNmU4OC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/close.png\n"));

/***/ }),

/***/ "../../packages/assets/images/copy.png":
/*!*********************************************!*\
  !*** ../../packages/assets/images/copy.png ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/copy.7685cfef.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcopy.7685cfef.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9jb3B5LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQywwTEFBMEwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvY29weS5wbmc/NmVkMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvY29weS43Njg1Y2ZlZi5wbmdcIixcImhlaWdodFwiOjMyLFwid2lkdGhcIjozMixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZjb3B5Ljc2ODVjZmVmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/copy.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-1-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-1-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-1-active.e535a13f.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-1-active.e535a13f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0xLWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTEtYWN0aXZlLnBuZz8yNzcwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi0xLWFjdGl2ZS5lNTM1YTEzZi5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0xLWFjdGl2ZS5lNTM1YTEzZi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-1-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-1.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-1.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-1.ea89c0a4.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-1.ea89c0a4.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0xLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tMS5wbmc/MGM4NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tMS5lYTg5YzBhNC5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0xLmVhODljMGE0LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-1.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-2-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-2-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-2-active.5e386890.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-2-active.5e386890.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0yLWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTItYWN0aXZlLnBuZz9iMGFjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi0yLWFjdGl2ZS41ZTM4Njg5MC5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0yLWFjdGl2ZS41ZTM4Njg5MC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-2-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-2.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-2.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-2.ca442dfc.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-2.ca442dfc.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0yLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tMi5wbmc/MmMxZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tMi5jYTQ0MmRmYy5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0yLmNhNDQyZGZjLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-2.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-3-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-3-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-3-active.d4c8b8ea.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-3-active.d4c8b8ea.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0zLWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTMtYWN0aXZlLnBuZz8yZjc4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi0zLWFjdGl2ZS5kNGM4YjhlYS5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0zLWFjdGl2ZS5kNGM4YjhlYS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-3-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-3.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-3.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-3.8beb2569.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-3.8beb2569.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi0zLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tMy5wbmc/YzY2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tMy44YmViMjU2OS5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi0zLjhiZWIyNTY5LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-3.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-4-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-4-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-4-active.4982b8c6.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-4-active.4982b8c6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi00LWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTQtYWN0aXZlLnBuZz81MjgyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi00LWFjdGl2ZS40OTgyYjhjNi5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi00LWFjdGl2ZS40OTgyYjhjNi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-4-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-4.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-4.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-4.88eec0df.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-4.88eec0df.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi00LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tNC5wbmc/YTdiNCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tNC44OGVlYzBkZi5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi00Ljg4ZWVjMGRmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-4.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-5-active.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/fot-icon-5-active.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-5-active.6ccd55e5.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-5-active.6ccd55e5.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi01LWFjdGl2ZS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsb05BQW9OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL2ZvdC1pY29uLTUtYWN0aXZlLnBuZz9iMzM2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mb3QtaWNvbi01LWFjdGl2ZS42Y2NkNTVlNS5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi01LWFjdGl2ZS42Y2NkNTVlNS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-5-active.png\n"));

/***/ }),

/***/ "../../packages/assets/images/fot-icon-5.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/fot-icon-5.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/fot-icon-5.571cfb9d.png\",\"height\":80,\"width\":80,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffot-icon-5.571cfb9d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9mb3QtaWNvbi01LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxzTUFBc00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvZm90LWljb24tNS5wbmc/YWQxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZm90LWljb24tNS41NzFjZmI5ZC5wbmdcIixcImhlaWdodFwiOjgwLFwid2lkdGhcIjo4MCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZmb3QtaWNvbi01LjU3MWNmYjlkLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/fot-icon-5.png\n"));

/***/ }),

/***/ "../../packages/assets/images/main-connect.png":
/*!*****************************************************!*\
  !*** ../../packages/assets/images/main-connect.png ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/main-connect.a1c10843.png\",\"height\":182,\"width\":348,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmain-connect.a1c10843.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":4});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9tYWluLWNvbm5lY3QucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRNQUE0TSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9tYWluLWNvbm5lY3QucG5nPzI1NzQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL21haW4tY29ubmVjdC5hMWMxMDg0My5wbmdcIixcImhlaWdodFwiOjE4MixcIndpZHRoXCI6MzQ4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm1haW4tY29ubmVjdC5hMWMxMDg0My5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6NH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/main-connect.png\n"));

/***/ }),

/***/ "../../packages/assets/images/mint1.png":
/*!**********************************************!*\
  !*** ../../packages/assets/images/mint1.png ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/mint1.b94776a6.png\",\"height\":456,\"width\":686,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmint1.b94776a6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9taW50MS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsOExBQThMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL21pbnQxLnBuZz9kMzE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9taW50MS5iOTQ3NzZhNi5wbmdcIixcImhlaWdodFwiOjQ1NixcIndpZHRoXCI6Njg2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm1pbnQxLmI5NDc3NmE2LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/mint1.png\n"));

/***/ }),

/***/ "../../packages/assets/images/mint2.png":
/*!**********************************************!*\
  !*** ../../packages/assets/images/mint2.png ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/mint2.37d559f3.png\",\"height\":388,\"width\":686,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmint2.37d559f3.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9taW50Mi5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsOExBQThMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL21pbnQyLnBuZz9jZDVjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9taW50Mi4zN2Q1NTlmMy5wbmdcIixcImhlaWdodFwiOjM4OCxcIndpZHRoXCI6Njg2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm1pbnQyLjM3ZDU1OWYzLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/mint2.png\n"));

/***/ }),

/***/ "../../packages/assets/images/search.png":
/*!***********************************************!*\
  !*** ../../packages/assets/images/search.png ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/search.e0802f75.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fsearch.e0802f75.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9zZWFyY2gucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDhMQUE4TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9zZWFyY2gucG5nPzlhY2MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL3NlYXJjaC5lMDgwMmY3NS5wbmdcIixcImhlaWdodFwiOjMyLFwid2lkdGhcIjozMixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZzZWFyY2guZTA4MDJmNzUucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/search.png\n"));

/***/ }),

/***/ "../../packages/assets/images/setting.png":
/*!************************************************!*\
  !*** ../../packages/assets/images/setting.png ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/setting.1f39b21b.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fsetting.1f39b21b.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9zZXR0aW5nLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxnTUFBZ00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvc2V0dGluZy5wbmc/OTJiNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvc2V0dGluZy4xZjM5YjIxYi5wbmdcIixcImhlaWdodFwiOjI0LFwid2lkdGhcIjoyNCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZzZXR0aW5nLjFmMzliMjFiLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/setting.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/eth.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/wallet/eth.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/eth.b241a439.png\",\"height\":64,\"width\":64,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Feth.b241a439.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZXRoLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TEFBd0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2V0aC5wbmc/NjAwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZXRoLmIyNDFhNDM5LnBuZ1wiLFwiaGVpZ2h0XCI6NjQsXCJ3aWR0aFwiOjY0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmV0aC5iMjQxYTQzOS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/eth.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/exchange.png":
/*!********************************************************!*\
  !*** ../../packages/assets/images/wallet/exchange.png ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/exchange.0ebdc08f.png\",\"height\":284,\"width\":346,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fexchange.0ebdc08f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":7});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZXhjaGFuZ2UucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLG9NQUFvTSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZXhjaGFuZ2UucG5nP2I0MWMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2V4Y2hhbmdlLjBlYmRjMDhmLnBuZ1wiLFwiaGVpZ2h0XCI6Mjg0LFwid2lkdGhcIjozNDYsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGZXhjaGFuZ2UuMGViZGMwOGYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjd9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/exchange.png\n"));

/***/ }),

/***/ "./pages/wallet/exchange.tsx":
/*!***********************************!*\
  !*** ./pages/wallet/exchange.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_exchange_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/exchange-screen */ \"../../packages/app/features/wallet/exchange-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_exchange_screen__WEBPACK_IMPORTED_MODULE_1__.ExchangeScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/exchange.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvZXhjaGFuZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRW9FO0FBRXJELFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCwrRUFBY0E7Ozs7O0FBQ3hCO0tBRndCQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy93YWxsZXQvZXhjaGFuZ2UudHN4PzMxZGMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IEV4Y2hhbmdlU2NyZWVuIH0gZnJvbSAnYXBwL2ZlYXR1cmVzL3dhbGxldC9leGNoYW5nZS1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8RXhjaGFuZ2VTY3JlZW4gLz5cbn0iXSwibmFtZXMiOlsiRXhjaGFuZ2VTY3JlZW4iLCJQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/wallet/exchange.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/home/<USER>":
/*!*****************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HomePage: function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/close.png */ \"../../packages/assets/images/close.png\");\n/* harmony import */ var _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/main-connect.png */ \"../../packages/assets/images/main-connect.png\");\n/* harmony import */ var _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/mint1.png */ \"../../packages/assets/images/mint1.png\");\n/* harmony import */ var _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/mint2.png */ \"../../packages/assets/images/mint2.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Underline;\nconst ActiveBlock = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#fff\",\n    borderRadius: 10\n});\n_c1 = ActiveBlock;\nconst Block = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: 22,\n    height: 2,\n    background: \"#262729\",\n    borderRadius: 10\n});\n_c2 = Block;\nconst SwiperContainer = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    position: \"relative\",\n    overflow: \"hidden\",\n    width: \"100%\"\n});\n_c3 = SwiperContainer;\nconst SwiperWrapper = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    display: \"flex\",\n    flexDirection: \"row\",\n    transition: \"transform 0.3s ease\",\n    width: \"400%\" // 4页内容，每页100%\n});\n_c4 = SwiperWrapper;\nconst SwiperSlide = (0,tamagui__WEBPACK_IMPORTED_MODULE_6__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n    width: \"25%\",\n    // 每个slide占25%（因为总宽度是400%）\n    flexShrink: 0\n});\n_c5 = SwiperSlide;\nfunction HomePage(param) {\n    let { pagesMode = false } = param;\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const totalPages = 4;\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n    // 处理触摸开始\n    const handleTouchStart = (e)=>{\n        setTouchEnd(0); // 重置touchEnd\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    // 处理触摸移动\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    // 处理触摸结束\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe && currentPage < totalPages - 1) {\n            setCurrentPage(currentPage + 1);\n        }\n        if (isRightSwipe && currentPage > 0) {\n            setCurrentPage(currentPage - 1);\n        }\n    };\n    // 轮播内容数据\n    const swiperData = [\n        {\n            id: 1,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.1\") || \"获得 4.1% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Base\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.1\") || \"将 Base 上的 USDC 添加到您的钱包，每年可赚取 4.1% 的奖励\"\n        },\n        {\n            id: 2,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"3.8\") || \"获得 3.8% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Ethereum\").replace(\"{token}\", \"USDT\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"3.8\") || \"将 Ethereum 上的 USDT 添加到您的钱包，每年可赚取 3.8% 的奖励\"\n        },\n        {\n            id: 3,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"5.2\") || \"获得 5.2% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Polygon\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"5.2\") || \"将 Polygon 上的 USDC 添加到您的钱包，每年可赚取 5.2% 的奖励\"\n        },\n        {\n            id: 4,\n            title: t(\"home.earnRewards\").replace(\"{rate}\", \"4.5\") || \"获得 4.5% 的奖励\",\n            desc: t(\"home.addToWallet\").replace(\"{network}\", \"Arbitrum\").replace(\"{token}\", \"USDC\") + \"，\" + t(\"home.yearlyEarnings\").replace(\"{rate}\", \"4.5\") || \"将 Arbitrum 上的 USDC 添加到您的钱包，每年可赚取 4.5% 的奖励\"\n        }\n    ];\n    // Trending swaps 测试数据\n    const trendingSwapsData = [\n        {\n            id: 1,\n            name: \"KTA\",\n            price: \"US$0.54\",\n            change: \"15.51%\",\n            changeColor: \"#C7545E\",\n            swaps: 681,\n            buyPercentage: 70,\n            sellPercentage: 30\n        },\n        {\n            id: 2,\n            name: \"DEGEN\",\n            price: \"US$0.012\",\n            change: \"+8.23%\",\n            changeColor: \"#2FAB77\",\n            swaps: 47,\n            buyPercentage: 65,\n            sellPercentage: 35\n        },\n        {\n            id: 3,\n            name: \"HIGHER\",\n            price: \"US$0.089\",\n            change: \"-3.45%\",\n            changeColor: \"#C7545E\",\n            swaps: 82,\n            buyPercentage: 45,\n            sellPercentage: 55\n        },\n        {\n            id: 4,\n            name: \"BALD\",\n            price: \"US$0.0034\",\n            change: \"+12.67%\",\n            changeColor: \"#2FAB77\",\n            swaps: 156,\n            buyPercentage: 78,\n            sellPercentage: 22\n        }\n    ];\n    const handleAction = (action)=>{\n        if (action === \"watchlist\") {\n            router.push(\"/user/myAttention\");\n        }\n    };\n    // 处理外部链接点击\n    const handleExternalLink = (url)=>{\n        window.location.href = url;\n    // Linking.openURL(url).catch(err => console.error('Failed to open URL:', err))\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n        \"data-at\": \"homePage.tsx:183\",\n        \"data-in\": \"HomePage\",\n        \"data-is\": \"YStack\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:185\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:186\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        mt: 20,\n                        flex: 1,\n                        alignContent: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                \"data-at\": \"homePage.tsx:187\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"XStack\",\n                                gap: \"$2\",\n                                children: [\n                                    0,\n                                    1,\n                                    2,\n                                    3\n                                ].map((index)=>index <= currentPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveBlock, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 157,\n                                        columnNumber: 63\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Block, {}, index, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 157,\n                                        columnNumber: 93\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:192\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                style: {\n                                    width: 12,\n                                    height: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperContainer, {\n                        onTouchStart: handleTouchStart,\n                        onTouchMove: handleTouchMove,\n                        onTouchEnd: handleTouchEnd,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperWrapper, {\n                            style: {\n                                transform: \"translateX(-\".concat(currentPage * 25, \"%)\")\n                            },\n                            children: swiperData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SwiperSlide, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"homePage.tsx:206-213\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"XStack\",\n                                        mt: 10,\n                                        flex: 1,\n                                        gap: \"$4\",\n                                        alignContent: \"center\",\n                                        justifyContent: \"space-between\",\n                                        mb: 10,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                \"data-at\": \"homePage.tsx:214\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"Image\",\n                                                source: _assets_images_close_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                                style: {\n                                                    width: 50,\n                                                    height: 50\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                                \"data-at\": \"homePage.tsx:215\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"YStack\",\n                                                flex: 1,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:216\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#fff\",\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:219\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"#8B8F9A\",\n                                                        fontSize: 12,\n                                                        children: item.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 178,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 168,\n                                    columnNumber: 46\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:230\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                onPress: ()=>handleAction(\"watchlist\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:231\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        color: \"white\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        children: \"Watchlist\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"homePage.tsx:234-243\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"XStack\",\n                        bg: \"#141519\",\n                        borderRadius: 10,\n                        p: 10,\n                        mt: 10,\n                        height: 70,\n                        flex: 1,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                \"data-at\": \"homePage.tsx:244\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"YStack\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:245\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"创建“我的关注”\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:248\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        mt: 6,\n                                        children: \"获取价格提醒并了解最新信息\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                \"data-at\": \"homePage.tsx:252\",\n                                \"data-in\": \"HomePage\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_main_connect_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                style: {\n                                    width: 70,\n                                    height: 37\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:256\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                        \"data-at\": \"homePage.tsx:257\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"Text\",\n                        fontSize: 16,\n                        color: \"white\",\n                        fontWeight: \"bold\",\n                        children: \"Trending swaps on Base\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.ScrollView, {\n                        \"data-at\": \"homePage.tsx:260\",\n                        \"data-in\": \"HomePage\",\n                        \"data-is\": \"ScrollView\",\n                        horizontal: true,\n                        showsHorizontalScrollIndicator: false,\n                        mt: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                            \"data-at\": \"homePage.tsx:261\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"XStack\",\n                            gap: \"$3\",\n                            children: trendingSwapsData.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                    \"data-at\": \"homePage.tsx:263\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"View\",\n                                    width: 260,\n                                    height: 180,\n                                    borderRadius: 10,\n                                    bg: \"#141519\",\n                                    px: 14,\n                                    py: 14,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                        \"data-at\": \"homePage.tsx:264\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"YStack\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"homePage.tsx:265\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                mb: \"$4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                                        \"data-at\": \"homePage.tsx:266-269\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Image\",\n                                                        source: {\n                                                            uri: \"\"\n                                                        },\n                                                        style: {\n                                                            width: 28,\n                                                            height: 28,\n                                                            borderRadius: \"50%\",\n                                                            background: \"#2B2B2B\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:270\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        ml: \"$2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                \"data-at\": \"homePage.tsx:271\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"Text\",\n                                                                color: \"white\",\n                                                                fontSize: 14,\n                                                                fontWeight: \"bold\",\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                \"data-at\": \"homePage.tsx:274\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"Text\",\n                                                                color: \"#8B8F9A\",\n                                                                fontSize: 12,\n                                                                fontWeight: 500,\n                                                                mt: 6,\n                                                                children: item.price\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 229,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:278\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        style: {\n                                                            color: item.changeColor\n                                                        },\n                                                        fontSize: 14,\n                                                        fontWeight: \"bold\",\n                                                        ml: \"$4\",\n                                                        children: item.change\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 233,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"homePage.tsx:282\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                alignItems: \"center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:283\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"white\",\n                                                        fontSize: 20,\n                                                        fontWeight: \"bold\",\n                                                        children: item.swaps\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                        \"data-at\": \"homePage.tsx:286\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"Text\",\n                                                        color: \"white\",\n                                                        fontSize: 16,\n                                                        fontWeight: \"bold\",\n                                                        ml: \"$1\",\n                                                        children: \"兑换\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 243,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:289-298\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        bg: \"#282B32\",\n                                                        width: 137,\n                                                        height: 34,\n                                                        borderRadius: 20,\n                                                        ml: \"$5\",\n                                                        onPress: ()=>{\n                                                            router.push(\"/wallet/convert\");\n                                                        },\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                            \"data-at\": \"homePage.tsx:299\",\n                                                            \"data-in\": \"HomePage\",\n                                                            \"data-is\": \"Text\",\n                                                            color: \"white\",\n                                                            text: \"center\",\n                                                            lineHeight: 34,\n                                                            children: \"兑换\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                            lineNumber: 249,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"homePage.tsx:304\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                mt: 20,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:305-310\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        width: Math.round(224 * item.buyPercentage / 100),\n                                                        height: 4,\n                                                        bg: \"#2FAB77\",\n                                                        borderRadius: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                        \"data-at\": \"homePage.tsx:311-316\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"View\",\n                                                        width: Math.round(224 * item.sellPercentage / 100),\n                                                        height: 4,\n                                                        bg: \"#C7545E\",\n                                                        borderRadius: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"homePage.tsx:318\",\n                                                \"data-in\": \"HomePage\",\n                                                \"data-is\": \"XStack\",\n                                                mt: 20,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                        \"data-at\": \"homePage.tsx:319\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"XStack\",\n                                                        flex: 1,\n                                                        alignItems: \"center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                                \"data-at\": \"homePage.tsx:320\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"View\",\n                                                                width: 6,\n                                                                height: 6,\n                                                                bg: \"#2FAB77\",\n                                                                borderRadius: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                \"data-at\": \"homePage.tsx:321\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"Text\",\n                                                                fontSize: 12,\n                                                                color: \"#8B8F9A\",\n                                                                ml: \"$2\",\n                                                                children: [\n                                                                    \"已购买 \",\n                                                                    item.buyPercentage,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                        \"data-at\": \"homePage.tsx:325\",\n                                                        \"data-in\": \"HomePage\",\n                                                        \"data-is\": \"XStack\",\n                                                        flex: 1,\n                                                        alignItems: \"center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                                                \"data-at\": \"homePage.tsx:326\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"View\",\n                                                                width: 6,\n                                                                height: 6,\n                                                                bg: \"#C7545E\",\n                                                                borderRadius: 3\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                                \"data-at\": \"homePage.tsx:327\",\n                                                                \"data-in\": \"HomePage\",\n                                                                \"data-is\": \"Text\",\n                                                                fontSize: 12,\n                                                                color: \"#8B8F9A\",\n                                                                ml: \"$2\",\n                                                                children: [\n                                                                    \"已售出 \",\n                                                                    item.sellPercentage,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.id, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 214,\n                                    columnNumber: 44\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:339\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:340\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    onPress: ()=>handleExternalLink(\"https://bridge.base.org\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                            \"data-at\": \"homePage.tsx:341\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_mint1_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                            style: {\n                                width: \"100%\",\n                                height: 228,\n                                borderRadius: 12\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:342\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 12,\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"Sponsored\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:345\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            fontSize: 16,\n                            color: \"white\",\n                            fontWeight: \"bold\",\n                            mt: 6,\n                            children: \"In-App Bridging is Here\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:348\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 12,\n                            fontWeight: 500,\n                            mt: 6,\n                            children: \"For when you really, really want that one token. Onthat other chain.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                            \"data-at\": \"homePage.tsx:351\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"XStack\",\n                            mt: \"$4\",\n                            alignItems: \"center\",\n                            gap: \"$5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                                    \"data-at\": \"homePage.tsx:352-359\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"View\",\n                                    bg: \"#282B32\",\n                                    width: 137,\n                                    height: 34,\n                                    borderRadius: 20,\n                                    alignItems: \"center\",\n                                    onPress: ()=>handleExternalLink(\"https://bridge.base.org\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                        \"data-at\": \"homePage.tsx:360\",\n                                        \"data-in\": \"HomePage\",\n                                        \"data-is\": \"Text\",\n                                        color: \"white\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        lineHeight: 34,\n                                        children: \"Learn More\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                    \"data-at\": \"homePage.tsx:364\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 12,\n                                    fontWeight: \"bold\",\n                                    children: \"Dismiss\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"homePage.tsx:371\",\n                \"data-in\": \"HomePage\",\n                \"data-is\": \"YStack\",\n                mt: 20,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                    \"data-at\": \"homePage.tsx:372\",\n                    \"data-in\": \"HomePage\",\n                    \"data-is\": \"View\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                            \"data-at\": \"homePage.tsx:373\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            mb: 10,\n                            children: \"Trending onchain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.View, {\n                            \"data-at\": \"homePage.tsx:376\",\n                            \"data-in\": \"HomePage\",\n                            \"data-is\": \"View\",\n                            onPress: ()=>handleExternalLink(\"https://opensea.io/collection/drifters-nft\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Image, {\n                                    \"data-at\": \"homePage.tsx:377\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"Image\",\n                                    source: _assets_images_mint2_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                    style: {\n                                        width: \"100%\",\n                                        height: 228,\n                                        borderRadius: 12\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                    \"data-at\": \"homePage.tsx:378\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 16,\n                                    color: \"white\",\n                                    fontWeight: \"bold\",\n                                    mt: 6,\n                                    children: \"Drifters\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                    \"data-at\": \"homePage.tsx:381\",\n                                    \"data-in\": \"HomePage\",\n                                    \"data-is\": \"Text\",\n                                    color: \"#8B8F9A\",\n                                    fontSize: 12,\n                                    fontWeight: 500,\n                                    mt: 6,\n                                    children: \"Drifters are handcrafted, fully customiz..\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 152,\n        columnNumber: 10\n    }, this);\n}\n_s(HomePage, \"W+W+6QFeS0A5s7b4o2uYY16EQGU=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c6 = HomePage;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveBlock\");\n$RefreshReg$(_c2, \"Block\");\n$RefreshReg$(_c3, \"SwiperContainer\");\n$RefreshReg$(_c4, \"SwiperWrapper\");\n$RefreshReg$(_c5, \"SwiperSlide\");\n$RefreshReg$(_c6, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>"));

/***/ }),

/***/ "../../packages/app/features/home/<USER>":
/*!***************************************************!*\
  !*** ../../packages/app/features/home/<USER>
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FooterNavBar: function() { return /* binding */ FooterNavBar; },\n/* harmony export */   FotIconContainer: function() { return /* binding */ FotIconContainer; },\n/* harmony export */   HomeScreen: function() { return /* binding */ HomeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/search.png */ \"../../packages/assets/images/search.png\");\n/* harmony import */ var _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/copy.png */ \"../../packages/assets/images/copy.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var _assets_images_fot_icon_1_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/fot-icon-1.png */ \"../../packages/assets/images/fot-icon-1.png\");\n/* harmony import */ var _assets_images_fot_icon_2_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/fot-icon-2.png */ \"../../packages/assets/images/fot-icon-2.png\");\n/* harmony import */ var _assets_images_fot_icon_3_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/fot-icon-3.png */ \"../../packages/assets/images/fot-icon-3.png\");\n/* harmony import */ var _assets_images_fot_icon_4_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/fot-icon-4.png */ \"../../packages/assets/images/fot-icon-4.png\");\n/* harmony import */ var _assets_images_fot_icon_5_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/fot-icon-5.png */ \"../../packages/assets/images/fot-icon-5.png\");\n/* harmony import */ var _assets_images_fot_icon_1_active_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../../assets/images/fot-icon-1-active.png */ \"../../packages/assets/images/fot-icon-1-active.png\");\n/* harmony import */ var _assets_images_fot_icon_2_active_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../../assets/images/fot-icon-2-active.png */ \"../../packages/assets/images/fot-icon-2-active.png\");\n/* harmony import */ var _assets_images_fot_icon_3_active_png__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../../assets/images/fot-icon-3-active.png */ \"../../packages/assets/images/fot-icon-3-active.png\");\n/* harmony import */ var _assets_images_fot_icon_4_active_png__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../../assets/images/fot-icon-4-active.png */ \"../../packages/assets/images/fot-icon-4-active.png\");\n/* harmony import */ var _assets_images_fot_icon_5_active_png__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../../assets/images/fot-icon-5-active.png */ \"../../packages/assets/images/fot-icon-5-active.png\");\n/* harmony import */ var _homePage__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./homePage */ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(react_native__WEBPACK_IMPORTED_MODULE_16__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c1 = Underline;\nconst FotIconList = [\n    _assets_images_fot_icon_1_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    _assets_images_fot_icon_2_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    _assets_images_fot_icon_3_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    _assets_images_fot_icon_4_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    _assets_images_fot_icon_5_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n];\nconst FotIconListActive = [\n    _assets_images_fot_icon_1_active_png__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n    _assets_images_fot_icon_2_active_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n    _assets_images_fot_icon_3_active_png__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n    _assets_images_fot_icon_4_active_png__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n    _assets_images_fot_icon_5_active_png__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n];\nconst FotIconContainer = (0,tamagui__WEBPACK_IMPORTED_MODULE_15__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n    maxWidth: 640,\n    margin: \"auto\",\n    alignItems: \"center\",\n    position: \"absolute\",\n    bottom: 0,\n    left: 0,\n    right: 0,\n    height: 80,\n    backgroundColor: \"#131518\",\n    paddingHorizontal: 20,\n    cursor: \"pointer\",\n    zIndex: 100\n});\n_c2 = FotIconContainer;\nconst FooterNavBar = ()=>{\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter)();\n    const pathname = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.usePathname)();\n    const fotLinks = [\n        \"/\",\n        \"/wallet/network\",\n        \"/wallet/buy\",\n        \"/wallet/exchange\",\n        \"/user/home\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FotIconContainer, {\n        justifyContent: \"space-between\",\n        children: FotIconList.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_16__.Pressable, {\n                onPress: ()=>{\n                    router.push(fotLinks[index]);\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                    \"data-at\": \"screen.tsx:92-95\",\n                    \"data-in\": \"FooterNavBar\",\n                    \"data-is\": \"Image\",\n                    source: pathname === fotLinks[index] ? FotIconListActive[index].src : item.src,\n                    style: {\n                        width: 40,\n                        height: 40\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                    lineNumber: 61,\n                    columnNumber: 11\n                }, undefined)\n            }, index, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 58,\n                columnNumber: 41\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 57,\n        columnNumber: 10\n    }, undefined);\n};\n_s(FooterNavBar, \"gA9e4WsoP6a20xDgQgrFkfMP8lc=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.usePathname\n    ];\n});\n_c3 = FooterNavBar;\nfunction HomeScreen(param) {\n    let { pagesMode = false } = param;\n    _s1();\n    const linkTarget = pagesMode ? \"/user\" : \"/user\";\n    const toast = (0,_my_ui__WEBPACK_IMPORTED_MODULE_18__.useToastController)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_19__.useTranslation)();\n    const linkProps = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useLink)({\n        href: \"\".concat(linkTarget, \"/nate\")\n    });\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentAccount, setCurrentAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__.useWalletStore)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWalletId, setSelectedWalletId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        walletStore.init();\n        // 如果没有钱包数据，加载测试数据\n        if (walletStore.walletList.length === 0) {\n            console.log(\"没有钱包数据，加载测试数据\");\n            // const testWalletData = [\n            //   {\n            //     \"mnemonic\": \"below neutral satoshi inhale hotel inhale humor forum visual citizen element seat\",\n            //     \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //     \"accounts\": [\n            //       {\n            //         \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //         \"accountId\": \"61053e03-2dbb-4e39-ac12-173dc80da9a6\",\n            //         \"name\": \"主钱包\",\n            //         \"btc\": {\n            //           \"uid\": \"97cba0ac-5708-44b7-be93-bebbb2939c94\",\n            //           \"address\": \"**********************************\",\n            //           \"privateKey\": \"97ca01b8eb25fc932f875df3acd2b3dc4b7f65090d575d4953f4432fad054bcb\",\n            //           \"accountType\": \"btc\"\n            //         },\n            //         \"eth\": {\n            //           \"uid\": \"c3aff85b-0f2a-40db-abf7-d14d8e6299a8\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18c\",\n            //           \"accountType\": \"eth\"\n            //         },\n            //         \"bsc\": {\n            //           \"uid\": \"7b1e48d5-4c27-48c9-b0c2-70f4a7f4c10e\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18c\",\n            //           \"accountType\": \"bsc\"\n            //         },\n            //         \"solana\": {\n            //           \"uid\": \"e1e06ec5-c947-4a21-b516-0743596c8064\",\n            //           \"address\": \"C5ThiTSKBAhYufh6uqLwzp1SGqWSHtTuE7guRKyVcaXx\",\n            //           \"privateKey\": \"71143f84a0b1a3beb45cd2882bc1f4a13692465e407aac824207fa170ba781c6a495124a84be84ca5b12032ab0b89950ce0fcc570ec5d545221193ea669cab5f\",\n            //           \"accountType\": \"solana\"\n            //         }\n            //       },\n            //       {\n            //         \"walletId\": \"cb1bfc86-1d02-4f3f-b52a-dca1988297c2\",\n            //         \"accountId\": \"61053e03-2dbb-4e39-ac12-173dc80da9a7\",\n            //         \"name\": \"备用钱包\",\n            //         \"btc\": {\n            //           \"uid\": \"97cba0ac-5708-44b7-be93-bebbb2939c95\",\n            //           \"address\": \"**********************************\",\n            //           \"privateKey\": \"97ca01b8eb25fc932f875df3acd2b3dc4b7f65090d575d4953f4432fad054bcd\",\n            //           \"accountType\": \"btc\"\n            //         },\n            //         \"eth\": {\n            //           \"uid\": \"c3aff85b-0f2a-40db-abf7-d14d8e6299a9\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18d\",\n            //           \"accountType\": \"eth\"\n            //         },\n            //         \"bsc\": {\n            //           \"uid\": \"7b1e48d5-4c27-48c9-b0c2-70f4a7f4c10f\",\n            //           \"address\": \"******************************************\",\n            //           \"privateKey\": \"0xbfc40e174072dfddba8028f9f6f72e48d67447d2f375921a44dbb03ee9e2e18d\",\n            //           \"accountType\": \"bsc\"\n            //         },\n            //         \"solana\": {\n            //           \"uid\": \"e1e06ec5-c947-4a21-b516-0743596c8065\",\n            //           \"address\": \"D5ThiTSKBAhYufh6uqLwzp1SGqWSHtTuE7guRKyVcaXy\",\n            //           \"privateKey\": \"71143f84a0b1a3beb45cd2882bc1f4a13692465e407aac824207fa170ba781c6a495124a84be84ca5b12032ab0b89950ce0fcc570ec5d545221193ea669cab60\",\n            //           \"accountType\": \"solana\"\n            //         }\n            //       }\n            //     ]\n            //   }\n            // ]\n            // localStorage.setItem('WALLET_LIST', JSON.stringify(testWalletData))\n            walletStore.init(); // 重新初始化\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (walletStore.currentAccount && walletStore.currentAccount.accountId) {\n            // 从 store 中获取当前账户，并设置显示名称\n            const account = walletStore.currentAccount;\n            // 如果账户有自定义名称，使用自定义名称；否则使用默认格式\n            let accountName = account.name;\n            if (!accountName) {\n                // 查找账户在钱包列表中的索引来生成默认名称\n                let accountIndex = 1;\n                for (const wallet of walletStore.walletList){\n                    const foundIndex = wallet.accounts.findIndex((acc)=>acc.accountId === account.accountId);\n                    if (foundIndex !== -1) {\n                        accountIndex = foundIndex + 1;\n                        break;\n                    }\n                }\n                accountName = (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(accountIndex));\n            }\n            setCurrentAccount({\n                ...account,\n                accountName\n            });\n            setSelectedWalletId(account.accountId);\n        }\n    }, [\n        walletStore.currentAccount,\n        walletStore.walletList,\n        t\n    ]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (action)=>{\n        if (action === \"search\") {\n            router.push(\"/wallet/search\");\n        }\n        if (action === \"copy\") {\n            try {\n                await navigator.clipboard.writeText(currentAccount.eth.address);\n                toast.show(t(\"success.addressCopied\") || \"地址已复制到剪贴板\", {\n                    duration: 2000\n                });\n            } catch (err) {\n                toast.show(t(\"home.copyFailed\") || \"复制失败，请手动复制\", {\n                    duration: 2000\n                });\n            }\n        }\n        if (action === \"setting\") {\n            router.push(\"/wallet/setting\");\n        }\n    }, [\n        currentAccount\n    ]);\n    // 手动刷新余额\n    const handleRefreshBalance = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (isRefreshing) return;\n        setIsRefreshing(true);\n        try {\n            await walletStore.fetchAllBalances();\n            toast.show(t(\"success.balanceRefreshed\") || \"余额已刷新\", {\n                duration: 2000\n            });\n        } catch (error) {\n            toast.show(t(\"error.refreshFailed\") || \"刷新失败，请稍后重试\", {\n                duration: 2000\n            });\n        } finally{\n            setIsRefreshing(false);\n        }\n    }, [\n        isRefreshing,\n        walletStore,\n        toast,\n        t\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n        \"data-at\": \"screen.tsx:263\",\n        \"data-in\": \"HomeScreen\",\n        \"data-is\": \"YStack\",\n        height: \"100vh\",\n        bg: \"#0A0B0D\",\n        width: \"100%\",\n        maxW: 640,\n        margin: \"auto\",\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                \"data-at\": \"screen.tsx:264\",\n                \"data-in\": \"HomeScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                gap: \"$3\",\n                p: \"$4\",\n                overflow: \"scroll\",\n                pb: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                        \"data-at\": \"screen.tsx:265\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                                \"data-at\": \"screen.tsx:266-272\",\n                                \"data-in\": \"HomeScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                space: \"$2\",\n                                onPress: ()=>{\n                                    setIsOpen(true);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar, {\n                                        circular: true,\n                                        size: 24,\n                                        mr: 6,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar.Image, {\n                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(currentAccount.accountId),\n                                                accessibilityLabel: currentAccount.accountId\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 237,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Avatar.Fallback, {\n                                                backgroundColor: \"$blue10\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Text, {\n                                        \"data-at\": \"screen.tsx:280\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#8B8F9A\",\n                                        fontSize: 14,\n                                        children: currentAccount.accountName\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_21__.ChevronDown, {\n                                        color: \"#8B8F9A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                                \"data-at\": \"screen.tsx:285\",\n                                \"data-in\": \"HomeScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                gap: \"$3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:286\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"search\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:287\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_search_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:289\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"copy\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:290\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_copy_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                            style: {\n                                                width: 20,\n                                                height: 20,\n                                                marginHorizontal: 8\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.YStack, {\n                                        \"data-at\": \"screen.tsx:292\",\n                                        \"data-in\": \"HomeScreen\",\n                                        \"data-is\": \"YStack\",\n                                        onPress: ()=>handleAction(\"setting\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_15__.Image, {\n                                            \"data-at\": \"screen.tsx:293\",\n                                            \"data-in\": \"HomeScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                            width: 18,\n                                            style: {\n                                                width: 18,\n                                                height: 18\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.XStack, {\n                        \"data-at\": \"screen.tsx:297\",\n                        \"data-in\": \"HomeScreen\",\n                        \"data-is\": \"XStack\",\n                        gap: \"$2\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.H2, {\n                            \"data-at\": \"screen.tsx:298\",\n                            \"data-in\": \"HomeScreen\",\n                            \"data-is\": \"H2\",\n                            textAlign: \"left\",\n                            color: \"#fff\",\n                            children: [\n                                \"$ \",\n                                walletStore.getCurrentAccountBalance().toFixed(4)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_homePage__WEBPACK_IMPORTED_MODULE_22__.HomePage, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_18__.WalletSheet, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                wallets: walletStore.walletList,\n                selectedId: selectedWalletId,\n                onSelect: (wallet, index)=>{\n                    if (wallet === \"addWallet\") {\n                        router.push(\"/wallet/manager\");\n                    } else {\n                        // 使用 store 的方法设置当前账户\n                        walletStore.setCurrentAccount(wallet);\n                        setSelectedWalletId(wallet.accountId);\n                        // 设置显示名称\n                        const accountName = wallet.name || (t(\"home.addressLabel\") || \"地址{number}\").replace(\"{number}\", String(Number(index) + 1));\n                        setCurrentAccount({\n                            ...wallet,\n                            accountName\n                        });\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 295,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/home/<USER>",\n        lineNumber: 230,\n        columnNumber: 10\n    }, this);\n}\n_s1(HomeScreen, \"Dv8Je33w+g6qB19H0e0sfZ7Y2kY=\", false, function() {\n    return [\n        _my_ui__WEBPACK_IMPORTED_MODULE_18__.useToastController,\n        app_i18n__WEBPACK_IMPORTED_MODULE_19__.useTranslation,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useLink,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_20__.useWalletStore,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_17__.useRouter\n    ];\n});\n_c4 = HomeScreen;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ActiveText\");\n$RefreshReg$(_c1, \"Underline\");\n$RefreshReg$(_c2, \"FotIconContainer\");\n$RefreshReg$(_c3, \"FooterNavBar\");\n$RefreshReg$(_c4, \"HomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/home/<USER>"));

/***/ }),

/***/ "../../packages/app/features/wallet/exchange-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/exchange-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExchangeScreen: function() { return /* binding */ ExchangeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/exchange.png */ \"../../packages/assets/images/wallet/exchange.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_screen__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../home/<USER>/ \"../../packages/app/features/home/<USER>");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ExchangeScreen() {\n    _s();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore)();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    // 获取当前账户\n    const currentAccount = walletStore.currentAccount;\n    // 初始化钱包和交易数据\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // 确保钱包数据先加载\n        walletStore.init();\n        // 然后加载交易数据\n        transactionStore.loadTransactions();\n    }, []);\n    // 监听钱包状态变化，确保钱包加载完成后重新获取交易记录\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentAccount && currentAccount.accountId && !transactionStore.isLoading) {\n            var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n            // 当钱包账户加载完成且交易数据不在加载中时，重新获取交易记录\n            const addresses = [\n                (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n                (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n                (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n                (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n            ].filter(Boolean);\n            // 如果有地址但没有交易记录，可能需要重新加载\n            if (addresses.length > 0) {\n                const currentTransactions = transactionStore.getTransactionsByAddresses(addresses);\n                if (currentTransactions.length === 0 && transactionStore.transactions.length > 0) {\n                    // 有交易数据但当前账户没有匹配的交易，可能是账户刚加载完成\n                    console.log(\"钱包账户已加载，重新获取交易记录\");\n                }\n            }\n        }\n    }, [\n        currentAccount === null || currentAccount === void 0 ? void 0 : currentAccount.accountId,\n        transactionStore.isLoading\n    ]);\n    // 获取当前账户的所有链的交易\n    const transactions = currentAccount && currentAccount.accountId ? (()=>{\n        var _currentAccount_eth, _currentAccount_bsc, _currentAccount_btc, _currentAccount_solana;\n        // 获取所有链的地址\n        const addresses = [\n            (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address,\n            (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address,\n            (_currentAccount_btc = currentAccount.btc) === null || _currentAccount_btc === void 0 ? void 0 : _currentAccount_btc.address,\n            (_currentAccount_solana = currentAccount.solana) === null || _currentAccount_solana === void 0 ? void 0 : _currentAccount_solana.address\n        ].filter(Boolean); // 过滤掉undefined/null值\n        // 使用新的去重方法获取交易记录\n        return transactionStore.getTransactionsByAddresses(addresses);\n    })() : [];\n    // 按日期分组交易\n    const groupTransactionsByDate = (transactions)=>{\n        const groups = {};\n        transactions.forEach((tx)=>{\n            const date = new Date(tx.timestamp);\n            const today = new Date();\n            const yesterday = new Date(today);\n            yesterday.setDate(yesterday.getDate() - 1);\n            let dateKey = \"\";\n            if (date.toDateString() === today.toDateString()) {\n                dateKey = t(\"time.today\") || \"今天\";\n            } else if (date.toDateString() === yesterday.toDateString()) {\n                dateKey = t(\"time.yesterday\") || \"昨天\";\n            } else {\n                dateKey = date.toLocaleDateString();\n            }\n            if (!groups[dateKey]) {\n                groups[dateKey] = [];\n            }\n            groups[dateKey].push((0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.formatTransaction)(tx));\n        });\n        return groups;\n    };\n    const groupedTransactions = groupTransactionsByDate(transactions);\n    // 获取链图标\n    const getChainIcon = (chain)=>{\n        switch(chain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            // 暂时使用同一个图标\n            case \"btc\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n            default:\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src;\n        }\n    };\n    if (transactionStore.isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n            \"data-at\": \"exchange-screen.tsx:115\",\n            \"data-in\": \"ExchangeScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                \"data-at\": \"exchange-screen.tsx:116\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"加载中...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n            lineNumber: 100,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n        \"data-at\": \"exchange-screen.tsx:122\",\n        \"data-in\": \"ExchangeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        flex: 1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                \"data-at\": \"exchange-screen.tsx:123\",\n                \"data-in\": \"ExchangeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 30,\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"exchange-screen.tsx:124\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"XStack\",\n                        pl: 16,\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.H3, {\n                                \"data-at\": \"exchange-screen.tsx:125\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"H3\",\n                                children: \"交易\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:126\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"#4575FF\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                mr: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:132\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        flex: 1,\n                        mb: currentAccount ? 80 : 140,\n                        children: Object.keys(groupedTransactions).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                            \"data-at\": \"exchange-screen.tsx:134\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"YStack\",\n                            flex: 1,\n                            alignItems: \"center\",\n                            mt: 100,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                    \"data-at\": \"exchange-screen.tsx:135\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"XStack\",\n                                    justifyContent: \"center\",\n                                    mt: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                        \"data-at\": \"exchange-screen.tsx:136\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_exchange_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                        width: 173,\n                                        height: 142\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                    \"data-at\": \"exchange-screen.tsx:138\",\n                                    \"data-in\": \"ExchangeScreen\",\n                                    \"data-is\": \"YStack\",\n                                    justifyContent: \"center\",\n                                    mt: 20,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:139\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$white1\",\n                                            fontSize: 16,\n                                            fontWeight: \"bold\",\n                                            textAlign: \"center\",\n                                            mb: 10,\n                                            children: \"还没有交易\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                            \"data-at\": \"exchange-screen.tsx:140\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"$accent11\",\n                                            width: 280,\n                                            textAlign: \"center\",\n                                            margin: \"auto\",\n                                            children: \"一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 60\n                        }, this) : Object.entries(groupedTransactions).map((param)=>{\n                            let [date, txs] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                \"data-at\": \"exchange-screen.tsx:145\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"YStack\",\n                                mt: 20,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                        \"data-at\": \"exchange-screen.tsx:146\",\n                                        \"data-in\": \"ExchangeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mb: 10,\n                                        children: date\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this),\n                                    txs.map((tx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                            \"data-at\": \"exchange-screen.tsx:150\",\n                                            \"data-in\": \"ExchangeScreen\",\n                                            \"data-is\": \"XStack\",\n                                            justifyContent: \"space-between\",\n                                            mt: 25,\n                                            alignItems: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:151\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"XStack\",\n                                                    alignItems: \"center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:152\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            position: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Image, {\n                                                                \"data-at\": \"exchange-screen.tsx:153\",\n                                                                \"data-in\": \"ExchangeScreen\",\n                                                                \"data-is\": \"Image\",\n                                                                source: getChainIcon(tx.chain),\n                                                                width: 38,\n                                                                height: 38\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.View, {\n                                                            \"data-at\": \"exchange-screen.tsx:155\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"View\",\n                                                            ml: 10,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:156\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 14,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"white\",\n                                                                    children: (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.getTypeDisplayText)(tx.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                                    \"data-at\": \"exchange-screen.tsx:159\",\n                                                                    \"data-in\": \"ExchangeScreen\",\n                                                                    \"data-is\": \"Text\",\n                                                                    fontSize: 12,\n                                                                    fontWeight: \"bold\",\n                                                                    color: \"$accent11\",\n                                                                    mt: 2,\n                                                                    children: tx.displayAddress\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                                                    \"data-at\": \"exchange-screen.tsx:164\",\n                                                    \"data-in\": \"ExchangeScreen\",\n                                                    \"data-is\": \"YStack\",\n                                                    flexDirection: \"column\",\n                                                    alignItems: \"flex-end\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:165\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 14,\n                                                            fontWeight: \"bold\",\n                                                            color: \"white\",\n                                                            children: tx.displayAmount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:168\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 12,\n                                                            fontWeight: \"bold\",\n                                                            color: \"$accent11\",\n                                                            mt: 2,\n                                                            children: tx.displayTime\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        tx.txHash ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                                            \"data-at\": \"exchange-screen.tsx:172\",\n                                                            \"data-in\": \"ExchangeScreen\",\n                                                            \"data-is\": \"Text\",\n                                                            fontSize: 10,\n                                                            color: \"$accent11\",\n                                                            mt: 1,\n                                                            children: [\n                                                                tx.txHash.slice(0, 8),\n                                                                \"...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 36\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, tx.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 32\n                                        }, this))\n                                ]\n                            }, date, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 82\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    !currentAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                        \"data-at\": \"exchange-screen.tsx:186\",\n                        \"data-in\": \"ExchangeScreen\",\n                        \"data-is\": \"YStack\",\n                        position: \"absolute\",\n                        bottom: 90,\n                        left: 16,\n                        right: 16,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                            \"data-at\": \"exchange-screen.tsx:187\",\n                            \"data-in\": \"ExchangeScreen\",\n                            \"data-is\": \"Button\",\n                            rounded: 30,\n                            width: \"100%\",\n                            bg: \"$accent11\",\n                            onPress: ()=>router.push(\"/wallet/buyCoin\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                                \"data-at\": \"exchange-screen.tsx:188\",\n                                \"data-in\": \"ExchangeScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"$white1\",\n                                children: \"将加密货币添加到您的钱包\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 29\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_home_screen__WEBPACK_IMPORTED_MODULE_10__.FooterNavBar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/exchange-screen.tsx\",\n        lineNumber: 104,\n        columnNumber: 10\n    }, this);\n}\n_s(ExchangeScreen, \"bI91Hn7j8cjELTH2QtyxJakv90I=\", false, function() {\n    return [\n        app_i18n__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_5__.useTransactionStore,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_6__.useWalletStore,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = ExchangeScreen;\nvar _c;\n$RefreshReg$(_c, \"ExchangeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL3dhbGxldC9leGNoYW5nZS1zY3JlZW4udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBbUQ7QUFDUjtBQUMwQjtBQUVwQztBQUNZO0FBQzJEO0FBQ2pEO0FBQ2Q7QUFDa0I7QUFDZDtBQUV0QyxTQUFTaUI7O0lBQ2QsTUFBTSxFQUFFQyxDQUFBQSxFQUFHLEdBQUdKLHdEQUFjQTtJQUM1QixNQUFNSyxtQkFBbUJULGdGQUFtQkE7SUFDNUMsTUFBTVUsY0FBY1Asc0VBQWNBO0lBQ2xDLE1BQU1RLFNBQVNMLDREQUFTQTtJQUV4QjtJQUNBLE1BQU1NLGlCQUFpQkYsWUFBWUUsY0FBYztJQUVqRDtJQUNBZCxnREFBU0EsQ0FBQztRQUNSO1FBQ0FZLFlBQVlHLElBQUk7UUFDaEI7UUFDQUosaUJBQWlCSyxnQkFBZ0I7SUFDbkMsR0FBRyxFQUFFO0lBRUw7SUFDQWhCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWMsa0JBQWtCQSxlQUFlRyxTQUFTLElBQUksQ0FBQ04saUJBQWlCTyxTQUFTLEVBQUU7Z0JBRzNFSixxQkFDQUEscUJBQ0FBLHFCQUNBQTtZQUxGO1lBQ0EsTUFBTUssWUFBWTtpQkFDaEJMLHNCQUFBQSxlQUFlTSxHQUFHLGNBQWxCTiwwQ0FBQUEsb0JBQW9CTyxPQUFPO2lCQUMzQlAsc0JBQUFBLGVBQWVRLEdBQUcsY0FBbEJSLDBDQUFBQSxvQkFBb0JPLE9BQU87aUJBQzNCUCxzQkFBQUEsZUFBZVMsR0FBRyxjQUFsQlQsMENBQUFBLG9CQUFvQk8sT0FBTztpQkFDM0JQLHlCQUFBQSxlQUFlVSxNQUFNLGNBQXJCViw2Q0FBQUEsdUJBQXVCTyxPQUFPO2FBQy9CLENBQUNJLE1BQU0sQ0FBQ0M7WUFFVDtZQUNBLElBQUlQLFVBQVVRLE1BQU0sR0FBRyxHQUFHO2dCQUN4QixNQUFNQyxzQkFBc0JqQixpQkFBaUJrQiwwQkFBMEIsQ0FBQ1Y7Z0JBQ3hFLElBQUlTLG9CQUFvQkQsTUFBTSxLQUFLLEtBQUtoQixpQkFBaUJtQixZQUFZLENBQUNILE1BQU0sR0FBRyxHQUFHO29CQUNoRjtvQkFDQUksUUFBUUMsR0FBRyxDQUFDO2dCQUNkO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFBQ2xCLDJCQUFBQSxxQ0FBQUEsZUFBZ0JHLFNBQVM7UUFBRU4saUJBQWlCTyxTQUFTO0tBQUM7SUFFMUQ7SUFDQSxNQUFNWSxlQUFlaEIsa0JBQWtCQSxlQUFlRyxTQUFTLEdBQzNELENBQUM7WUFHQ0gscUJBQ0FBLHFCQUNBQSxxQkFDQUE7UUFMRjtRQUNBLE1BQU1LLFlBQVk7YUFDaEJMLHNCQUFBQSxlQUFlTSxHQUFHLGNBQWxCTiwwQ0FBQUEsb0JBQW9CTyxPQUFPO2FBQzNCUCxzQkFBQUEsZUFBZVEsR0FBRyxjQUFsQlIsMENBQUFBLG9CQUFvQk8sT0FBTzthQUMzQlAsc0JBQUFBLGVBQWVTLEdBQUcsY0FBbEJULDBDQUFBQSxvQkFBb0JPLE9BQU87YUFDM0JQLHlCQUFBQSxlQUFlVSxNQUFNLGNBQXJCViw2Q0FBQUEsdUJBQXVCTyxPQUFPO1NBQy9CLENBQUNJLE1BQU0sQ0FBQ0MsVUFBUztRQUVsQjtRQUNBLE9BQU9mLGlCQUFpQmtCLDBCQUEwQixDQUFDVjtJQUNyRCxPQUNFLEVBQUU7SUFFTjtJQUNBLE1BQU1jLDBCQUEwQkEsQ0FBQ0g7UUFDL0IsTUFBTUksU0FBbUMsQ0FBQztRQUUxQ0osYUFBYUssT0FBTyxDQUFFQyxDQUFBQTtZQUNwQixNQUFNQyxPQUFPLElBQUlDLEtBQUtGLEdBQUdHLFNBQVM7WUFDbEMsTUFBTUMsUUFBUSxJQUFJRjtZQUNsQixNQUFNRyxZQUFZLElBQUlILEtBQUtFO1lBQzNCQyxVQUFVQyxPQUFPLENBQUNELFVBQVVFLE9BQU8sS0FBSztZQUV4QyxJQUFJQyxVQUFVO1lBQ2QsSUFBSVAsS0FBS1EsWUFBWSxPQUFPTCxNQUFNSyxZQUFZLElBQUk7Z0JBQ2hERCxVQUFVbEMsRUFBRSxpQkFBaUI7WUFDL0IsT0FBTyxJQUFJMkIsS0FBS1EsWUFBWSxPQUFPSixVQUFVSSxZQUFZLElBQUk7Z0JBQzNERCxVQUFVbEMsRUFBRSxxQkFBcUI7WUFDbkMsT0FBTztnQkFDTGtDLFVBQVVQLEtBQUtTLGtCQUFrQjtZQUNuQztZQUVBLElBQUksQ0FBQ1osTUFBTSxDQUFDVSxRQUFRLEVBQUU7Z0JBQ3BCVixNQUFNLENBQUNVLFFBQVEsR0FBRyxFQUFFO1lBQ3RCO1lBQ0FWLE1BQU0sQ0FBQ1UsUUFBUSxDQUFDRyxJQUFJLENBQUM1Qyw4RUFBaUJBLENBQUNpQztRQUN6QztRQUVBLE9BQU9GO0lBQ1Q7SUFFQSxNQUFNYyxzQkFBc0JmLHdCQUF3Qkg7SUFFcEQ7SUFDQSxNQUFNbUIsZUFBZUEsQ0FBQ0M7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILE9BQU8zQyx5RUFBVztZQUNwQixLQUFLO2dCQUNILE9BQU9BLHlFQUFXO1lBQUM7WUFDckIsS0FBSztnQkFDSCxPQUFPQSx5RUFBVztZQUNwQixLQUFLO2dCQUNILE9BQU9BLHlFQUFXO1lBQ3BCO2dCQUNFLE9BQU9BLHlFQUFXO1FBQ3RCO0lBQ0Y7SUFDQSxJQUFJSSxpQkFBaUJPLFNBQVMsRUFBRTtRQUM5QixxQkFDRSw4REFBQ3ZCLDBDQUFNQTtZQUFBeUQsV0FBQTtZQUFBQyxXQUFBO1lBQUFDLFdBQUE7WUFBQ0MsSUFBRztZQUFjQyxNQUFNO1lBQUdDLGdCQUFlO1lBQVNDLFlBQVc7c0JBQ25FLDRFQUFDN0QseUNBQUlBO2dCQUFBdUQsV0FBQTtnQkFBQUMsV0FBQTtnQkFBQUMsV0FBQTtnQkFBQ0ssT0FBTTswQkFBUTs7Ozs7Ozs7Ozs7SUFHMUI7SUFFQSxxQkFDRSw4REFBQ2hFLDBDQUFNQTtRQUFBeUQsV0FBQTtRQUFBQyxXQUFBO1FBQUFDLFdBQUE7UUFBQ0MsSUFBRztRQUFjQyxNQUFNOzswQkFDN0IsOERBQUM3RCwwQ0FBTUE7Z0JBQUF5RCxXQUFBO2dCQUFBQyxXQUFBO2dCQUFBQyxXQUFBO2dCQUFDTSxJQUFJO2dCQUFJQyxJQUFJO2dCQUFJTCxNQUFNOztrQ0FDNUIsOERBQUM5RCwwQ0FBTUE7d0JBQUEwRCxXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDUSxJQUFJO3dCQUFJTCxnQkFBZTs7MENBQzdCLDhEQUFDaEUsc0NBQUVBO2dDQUFBMkQsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTswQ0FBQzs7Ozs7OzBDQUNKLDhEQUFDekQseUNBQUlBO2dDQUFBdUQsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ0ssT0FBTTtnQ0FBVUksVUFBVTtnQ0FBSUMsWUFBWTtnQ0FBS0MsSUFBSTs7Ozs7Ozs7Ozs7O2tDQU0zRCw4REFBQ3RFLDBDQUFNQTt3QkFBQXlELFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNFLE1BQU07d0JBQUdVLElBQUlwRCxpQkFBaUIsS0FBSztrQ0FDeENxRCxPQUFPQyxJQUFJLENBQUNwQixxQkFBcUJyQixNQUFNLEtBQUssa0JBQzNDLDhEQUFDaEMsMENBQU1BOzRCQUFBeUQsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQ0UsTUFBTTs0QkFBR0UsWUFBVzs0QkFBU1csSUFBSTs7OENBQ3ZDLDhEQUFDM0UsMENBQU1BO29DQUFBMEQsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQ0csZ0JBQWU7b0NBQVNZLElBQUk7OENBQ2xDLDRFQUFDdkUsMENBQUtBO3dDQUFBc0QsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ2dCLFFBQVF2RSw4RUFBZ0I7d0NBQUV3RSxPQUFPO3dDQUFLQyxRQUFROzs7Ozs7Ozs7Ozs4Q0FFdkQsOERBQUM3RSwwQ0FBTUE7b0NBQUF5RCxXQUFBO29DQUFBQyxXQUFBO29DQUFBQyxXQUFBO29DQUFDRyxnQkFBZTtvQ0FBU1ksSUFBSTs7c0RBQ2xDLDhEQUFDeEUseUNBQUlBOzRDQUFBdUQsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQUMsV0FBQTs0Q0FBQ0ssT0FBTTs0Q0FBVUksVUFBVTs0Q0FBSUMsWUFBVzs0Q0FBT1MsV0FBVTs0Q0FBU1AsSUFBSTtzREFBSTs7Ozs7O3NEQUNqRiw4REFBQ3JFLHlDQUFJQTs0Q0FBQXVELFdBQUE7NENBQUFDLFdBQUE7NENBQUFDLFdBQUE7NENBQUNLLE9BQU07NENBQVlZLE9BQU87NENBQUtFLFdBQVU7NENBQVNDLFFBQU87c0RBQU87Ozs7Ozs7Ozs7Ozs7Ozs7O21DQUl6RVAsT0FBT1EsT0FBTyxDQUFDM0IscUJBQXFCNEIsR0FBRyxDQUFDO2dDQUFDLENBQUN2QyxNQUFNd0MsSUFBSTtpREFDbEQsOERBQUNsRiwwQ0FBTUE7Z0NBQUF5RCxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFZZSxJQUFJOztrREFDckIsOERBQUN4RSx5Q0FBSUE7d0NBQUF1RCxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDUyxVQUFVO3dDQUFJQyxZQUFXO3dDQUFPTCxPQUFNO3dDQUFZTyxJQUFJO2tEQUN6RDdCOzs7Ozs7b0NBRUZ3QyxJQUFJRCxHQUFHLENBQUV4QyxDQUFBQSxtQkFDUiw4REFBQzFDLDBDQUFNQTs0Q0FBQTBELFdBQUE7NENBQUFDLFdBQUE7NENBQUFDLFdBQUE7NENBQWFHLGdCQUFlOzRDQUFnQlksSUFBSTs0Q0FBSVgsWUFBVzs7OERBQ3BFLDhEQUFDaEUsMENBQU1BO29EQUFBMEQsV0FBQTtvREFBQUMsV0FBQTtvREFBQUMsV0FBQTtvREFBQ0ksWUFBVzs7c0VBQ2pCLDhEQUFDOUQseUNBQUlBOzREQUFBd0QsV0FBQTs0REFBQUMsV0FBQTs0REFBQUMsV0FBQTs0REFBQ3dCLFVBQVM7c0VBQ2IsNEVBQUNoRiwwQ0FBS0E7Z0VBQUFzRCxXQUFBO2dFQUFBQyxXQUFBO2dFQUFBQyxXQUFBO2dFQUFDZ0IsUUFBUXJCLGFBQWFiLEdBQUdjLEtBQUs7Z0VBQUdxQixPQUFPO2dFQUFJQyxRQUFROzs7Ozs7Ozs7OztzRUFFNUQsOERBQUM1RSx5Q0FBSUE7NERBQUF3RCxXQUFBOzREQUFBQyxXQUFBOzREQUFBQyxXQUFBOzREQUFDeUIsSUFBSTs7OEVBQ1IsOERBQUNsRix5Q0FBSUE7b0VBQUF1RCxXQUFBO29FQUFBQyxXQUFBO29FQUFBQyxXQUFBO29FQUFDUyxVQUFVO29FQUFJQyxZQUFXO29FQUFPTCxPQUFNOzhFQUN6Q3ZELCtFQUFrQkEsQ0FBQ2dDLEdBQUc0QyxJQUFJOzs7Ozs7OEVBRTdCLDhEQUFDbkYseUNBQUlBO29FQUFBdUQsV0FBQTtvRUFBQUMsV0FBQTtvRUFBQUMsV0FBQTtvRUFBQ1MsVUFBVTtvRUFBSUMsWUFBVztvRUFBT0wsT0FBTTtvRUFBWVUsSUFBSTs4RUFDekRqQyxHQUFHNkMsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUl4Qiw4REFBQ3RGLDBDQUFNQTtvREFBQXlELFdBQUE7b0RBQUFDLFdBQUE7b0RBQUFDLFdBQUE7b0RBQUM0QixlQUFjO29EQUFTeEIsWUFBVzs7c0VBQ3hDLDhEQUFDN0QseUNBQUlBOzREQUFBdUQsV0FBQTs0REFBQUMsV0FBQTs0REFBQUMsV0FBQTs0REFBQ1MsVUFBVTs0REFBSUMsWUFBVzs0REFBT0wsT0FBTTtzRUFDekN2QixHQUFHK0MsYUFBYTs7Ozs7O3NFQUVuQiw4REFBQ3RGLHlDQUFJQTs0REFBQXVELFdBQUE7NERBQUFDLFdBQUE7NERBQUFDLFdBQUE7NERBQUNTLFVBQVU7NERBQUlDLFlBQVc7NERBQU9MLE9BQU07NERBQVlVLElBQUk7c0VBQ3pEakMsR0FBR2dELFdBQVc7Ozs7Ozt3REFFaEJoRCxHQUFHaUQsTUFBTSxpQkFDUiw4REFBQ3hGLHlDQUFJQTs0REFBQXVELFdBQUE7NERBQUFDLFdBQUE7NERBQUFDLFdBQUE7NERBQUNTLFVBQVU7NERBQUlKLE9BQU07NERBQVlVLElBQUk7O2dFQUN2Q2pDLEdBQUdpRCxNQUFNLENBQUNDLEtBQUssQ0FBQyxHQUFHO2dFQUFHOzs7Ozs7bUVBRXZCOzs7Ozs7OzsyQ0F6QktsRCxHQUFHbUQsRUFBRTs7Ozs7OytCQUxUbEQ7Ozs7Ozs7Ozs7O29CQXdDbEIsQ0FBQ3ZCLGdDQUNBLDhEQUFDbkIsMENBQU1BO3dCQUFBeUQsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ3dCLFVBQVM7d0JBQVdVLFFBQVE7d0JBQUlDLE1BQU07d0JBQUlDLE9BQU87a0NBQ3ZELDRFQUFDbEcsMENBQU1BOzRCQUFBNEQsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQUMsV0FBQTs0QkFBQ3FDLFNBQVM7NEJBQUlwQixPQUFNOzRCQUFPaEIsSUFBRzs0QkFBWXFDLFNBQVMsSUFBTS9FLE9BQU9rQyxJQUFJLENBQUM7c0NBQzFFLDRFQUFDbEQseUNBQUlBO2dDQUFBdUQsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ0ssT0FBTTswQ0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNOUIsOERBQUMxRCx1REFBWUE7Ozs7Ozs7Ozs7O0FBR25CO0dBeExnQlE7O1FBQ0FILG9EQUFjQTtRQUNISiw0RUFBbUJBO1FBQ3hCRyxrRUFBY0E7UUFDbkJHLHdEQUFTQTs7O0tBSlZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hcHAvZmVhdHVyZXMvd2FsbGV0L2V4Y2hhbmdlLXNjcmVlbi50c3g/NmUxMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCdXR0b24sIEgzLCBYU3RhY2ssIFlTdGFjayB9IGZyb20gJ0BteS91aSdcbmltcG9ydCB7IFZpZXcsIFRleHQsIEltYWdlIH0gZnJvbSAndGFtYWd1aSdcbmltcG9ydCBleGNlaGFuZ0ljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy93YWxsZXQvZXhjaGFuZ2UucG5nJ1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEZvb3Rlck5hdkJhciB9IGZyb20gJy4uL2hvbWUvc2NyZWVuJ1xuaW1wb3J0IHsgdXNlVHJhbnNhY3Rpb25TdG9yZSwgZm9ybWF0VHJhbnNhY3Rpb24sIGdldFR5cGVEaXNwbGF5VGV4dCB9IGZyb20gJ2FwcC9zdG9yZXMvdHJhbnNhY3Rpb25TdG9yZSdcbmltcG9ydCB7IHVzZVdhbGxldFN0b3JlIH0gZnJvbSAnYXBwL3N0b3Jlcy93YWxsZXRTdG9yZSdcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAnYXBwL2kxOG4nXG5pbXBvcnQgZXRoSWNvbiBmcm9tICcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL3dhbGxldC9ldGgucG5nJ1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnc29saXRvL25hdmlnYXRpb24nXG5cbmV4cG9ydCBmdW5jdGlvbiBFeGNoYW5nZVNjcmVlbigpIHtcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpXG4gIGNvbnN0IHRyYW5zYWN0aW9uU3RvcmUgPSB1c2VUcmFuc2FjdGlvblN0b3JlKClcbiAgY29uc3Qgd2FsbGV0U3RvcmUgPSB1c2VXYWxsZXRTdG9yZSgpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG5cbiAgLy8g6I635Y+W5b2T5YmN6LSm5oi3XG4gIGNvbnN0IGN1cnJlbnRBY2NvdW50ID0gd2FsbGV0U3RvcmUuY3VycmVudEFjY291bnRcblxuICAvLyDliJ3lp4vljJbpkrHljIXlkozkuqTmmJPmlbDmja5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyDnoa7kv53pkrHljIXmlbDmja7lhYjliqDovb1cbiAgICB3YWxsZXRTdG9yZS5pbml0KClcbiAgICAvLyDnhLblkI7liqDovb3kuqTmmJPmlbDmja5cbiAgICB0cmFuc2FjdGlvblN0b3JlLmxvYWRUcmFuc2FjdGlvbnMoKVxuICB9LCBbXSlcblxuICAvLyDnm5HlkKzpkrHljIXnirbmgIHlj5jljJbvvIznoa7kv53pkrHljIXliqDovb3lrozmiJDlkI7ph43mlrDojrflj5bkuqTmmJPorrDlvZVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoY3VycmVudEFjY291bnQgJiYgY3VycmVudEFjY291bnQuYWNjb3VudElkICYmICF0cmFuc2FjdGlvblN0b3JlLmlzTG9hZGluZykge1xuICAgICAgLy8g5b2T6ZKx5YyF6LSm5oi35Yqg6L295a6M5oiQ5LiU5Lqk5piT5pWw5o2u5LiN5Zyo5Yqg6L295Lit5pe277yM6YeN5paw6I635Y+W5Lqk5piT6K6w5b2VXG4gICAgICBjb25zdCBhZGRyZXNzZXMgPSBbXG4gICAgICAgIGN1cnJlbnRBY2NvdW50LmV0aD8uYWRkcmVzcyxcbiAgICAgICAgY3VycmVudEFjY291bnQuYnNjPy5hZGRyZXNzLFxuICAgICAgICBjdXJyZW50QWNjb3VudC5idGM/LmFkZHJlc3MsXG4gICAgICAgIGN1cnJlbnRBY2NvdW50LnNvbGFuYT8uYWRkcmVzcyxcbiAgICAgIF0uZmlsdGVyKEJvb2xlYW4pXG5cbiAgICAgIC8vIOWmguaenOacieWcsOWdgOS9huayoeacieS6pOaYk+iusOW9le+8jOWPr+iDvemcgOimgemHjeaWsOWKoOi9vVxuICAgICAgaWYgKGFkZHJlc3Nlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnN0IGN1cnJlbnRUcmFuc2FjdGlvbnMgPSB0cmFuc2FjdGlvblN0b3JlLmdldFRyYW5zYWN0aW9uc0J5QWRkcmVzc2VzKGFkZHJlc3NlcylcbiAgICAgICAgaWYgKGN1cnJlbnRUcmFuc2FjdGlvbnMubGVuZ3RoID09PSAwICYmIHRyYW5zYWN0aW9uU3RvcmUudHJhbnNhY3Rpb25zLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAvLyDmnInkuqTmmJPmlbDmja7kvYblvZPliY3otKbmiLfmsqHmnInljLnphY3nmoTkuqTmmJPvvIzlj6/og73mmK/otKbmiLfliJrliqDovb3lrozmiJBcbiAgICAgICAgICBjb25zb2xlLmxvZygn6ZKx5YyF6LSm5oi35bey5Yqg6L2977yM6YeN5paw6I635Y+W5Lqk5piT6K6w5b2VJylcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSwgW2N1cnJlbnRBY2NvdW50Py5hY2NvdW50SWQsIHRyYW5zYWN0aW9uU3RvcmUuaXNMb2FkaW5nXSlcblxuICAvLyDojrflj5blvZPliY3otKbmiLfnmoTmiYDmnInpk77nmoTkuqTmmJNcbiAgY29uc3QgdHJhbnNhY3Rpb25zID0gY3VycmVudEFjY291bnQgJiYgY3VycmVudEFjY291bnQuYWNjb3VudElkXG4gICAgPyAoKCkgPT4ge1xuICAgICAgLy8g6I635Y+W5omA5pyJ6ZO+55qE5Zyw5Z2AXG4gICAgICBjb25zdCBhZGRyZXNzZXMgPSBbXG4gICAgICAgIGN1cnJlbnRBY2NvdW50LmV0aD8uYWRkcmVzcyxcbiAgICAgICAgY3VycmVudEFjY291bnQuYnNjPy5hZGRyZXNzLFxuICAgICAgICBjdXJyZW50QWNjb3VudC5idGM/LmFkZHJlc3MsXG4gICAgICAgIGN1cnJlbnRBY2NvdW50LnNvbGFuYT8uYWRkcmVzcyxcbiAgICAgIF0uZmlsdGVyKEJvb2xlYW4pIC8vIOi/h+a7pOaOiXVuZGVmaW5lZC9udWxs5YC8XG5cbiAgICAgIC8vIOS9v+eUqOaWsOeahOWOu+mHjeaWueazleiOt+WPluS6pOaYk+iusOW9lVxuICAgICAgcmV0dXJuIHRyYW5zYWN0aW9uU3RvcmUuZ2V0VHJhbnNhY3Rpb25zQnlBZGRyZXNzZXMoYWRkcmVzc2VzKVxuICAgIH0pKClcbiAgICA6IFtdXG5cbiAgLy8g5oyJ5pel5pyf5YiG57uE5Lqk5piTXG4gIGNvbnN0IGdyb3VwVHJhbnNhY3Rpb25zQnlEYXRlID0gKHRyYW5zYWN0aW9uczogYW55W10pID0+IHtcbiAgICBjb25zdCBncm91cHM6IHsgW2tleTogc3RyaW5nXTogYW55W10gfSA9IHt9XG5cbiAgICB0cmFuc2FjdGlvbnMuZm9yRWFjaCgodHgpID0+IHtcbiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0eC50aW1lc3RhbXApXG4gICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKClcbiAgICAgIGNvbnN0IHllc3RlcmRheSA9IG5ldyBEYXRlKHRvZGF5KVxuICAgICAgeWVzdGVyZGF5LnNldERhdGUoeWVzdGVyZGF5LmdldERhdGUoKSAtIDEpXG5cbiAgICAgIGxldCBkYXRlS2V5ID0gJydcbiAgICAgIGlmIChkYXRlLnRvRGF0ZVN0cmluZygpID09PSB0b2RheS50b0RhdGVTdHJpbmcoKSkge1xuICAgICAgICBkYXRlS2V5ID0gdCgndGltZS50b2RheScpIHx8ICfku4rlpKknXG4gICAgICB9IGVsc2UgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHllc3RlcmRheS50b0RhdGVTdHJpbmcoKSkge1xuICAgICAgICBkYXRlS2V5ID0gdCgndGltZS55ZXN0ZXJkYXknKSB8fCAn5pio5aSpJ1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZGF0ZUtleSA9IGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKClcbiAgICAgIH1cblxuICAgICAgaWYgKCFncm91cHNbZGF0ZUtleV0pIHtcbiAgICAgICAgZ3JvdXBzW2RhdGVLZXldID0gW11cbiAgICAgIH1cbiAgICAgIGdyb3Vwc1tkYXRlS2V5XS5wdXNoKGZvcm1hdFRyYW5zYWN0aW9uKHR4KSlcbiAgICB9KVxuXG4gICAgcmV0dXJuIGdyb3Vwc1xuICB9XG5cbiAgY29uc3QgZ3JvdXBlZFRyYW5zYWN0aW9ucyA9IGdyb3VwVHJhbnNhY3Rpb25zQnlEYXRlKHRyYW5zYWN0aW9ucylcblxuICAvLyDojrflj5bpk77lm77moIdcbiAgY29uc3QgZ2V0Q2hhaW5JY29uID0gKGNoYWluOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKGNoYWluKSB7XG4gICAgICBjYXNlICdldGgnOlxuICAgICAgICByZXR1cm4gZXRoSWNvbi5zcmNcbiAgICAgIGNhc2UgJ2JzYyc6XG4gICAgICAgIHJldHVybiBldGhJY29uLnNyYyAvLyDmmoLml7bkvb/nlKjlkIzkuIDkuKrlm77moIdcbiAgICAgIGNhc2UgJ2J0Yyc6XG4gICAgICAgIHJldHVybiBldGhJY29uLnNyY1xuICAgICAgY2FzZSAnc29sYW5hJzpcbiAgICAgICAgcmV0dXJuIGV0aEljb24uc3JjXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gZXRoSWNvbi5zcmNcbiAgICB9XG4gIH1cbiAgaWYgKHRyYW5zYWN0aW9uU3RvcmUuaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxZU3RhY2sgYmc9XCIkYmFja2dyb3VuZFwiIGZsZXg9ezF9IGp1c3RpZnlDb250ZW50PVwiY2VudGVyXCIgYWxpZ25JdGVtcz1cImNlbnRlclwiPlxuICAgICAgICA8VGV4dCBjb2xvcj1cIndoaXRlXCI+5Yqg6L295LitLi4uPC9UZXh0PlxuICAgICAgPC9ZU3RhY2s+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8WVN0YWNrIGJnPVwiJGJhY2tncm91bmRcIiBmbGV4PXsxfT5cbiAgICAgIDxZU3RhY2sgcHg9ezE2fSBweT17MzB9IGZsZXg9ezF9PlxuICAgICAgICA8WFN0YWNrIHBsPXsxNn0ganVzdGlmeUNvbnRlbnQ9J3NwYWNlLWJldHdlZW4nPlxuICAgICAgICAgIDxIMz7kuqTmmJM8L0gzPlxuICAgICAgICAgIDxUZXh0IGNvbG9yPVwiIzQ1NzVGRlwiIGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD17NTAwfSBtcj17MTZ9PlxuICAgICAgICAgICAgey8qIHt0KCd0aW1lLmZpbHRlcicpIHx8ICfnrZvpgIknfSAqL31cbiAgICAgICAgICA8L1RleHQ+XG4gICAgICAgIDwvWFN0YWNrPlxuXG4gICAgICAgIHsvKiDkuqTmmJPorrDlvZXliJfooaggKi99XG4gICAgICAgIDxZU3RhY2sgZmxleD17MX0gbWI9e2N1cnJlbnRBY2NvdW50ID8gODAgOiAxNDB9PlxuICAgICAgICAgIHtPYmplY3Qua2V5cyhncm91cGVkVHJhbnNhY3Rpb25zKS5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICA8WVN0YWNrIGZsZXg9ezF9IGFsaWduSXRlbXM9XCJjZW50ZXJcIiBtdD17MTAwfT5cbiAgICAgICAgICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD0nY2VudGVyJyBtdD17MH0+XG4gICAgICAgICAgICAgICAgPEltYWdlIHNvdXJjZT17ZXhjZWhhbmdJY29uLnNyY30gd2lkdGg9ezE3M30gaGVpZ2h0PXsxNDJ9IC8+XG4gICAgICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgICAgICA8WVN0YWNrIGp1c3RpZnlDb250ZW50PSdjZW50ZXInIG10PXsyMH0+XG4gICAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCIkd2hpdGUxXCIgZm9udFNpemU9ezE2fSBmb250V2VpZ2h0PVwiYm9sZFwiIHRleHRBbGlnbj0nY2VudGVyJyBtYj17MTB9Pui/mOayoeacieS6pOaYkzwvVGV4dD5cbiAgICAgICAgICAgICAgICA8VGV4dCBjb2xvcj1cIiRhY2NlbnQxMVwiIHdpZHRoPXsyODB9IHRleHRBbGlnbj0nY2VudGVyJyBtYXJnaW49XCJhdXRvXCI+5LiA5pem5oKo5byA5aeL5L2/55So6ZKx5YyF77yM5oKo55qE5Yqg5a+G6LSn5biB5ZKMIE5GVCDmtLvliqjlsIbmmL7npLrlnKjov5nph4zjgII8L1RleHQ+XG4gICAgICAgICAgICAgIDwvWVN0YWNrPlxuICAgICAgICAgICAgPC9ZU3RhY2s+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIE9iamVjdC5lbnRyaWVzKGdyb3VwZWRUcmFuc2FjdGlvbnMpLm1hcCgoW2RhdGUsIHR4c10pID0+IChcbiAgICAgICAgICAgICAgPFlTdGFjayBrZXk9e2RhdGV9IG10PXsyMH0+XG4gICAgICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiIGNvbG9yPVwiJGFjY2VudDExXCIgbWI9ezEwfT5cbiAgICAgICAgICAgICAgICAgIHtkYXRlfVxuICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICB7dHhzLm1hcCgodHgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxYU3RhY2sga2V5PXt0eC5pZH0ganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezI1fSBhbGlnbkl0ZW1zPVwiY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxYU3RhY2sgYWxpZ25JdGVtcz1cImNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxWaWV3IHBvc2l0aW9uPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZSBzb3VyY2U9e2dldENoYWluSWNvbih0eC5jaGFpbil9IHdpZHRoPXszOH0gaGVpZ2h0PXszOH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgICAgICAgICAgPFZpZXcgbWw9ezEwfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cIndoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRUeXBlRGlzcGxheVRleHQodHgudHlwZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTJ9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCIkYWNjZW50MTFcIiBtdD17Mn0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0eC5kaXNwbGF5QWRkcmVzc31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICAgICAgICAgICAgICA8WVN0YWNrIGZsZXhEaXJlY3Rpb249XCJjb2x1bW5cIiBhbGlnbkl0ZW1zPVwiZmxleC1lbmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCJ3aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3R4LmRpc3BsYXlBbW91bnR9XG4gICAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxMn0gZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cIiRhY2NlbnQxMVwiIG10PXsyfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHt0eC5kaXNwbGF5VGltZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAge3R4LnR4SGFzaCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxMH0gY29sb3I9XCIkYWNjZW50MTFcIiBtdD17MX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0eC50eEhhc2guc2xpY2UoMCwgOCl9Li4uXG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgICAgIDwvWVN0YWNrPlxuICAgICAgICAgICAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvWVN0YWNrPlxuICAgICAgICAgICAgKSlcbiAgICAgICAgICApfVxuICAgICAgICA8L1lTdGFjaz5cblxuICAgICAgICB7Lyog5bqV6YOo5oyJ6ZKuIC0g5qC55o2u5piv5ZCm5pyJ6ZKx5YyF5Yqo5oCB5pi+56S6ICovfVxuICAgICAgICB7IWN1cnJlbnRBY2NvdW50ICYmIChcbiAgICAgICAgICA8WVN0YWNrIHBvc2l0aW9uPVwiYWJzb2x1dGVcIiBib3R0b209ezkwfSBsZWZ0PXsxNn0gcmlnaHQ9ezE2fT5cbiAgICAgICAgICAgIDxCdXR0b24gcm91bmRlZD17MzB9IHdpZHRoPVwiMTAwJVwiIGJnPVwiJGFjY2VudDExXCIgb25QcmVzcz17KCkgPT4gcm91dGVyLnB1c2goJy93YWxsZXQvYnV5Q29pbicpfT5cbiAgICAgICAgICAgICAgPFRleHQgY29sb3I9XCIkd2hpdGUxXCI+5bCG5Yqg5a+G6LSn5biB5re75Yqg5Yiw5oKo55qE6ZKx5YyFPC9UZXh0PlxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9ZU3RhY2s+XG4gICAgICAgICl9XG4gICAgICA8L1lTdGFjaz5cblxuICAgICAgPEZvb3Rlck5hdkJhciAvPlxuICAgIDwvWVN0YWNrPlxuICApXG59Il0sIm5hbWVzIjpbIkJ1dHRvbiIsIkgzIiwiWFN0YWNrIiwiWVN0YWNrIiwiVmlldyIsIlRleHQiLCJJbWFnZSIsImV4Y2VoYW5nSWNvbiIsInVzZUVmZmVjdCIsIkZvb3Rlck5hdkJhciIsInVzZVRyYW5zYWN0aW9uU3RvcmUiLCJmb3JtYXRUcmFuc2FjdGlvbiIsImdldFR5cGVEaXNwbGF5VGV4dCIsInVzZVdhbGxldFN0b3JlIiwidXNlVHJhbnNsYXRpb24iLCJldGhJY29uIiwidXNlUm91dGVyIiwiRXhjaGFuZ2VTY3JlZW4iLCJ0IiwidHJhbnNhY3Rpb25TdG9yZSIsIndhbGxldFN0b3JlIiwicm91dGVyIiwiY3VycmVudEFjY291bnQiLCJpbml0IiwibG9hZFRyYW5zYWN0aW9ucyIsImFjY291bnRJZCIsImlzTG9hZGluZyIsImFkZHJlc3NlcyIsImV0aCIsImFkZHJlc3MiLCJic2MiLCJidGMiLCJzb2xhbmEiLCJmaWx0ZXIiLCJCb29sZWFuIiwibGVuZ3RoIiwiY3VycmVudFRyYW5zYWN0aW9ucyIsImdldFRyYW5zYWN0aW9uc0J5QWRkcmVzc2VzIiwidHJhbnNhY3Rpb25zIiwiY29uc29sZSIsImxvZyIsImdyb3VwVHJhbnNhY3Rpb25zQnlEYXRlIiwiZ3JvdXBzIiwiZm9yRWFjaCIsInR4IiwiZGF0ZSIsIkRhdGUiLCJ0aW1lc3RhbXAiLCJ0b2RheSIsInllc3RlcmRheSIsInNldERhdGUiLCJnZXREYXRlIiwiZGF0ZUtleSIsInRvRGF0ZVN0cmluZyIsInRvTG9jYWxlRGF0ZVN0cmluZyIsInB1c2giLCJncm91cGVkVHJhbnNhY3Rpb25zIiwiZ2V0Q2hhaW5JY29uIiwiY2hhaW4iLCJzcmMiLCJkYXRhLWF0IiwiZGF0YS1pbiIsImRhdGEtaXMiLCJiZyIsImZsZXgiLCJqdXN0aWZ5Q29udGVudCIsImFsaWduSXRlbXMiLCJjb2xvciIsInB4IiwicHkiLCJwbCIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsIm1yIiwibWIiLCJPYmplY3QiLCJrZXlzIiwibXQiLCJzb3VyY2UiLCJ3aWR0aCIsImhlaWdodCIsInRleHRBbGlnbiIsIm1hcmdpbiIsImVudHJpZXMiLCJtYXAiLCJ0eHMiLCJwb3NpdGlvbiIsIm1sIiwidHlwZSIsImRpc3BsYXlBZGRyZXNzIiwiZmxleERpcmVjdGlvbiIsImRpc3BsYXlBbW91bnQiLCJkaXNwbGF5VGltZSIsInR4SGFzaCIsInNsaWNlIiwiaWQiLCJib3R0b20iLCJsZWZ0IiwicmlnaHQiLCJyb3VuZGVkIiwib25QcmVzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/exchange-screen.tsx\n"));

/***/ }),

/***/ "../../packages/app/stores/transactionStore.ts":
/*!*****************************************************!*\
  !*** ../../packages/app/stores/transactionStore.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatTransaction: function() { return /* binding */ formatTransaction; },\n/* harmony export */   getChainDisplayName: function() { return /* binding */ getChainDisplayName; },\n/* harmony export */   getStatusDisplayText: function() { return /* binding */ getStatusDisplayText; },\n/* harmony export */   getTypeDisplayText: function() { return /* binding */ getTypeDisplayText; },\n/* harmony export */   useTransactionStore: function() { return /* binding */ useTransactionStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"../../node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\n/* harmony import */ var app_utils_bip39__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! app/utils/bip39 */ \"../../packages/app/utils/bip39.ts\");\n\n\n\nconst STORAGE_KEY = \"TRANSACTION_HISTORY\";\nconst useTransactionStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        transactions: [],\n        isLoading: false,\n        loadTransactions: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const stored = await app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getItem(STORAGE_KEY);\n                if (stored) {\n                    const transactions = JSON.parse(stored);\n                    set({\n                        transactions\n                    });\n                }\n            } catch (error) {\n                console.error(\"Failed to load transactions:\", error);\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        addTransaction: (transactionData)=>{\n            const transaction = {\n                ...transactionData,\n                id: (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_2__.generateUID)(),\n                timestamp: Date.now()\n            };\n            const transactions = [\n                transaction,\n                ...get().transactions\n            ];\n            set({\n                transactions\n            });\n            // 持久化存储\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].setItem(STORAGE_KEY, JSON.stringify(transactions));\n        },\n        updateTransaction: (id, updates)=>{\n            const transactions = get().transactions.map((tx)=>tx.id === id ? {\n                    ...tx,\n                    ...updates\n                } : tx);\n            set({\n                transactions\n            });\n            // 持久化存储\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].setItem(STORAGE_KEY, JSON.stringify(transactions));\n        },\n        getTransactionsByAddress: (address)=>{\n            return get().transactions.filter((tx)=>tx.fromAddress.toLowerCase() === address.toLowerCase() || tx.toAddress.toLowerCase() === address.toLowerCase());\n        },\n        getTransactionsByChain: (chain)=>{\n            return get().transactions.filter((tx)=>tx.chain === chain);\n        },\n        getTransactionsByAddresses: (addresses)=>{\n            const allTransactions = [];\n            const seenTransactionIds = new Set();\n            addresses.forEach((address)=>{\n                if (address) {\n                    const addressTransactions = get().transactions.filter((tx)=>tx.fromAddress.toLowerCase() === address.toLowerCase() || tx.toAddress.toLowerCase() === address.toLowerCase());\n                    addressTransactions.forEach((tx)=>{\n                        // 使用交易ID去重，避免重复添加同一个交易\n                        if (!seenTransactionIds.has(tx.id)) {\n                            seenTransactionIds.add(tx.id);\n                            allTransactions.push(tx);\n                        }\n                    });\n                }\n            });\n            // 按时间戳排序，最新的在前面\n            return allTransactions.sort((a, b)=>b.timestamp - a.timestamp);\n        },\n        clearTransactions: ()=>{\n            set({\n                transactions: []\n            });\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].removeItem(STORAGE_KEY);\n        },\n        removeTransaction: (id)=>{\n            const transactions = get().transactions.filter((tx)=>tx.id !== id);\n            set({\n                transactions\n            });\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].setItem(STORAGE_KEY, JSON.stringify(transactions));\n        }\n    }));\n// 辅助函数：格式化交易显示\nconst formatTransaction = (transaction)=>{\n    const { amount, symbol, type, toAddress, fromAddress } = transaction;\n    return {\n        ...transaction,\n        displayAmount: \"\".concat(type === \"send\" ? \"-\" : \"+\").concat(amount, \" \").concat(symbol),\n        displayAddress: type === \"send\" ? \"至 \".concat(toAddress.slice(0, 6), \"...\").concat(toAddress.slice(-4)) : \"来自 \".concat(fromAddress.slice(0, 6), \"...\").concat(fromAddress.slice(-4)),\n        displayTime: new Date(transaction.timestamp).toLocaleString()\n    };\n};\n// 辅助函数：获取链的显示名称\nconst getChainDisplayName = (chain)=>{\n    const chainNames = {\n        eth: \"Ethereum\",\n        bsc: \"BSC\",\n        btc: \"Bitcoin\",\n        solana: \"Solana\"\n    };\n    return chainNames[chain] || chain.toUpperCase();\n};\n// 辅助函数：获取交易状态的显示文本\nconst getStatusDisplayText = (status)=>{\n    const statusTexts = {\n        pending: \"待确认\",\n        confirmed: \"已确认\",\n        failed: \"失败\"\n    };\n    return statusTexts[status] || status;\n};\n// 辅助函数：获取交易类型的显示文本\nconst getTypeDisplayText = (type)=>{\n    const typeTexts = {\n        send: \"已发送\",\n        receive: \"已接收\"\n    };\n    return typeTexts[type] || type;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/stores/transactionStore.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fexchange.tsx&page=%2Fwallet%2Fexchange!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);