/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/convertAssets"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FconvertAssets.tsx&page=%2Fwallet%2FconvertAssets!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FconvertAssets.tsx&page=%2Fwallet%2FconvertAssets! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/convertAssets\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/convertAssets.tsx */ \"./pages/wallet/convertAssets.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/convertAssets\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRmNvbnZlcnRBc3NldHMudHN4JnBhZ2U9JTJGd2FsbGV0JTJGY29udmVydEFzc2V0cyEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQywwRUFBa0M7QUFDekQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2FiMmEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvY29udmVydEFzc2V0c1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L2NvbnZlcnRBc3NldHMudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvY29udmVydEFzc2V0c1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FconvertAssets.tsx&page=%2Fwallet%2FconvertAssets!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/convert1.png":
/*!********************************************************!*\
  !*** ../../packages/assets/images/wallet/convert1.png ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/convert1.f953af80.png\",\"height\":224,\"width\":224,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fconvert1.f953af80.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvY29udmVydDEucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLG9NQUFvTSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvY29udmVydDEucG5nPzhkOGIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2NvbnZlcnQxLmY5NTNhZjgwLnBuZ1wiLFwiaGVpZ2h0XCI6MjI0LFwid2lkdGhcIjoyMjQsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGY29udmVydDEuZjk1M2FmODAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/convert1.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/convert2.png":
/*!********************************************************!*\
  !*** ../../packages/assets/images/wallet/convert2.png ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/convert2.5af96d2f.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fconvert2.5af96d2f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvY29udmVydDIucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLGtNQUFrTSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvY29udmVydDIucG5nPzQwM2IiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2NvbnZlcnQyLjVhZjk2ZDJmLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmNvbnZlcnQyLjVhZjk2ZDJmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/convert2.png\n"));

/***/ }),

/***/ "./pages/wallet/convertAssets.tsx":
/*!****************************************!*\
  !*** ./pages/wallet/convertAssets.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_conver_assets_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/conver-assets-screen */ \"../../packages/app/features/wallet/conver-assets-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_conver_assets_screen__WEBPACK_IMPORTED_MODULE_1__.ConvertAssetsScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/convertAssets.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvY29udmVydEFzc2V0cy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDOEU7QUFFL0QsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELHlGQUFtQkE7Ozs7O0FBQzdCO0tBRndCQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy93YWxsZXQvY29udmVydEFzc2V0cy50c3g/OTBkYSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IENvbnZlcnRBc3NldHNTY3JlZW4gfSBmcm9tICdhcHAvZmVhdHVyZXMvd2FsbGV0L2NvbnZlci1hc3NldHMtc2NyZWVuJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYWdlKCkge1xuICByZXR1cm4gPENvbnZlcnRBc3NldHNTY3JlZW4gLz5cbn0iXSwibmFtZXMiOlsiQ29udmVydEFzc2V0c1NjcmVlbiIsIlBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/wallet/convertAssets.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/conver-assets-screen.tsx":
/*!*******************************************************************!*\
  !*** ../../packages/app/features/wallet/conver-assets-screen.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvertAssetsScreen: function() { return /* binding */ ConvertAssetsScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_convert1_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/convert1.png */ \"../../packages/assets/images/wallet/convert1.png\");\n/* harmony import */ var _assets_images_wallet_convert2_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/convert2.png */ \"../../packages/assets/images/wallet/convert2.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 60\n});\n_c = Underline;\nfunction ConvertAssetsScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n        \"data-at\": \"conver-assets-screen.tsx:34\",\n        \"data-in\": \"ConvertAssetsScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        position: \"relative\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                \"data-at\": \"conver-assets-screen.tsx:35\",\n                \"data-in\": \"ConvertAssetsScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.NavBar, {\n                    title: \"\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                \"data-at\": \"conver-assets-screen.tsx:38\",\n                \"data-in\": \"ConvertAssetsScreen\",\n                \"data-is\": \"YStack\",\n                mt: 40,\n                px: 16,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                        \"data-at\": \"conver-assets-screen.tsx:39\",\n                        \"data-in\": \"ConvertAssetsScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_convert1_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 112,\n                        height: 112,\n                        margin: \"auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.H3, {\n                        \"data-at\": \"conver-assets-screen.tsx:40\",\n                        \"data-in\": \"ConvertAssetsScreen\",\n                        \"data-is\": \"H3\",\n                        width: \"70%\",\n                        textAlign: \"center\",\n                        margin: \"auto\",\n                        children: \"兑换并从一种资产桥接到 另一种资产\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"conver-assets-screen.tsx:43\",\n                        \"data-in\": \"ConvertAssetsScreen\",\n                        \"data-is\": \"XStack\",\n                        px: 36,\n                        width: \"70%\",\n                        mt: 60,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                \"data-at\": \"conver-assets-screen.tsx:44\",\n                                \"data-in\": \"ConvertAssetsScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_convert2_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 16,\n                                height: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                                \"data-at\": \"conver-assets-screen.tsx:45\",\n                                \"data-in\": \"ConvertAssetsScreen\",\n                                \"data-is\": \"YStack\",\n                                ml: 10,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"conver-assets-screen.tsx:46\",\n                                        \"data-in\": \"ConvertAssetsScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"获得最优价格\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"conver-assets-screen.tsx:49\",\n                                        \"data-in\": \"ConvertAssetsScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"$accent11\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        mt: 6,\n                                        width: 260,\n                                        children: \"我们调查超过 75 个去中心化交易所，为您找到最佳兑换路线和价格。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"conver-assets-screen.tsx:54\",\n                        \"data-in\": \"ConvertAssetsScreen\",\n                        \"data-is\": \"XStack\",\n                        px: 36,\n                        width: \"70%\",\n                        mt: 30,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                \"data-at\": \"conver-assets-screen.tsx:55\",\n                                \"data-in\": \"ConvertAssetsScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_convert2_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 16,\n                                height: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                                \"data-at\": \"conver-assets-screen.tsx:56\",\n                                \"data-in\": \"ConvertAssetsScreen\",\n                                \"data-is\": \"YStack\",\n                                ml: 10,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"conver-assets-screen.tsx:57\",\n                                        \"data-in\": \"ConvertAssetsScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"交易超过 200 万种代币\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"conver-assets-screen.tsx:60\",\n                                        \"data-in\": \"ConvertAssetsScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"$accent11\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        mt: 6,\n                                        width: 260,\n                                        children: \"在 Base、以太坊、Arbitrum、Optimism、 Polygon、Solana、BNB 以及 Avalanche 网络上 进行兑换。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"conver-assets-screen.tsx:66\",\n                        \"data-in\": \"ConvertAssetsScreen\",\n                        \"data-is\": \"XStack\",\n                        px: 36,\n                        width: \"70%\",\n                        mt: 30,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                \"data-at\": \"conver-assets-screen.tsx:67\",\n                                \"data-in\": \"ConvertAssetsScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_convert2_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 16,\n                                height: 16\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                                \"data-at\": \"conver-assets-screen.tsx:68\",\n                                \"data-in\": \"ConvertAssetsScreen\",\n                                \"data-is\": \"YStack\",\n                                ml: 10,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"conver-assets-screen.tsx:69\",\n                                        \"data-in\": \"ConvertAssetsScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"通过兑换模拟进行保护\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"conver-assets-screen.tsx:72\",\n                                        \"data-in\": \"ConvertAssetsScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"$accent11\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        mt: 6,\n                                        width: 260,\n                                        children: \"通过兑换模拟避免资金损失。如果您的兑换可能失败，我们会向您发出警告。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                \"data-at\": \"conver-assets-screen.tsx:78\",\n                \"data-in\": \"ConvertAssetsScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 50,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        \"data-at\": \"conver-assets-screen.tsx:79-86\",\n                        \"data-in\": \"ConvertAssetsScreen\",\n                        \"data-is\": \"Button\",\n                        rounded: 30,\n                        style: {\n                            // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',\n                            background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                        },\n                        onPress: ()=>router.push(\"/wallet/buyCoin\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            \"data-at\": \"conver-assets-screen.tsx:87\",\n                            \"data-in\": \"ConvertAssetsScreen\",\n                            \"data-is\": \"Text\",\n                            color: \"$black1\",\n                            fontSize: 14,\n                            fontWeight: \"bold\",\n                            children: \"将加密货币添加到您的钱包\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        \"data-at\": \"conver-assets-screen.tsx:91\",\n                        \"data-in\": \"ConvertAssetsScreen\",\n                        \"data-is\": \"Button\",\n                        rounded: 30,\n                        mt: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            \"data-at\": \"conver-assets-screen.tsx:92\",\n                            \"data-in\": \"ConvertAssetsScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 14,\n                            fontWeight: \"bold\",\n                            children: \"了解更多\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/conver-assets-screen.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\n_s(ConvertAssetsScreen, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c1 = ConvertAssetsScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ConvertAssetsScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/conver-assets-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FconvertAssets.tsx&page=%2Fwallet%2FconvertAssets!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);