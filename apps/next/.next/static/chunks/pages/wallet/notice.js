/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/notice"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fnotice.tsx&page=%2Fwallet%2Fnotice!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fnotice.tsx&page=%2Fwallet%2Fnotice! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/notice\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/notice.tsx */ \"./pages/wallet/notice.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/notice\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRm5vdGljZS50c3gmcGFnZT0lMkZ3YWxsZXQlMkZub3RpY2UhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsNERBQTJCO0FBQ2xEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz83NWFmIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvd2FsbGV0L25vdGljZVwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L25vdGljZS50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL3dhbGxldC9ub3RpY2VcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fnotice.tsx&page=%2Fwallet%2Fnotice!\n"));

/***/ }),

/***/ "../../packages/assets/images/setting.png":
/*!************************************************!*\
  !*** ../../packages/assets/images/setting.png ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/setting.1f39b21b.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fsetting.1f39b21b.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9zZXR0aW5nLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxnTUFBZ00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvc2V0dGluZy5wbmc/OTJiNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvc2V0dGluZy4xZjM5YjIxYi5wbmdcIixcImhlaWdodFwiOjI0LFwid2lkdGhcIjoyNCxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZzZXR0aW5nLjFmMzliMjFiLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/setting.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/notice.png":
/*!******************************************************!*\
  !*** ../../packages/assets/images/wallet/notice.png ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/notice.7bc1217f.png\",\"height\":224,\"width\":224,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnotice.7bc1217f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvbm90aWNlLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyxnTUFBZ00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L25vdGljZS5wbmc/Mjg4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvbm90aWNlLjdiYzEyMTdmLnBuZ1wiLFwiaGVpZ2h0XCI6MjI0LFwid2lkdGhcIjoyMjQsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGbm90aWNlLjdiYzEyMTdmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/notice.png\n"));

/***/ }),

/***/ "./pages/wallet/notice.tsx":
/*!*********************************!*\
  !*** ./pages/wallet/notice.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_notice_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/notice-screen */ \"../../packages/app/features/wallet/notice-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_notice_screen__WEBPACK_IMPORTED_MODULE_1__.NoticeScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/notice.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvbm90aWNlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUNnRTtBQUVqRCxTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QsMkVBQVlBOzs7OztBQUN0QjtLQUZ3QkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcGFnZXMvd2FsbGV0L25vdGljZS50c3g/ZGFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IE5vdGljZVNjcmVlbiB9IGZyb20gJ2FwcC9mZWF0dXJlcy93YWxsZXQvbm90aWNlLXNjcmVlbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAgcmV0dXJuIDxOb3RpY2VTY3JlZW4gLz5cbn0iXSwibmFtZXMiOlsiTm90aWNlU2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/notice.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/notice-screen.tsx":
/*!************************************************************!*\
  !*** ../../packages/app/features/wallet/notice-screen.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoticeScreen: function() { return /* binding */ NoticeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_notice_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/notice.png */ \"../../packages/assets/images/wallet/notice.png\");\n/* harmony import */ var _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/setting.png */ \"../../packages/assets/images/setting.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NoticeScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const tabList = [\n        \"全部\",\n        \"交换\",\n        \"赚取\",\n        \"社交媒体\",\n        \"管理\",\n        \"监听\"\n    ];\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const dataList = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: \"交易资产\",\n            isSelected: true\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: \"交易资产\",\n            isSelected: false\n        },\n        {\n            id: 3,\n            name: \"SushiSwap\",\n            desc: \"交易资产\",\n            isSelected: false\n        }\n    ];\n    const onMnemonicClick = ()=>{\n        router.push(\"/wallet/password\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n        \"data-at\": \"notice-screen.tsx:43\",\n        \"data-in\": \"NoticeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        px: 16,\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                \"data-at\": \"notice-screen.tsx:44\",\n                \"data-in\": \"NoticeScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.NavBar, {\n                        title: \"通知\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_6__.Pressable, {\n                        onPress: ()=>router.push(\"/wallet/noticeSetting\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                            \"data-at\": \"notice-screen.tsx:47\",\n                            \"data-in\": \"NoticeScreen\",\n                            \"data-is\": \"Image\",\n                            source: _assets_images_setting_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                            width: 16,\n                            height: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                \"data-at\": \"notice-screen.tsx:51\",\n                \"data-in\": \"NoticeScreen\",\n                \"data-is\": \"XStack\",\n                pl: 16,\n                justifyContent: \"center\",\n                mt: 50,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                    \"data-at\": \"notice-screen.tsx:52\",\n                    \"data-in\": \"NoticeScreen\",\n                    \"data-is\": \"Image\",\n                    source: _assets_images_wallet_notice_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                    width: 173,\n                    height: 142\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                \"data-at\": \"notice-screen.tsx:54\",\n                \"data-in\": \"NoticeScreen\",\n                \"data-is\": \"YStack\",\n                pl: 16,\n                justifyContent: \"center\",\n                mt: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                        \"data-at\": \"notice-screen.tsx:55\",\n                        \"data-in\": \"NoticeScreen\",\n                        \"data-is\": \"Text\",\n                        color: \"$white1\",\n                        fontSize: 16,\n                        fontWeight: \"bold\",\n                        textAlign: \"center\",\n                        mb: 10,\n                        children: \"欢迎!\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                        \"data-at\": \"notice-screen.tsx:56\",\n                        \"data-in\": \"NoticeScreen\",\n                        \"data-is\": \"Text\",\n                        color: \"$accent11\",\n                        width: 280,\n                        textAlign: \"center\",\n                        margin: \"auto\",\n                        children: \"探索加密货币世界以开始获取更新。\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-screen.tsx\",\n        lineNumber: 32,\n        columnNumber: 10\n    }, this);\n}\n_s(NoticeScreen, \"k5oVBT4gVizGZi8FjIw9Gkdgvrs=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = NoticeScreen;\nvar _c;\n$RefreshReg$(_c, \"NoticeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/notice-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fnotice.tsx&page=%2Fwallet%2Fnotice!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);