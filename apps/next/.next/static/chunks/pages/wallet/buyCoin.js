/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/buyCoin"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyCoin.tsx&page=%2Fwallet%2FbuyCoin!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyCoin.tsx&page=%2Fwallet%2FbuyCoin! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/buyCoin\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/buyCoin.tsx */ \"./pages/wallet/buyCoin.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/buyCoin\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRmJ1eUNvaW4udHN4JnBhZ2U9JTJGd2FsbGV0JTJGYnV5Q29pbiEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw4REFBNEI7QUFDbkQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzE4NjgiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvYnV5Q29pblwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L2J1eUNvaW4udHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvYnV5Q29pblwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyCoin.tsx&page=%2Fwallet%2FbuyCoin!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/buy1.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/buy1.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy1.143da269.png\",\"height\":40,\"width\":40,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy1.143da269.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYnV5MS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9idXkxLnBuZz9mNWRiIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9idXkxLjE0M2RhMjY5LnBuZ1wiLFwiaGVpZ2h0XCI6NDAsXCJ3aWR0aFwiOjQwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTEuMTQzZGEyNjkucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/buy1.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/buy2.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/buy2.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy2.1cda78b1.png\",\"height\":56,\"width\":56,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy2.1cda78b1.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYnV5Mi5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9idXkyLnBuZz8zOWE2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9idXkyLjFjZGE3OGIxLnBuZ1wiLFwiaGVpZ2h0XCI6NTYsXCJ3aWR0aFwiOjU2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTIuMWNkYTc4YjEucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/buy2.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/net3.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/net3.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/net3.4cb3a483.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnet3.4cb3a483.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvbmV0My5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9uZXQzLnBuZz82YzczIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9uZXQzLjRjYjNhNDgzLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm5ldDMuNGNiM2E0ODMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/net3.png\n"));

/***/ }),

/***/ "./pages/wallet/buyCoin.tsx":
/*!**********************************!*\
  !*** ./pages/wallet/buyCoin.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_buy_coin_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/buy-coin-screen */ \"../../packages/app/features/wallet/buy-coin-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_buy_coin_screen__WEBPACK_IMPORTED_MODULE_1__.BuyCoinScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/buyCoin.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvYnV5Q29pbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFbUU7QUFFcEQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDhFQUFhQTs7Ozs7QUFDdkI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3dhbGxldC9idXlDb2luLnRzeD8wM2EyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBCdXlDb2luU2NyZWVuIH0gZnJvbSAnYXBwL2ZlYXR1cmVzL3dhbGxldC9idXktY29pbi1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8QnV5Q29pblNjcmVlbiAvPlxufSJdLCJuYW1lcyI6WyJCdXlDb2luU2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/buyCoin.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/buy-coin-screen.tsx":
/*!**************************************************************!*\
  !*** ../../packages/app/features/wallet/buy-coin-screen.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BuyCoinScreen: function() { return /* binding */ BuyCoinScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/net3.png */ \"../../packages/assets/images/wallet/net3.png\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_buy1_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/buy1.png */ \"../../packages/assets/images/wallet/buy1.png\");\n/* harmony import */ var _assets_images_wallet_buy2_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/buy2.png */ \"../../packages/assets/images/wallet/buy2.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 60\n});\n_c = Underline;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst ActiveUnderline = (0,tamagui__WEBPACK_IMPORTED_MODULE_5__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = ActiveUnderline;\nfunction BuyCoinScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_7__.useTranslation)();\n    const tabList = [\n        t(\"trading.all\") || \"全部\",\n        t(\"trading.exchange\") || \"交换\",\n        t(\"trading.earn\") || \"赚取\",\n        t(\"trading.socialMedia\") || \"社交媒体\",\n        t(\"trading.manage\") || \"管理\",\n        t(\"trading.listen\") || \"监听\"\n    ];\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0);\n    const dataList = [\n        {\n            id: 1,\n            name: \"Aerodrome\",\n            desc: t(\"trading.tradeAssets\") || \"交易资产\",\n            isSelected: true\n        },\n        {\n            id: 2,\n            name: \"Uniswap\",\n            desc: t(\"trading.tradeAssets\") || \"交易资产\",\n            isSelected: false\n        },\n        {\n            id: 3,\n            name: \"SushiSwap\",\n            desc: t(\"trading.tradeAssets\") || \"交易资产\",\n            isSelected: false\n        }\n    ];\n    const onMnemonicClick = ()=>{\n        router.push(\"/wallet/password\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n        \"data-at\": \"buy-coin-screen.tsx:84\",\n        \"data-in\": \"BuyCoinScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        position: \"relative\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                \"data-at\": \"buy-coin-screen.tsx:85\",\n                \"data-in\": \"BuyCoinScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.NavBar, {\n                        title: t(\"trading.buy\") || \"买入\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                        \"data-at\": \"buy-coin-screen.tsx:87\",\n                        \"data-in\": \"BuyCoinScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_net3_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 16,\n                        height: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.YStack, {\n                \"data-at\": \"buy-coin-screen.tsx:89\",\n                \"data-in\": \"BuyCoinScreen\",\n                \"data-is\": \"YStack\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"buy-coin-screen.tsx:90\",\n                        \"data-in\": \"BuyCoinScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 20,\n                        position: \"relative\",\n                        px: 16,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                            \"data-at\": \"buy-coin-screen.tsx:91-104\",\n                            \"data-in\": \"BuyCoinScreen\",\n                            \"data-is\": \"Input\",\n                            placeholder: \"USD\",\n                            width: \"100%\",\n                            height: 100,\n                            bg: \"$black1\",\n                            borderColor: \"$black1\",\n                            borderRadius: 10,\n                            fontSize: 54,\n                            value: \"0\",\n                            fontWeight: \"bold\",\n                            p: 0,\n                            color: \"#3C72F9\",\n                            focusStyle: {\n                                borderColor: \"transparent\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"buy-coin-screen.tsx:106\",\n                        \"data-in\": \"BuyCoinScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        items: \"center\",\n                        px: 16,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            \"data-at\": \"buy-coin-screen.tsx:107\",\n                            \"data-in\": \"BuyCoinScreen\",\n                            \"data-is\": \"Text\",\n                            color: \"$accent11\",\n                            fontSize: 14,\n                            fontWeight: \"bold\",\n                            children: \"0.00000 ETH\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Underline, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"buy-coin-screen.tsx:113-122\",\n                        \"data-in\": \"BuyCoinScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        px: 16,\n                        rounded: 20,\n                        bg: \"#141519\",\n                        width: 140,\n                        height: 36,\n                        items: \"center\",\n                        ml: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                \"data-at\": \"buy-coin-screen.tsx:123\",\n                                \"data-in\": \"BuyCoinScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_buy1_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 20,\n                                height: 20\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                \"data-at\": \"buy-coin-screen.tsx:124\",\n                                \"data-in\": \"BuyCoinScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 12,\n                                ml: 4,\n                                children: \"网络：Base\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_9__.ChevronRight, {\n                                size: 20,\n                                color: \"$accent11\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"buy-coin-screen.tsx:129\",\n                        \"data-in\": \"BuyCoinScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 20,\n                        items: \"center\",\n                        justifyContent: \"space-between\",\n                        px: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                \"data-at\": \"buy-coin-screen.tsx:130\",\n                                \"data-in\": \"BuyCoinScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                        \"data-at\": \"buy-coin-screen.tsx:131\",\n                                        \"data-in\": \"BuyCoinScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: {\n                                            uri: \"\"\n                                        },\n                                        width: 28,\n                                        height: 28,\n                                        rounded: 14,\n                                        bg: \"$accent11\",\n                                        mr: 6\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                        \"data-at\": \"buy-coin-screen.tsx:132\",\n                                        \"data-in\": \"BuyCoinScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"buy-coin-screen.tsx:133\",\n                                                \"data-in\": \"BuyCoinScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: \"买入\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"buy-coin-screen.tsx:136\",\n                                                \"data-in\": \"BuyCoinScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"$accent11\",\n                                                fontSize: 12,\n                                                fontWeight: 500,\n                                                children: \"KTA\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                \"data-at\": \"buy-coin-screen.tsx:141\",\n                                \"data-in\": \"BuyCoinScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                        \"data-at\": \"buy-coin-screen.tsx:142\",\n                                        \"data-in\": \"BuyCoinScreen\",\n                                        \"data-is\": \"View\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_9__.ChevronRight, {\n                                        size: 20,\n                                        color: \"$white6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                        \"data-at\": \"buy-coin-screen.tsx:146\",\n                        \"data-in\": \"BuyCoinScreen\",\n                        \"data-is\": \"View\",\n                        width: 1,\n                        height: 16,\n                        bg: \"$black10\",\n                        ml: 30,\n                        mt: 10\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                        \"data-at\": \"buy-coin-screen.tsx:147\",\n                        \"data-in\": \"BuyCoinScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        items: \"center\",\n                        justifyContent: \"space-between\",\n                        px: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                \"data-at\": \"buy-coin-screen.tsx:148\",\n                                \"data-in\": \"BuyCoinScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Image, {\n                                        \"data-at\": \"buy-coin-screen.tsx:149-156\",\n                                        \"data-in\": \"BuyCoinScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_buy2_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                        width: 28,\n                                        height: 28,\n                                        rounded: 14,\n                                        bg: \"$accent11\",\n                                        mr: 6\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                        \"data-at\": \"buy-coin-screen.tsx:157\",\n                                        \"data-in\": \"BuyCoinScreen\",\n                                        \"data-is\": \"View\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"buy-coin-screen.tsx:158\",\n                                                \"data-in\": \"BuyCoinScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"white\",\n                                                fontSize: 14,\n                                                fontWeight: \"bold\",\n                                                children: \"付款方式\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                                \"data-at\": \"buy-coin-screen.tsx:161\",\n                                                \"data-in\": \"BuyCoinScreen\",\n                                                \"data-is\": \"Text\",\n                                                color: \"$accent11\",\n                                                fontSize: 12,\n                                                fontWeight: 500,\n                                                children: \"选择付款方式\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.XStack, {\n                                \"data-at\": \"buy-coin-screen.tsx:166\",\n                                \"data-in\": \"BuyCoinScreen\",\n                                \"data-is\": \"XStack\",\n                                justifyContent: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.View, {\n                                        \"data-at\": \"buy-coin-screen.tsx:167\",\n                                        \"data-in\": \"BuyCoinScreen\",\n                                        \"data-is\": \"View\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_9__.ChevronRight, {\n                                        size: 20,\n                                        color: \"$white6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                \"data-at\": \"buy-coin-screen.tsx:172-182\",\n                \"data-in\": \"BuyCoinScreen\",\n                \"data-is\": \"Button\",\n                position: \"absolute\",\n                bottom: 20,\n                ml: 16,\n                rounded: 30,\n                width: \"92%\",\n                style: {\n                    // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',\n                    background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                    \"data-at\": \"buy-coin-screen.tsx:183\",\n                    \"data-in\": \"BuyCoinScreen\",\n                    \"data-is\": \"Text\",\n                    color: \"$black1\",\n                    fontSize: 14,\n                    fontWeight: \"bold\",\n                    children: \"继续\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-coin-screen.tsx\",\n        lineNumber: 57,\n        columnNumber: 10\n    }, this);\n}\n_s(BuyCoinScreen, \"7MXzZv2V2Y4b/XY1RZrD/i1Pz5c=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_7__.useTranslation\n    ];\n});\n_c3 = BuyCoinScreen;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"ActiveUnderline\");\n$RefreshReg$(_c3, \"BuyCoinScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/buy-coin-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyCoin.tsx&page=%2Fwallet%2FbuyCoin!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);