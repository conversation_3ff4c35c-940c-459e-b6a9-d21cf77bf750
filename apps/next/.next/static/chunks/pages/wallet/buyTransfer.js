/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/buyTransfer"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyTransfer.tsx&page=%2Fwallet%2FbuyTransfer!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyTransfer.tsx&page=%2Fwallet%2FbuyTransfer! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/buyTransfer\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/buyTransfer.tsx */ \"./pages/wallet/buyTransfer.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/buyTransfer\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRmJ1eVRyYW5zZmVyLnRzeCZwYWdlPSUyRndhbGxldCUyRmJ1eVRyYW5zZmVyISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLHNFQUFnQztBQUN2RDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NDA5ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL3dhbGxldC9idXlUcmFuc2ZlclwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L2J1eVRyYW5zZmVyLnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvd2FsbGV0L2J1eVRyYW5zZmVyXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyTransfer.tsx&page=%2Fwallet%2FbuyTransfer!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/convert3.png":
/*!********************************************************!*\
  !*** ../../packages/assets/images/wallet/convert3.png ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/convert3.3c682c71.png\",\"height\":300,\"width\":300,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fconvert3.3c682c71.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvY29udmVydDMucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLG9NQUFvTSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvY29udmVydDMucG5nPzJiZTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2NvbnZlcnQzLjNjNjgyYzcxLnBuZ1wiLFwiaGVpZ2h0XCI6MzAwLFwid2lkdGhcIjozMDAsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGY29udmVydDMuM2M2ODJjNzEucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/convert3.png\n"));

/***/ }),

/***/ "./pages/wallet/buyTransfer.tsx":
/*!**************************************!*\
  !*** ./pages/wallet/buyTransfer.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_buy_transfer_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/buy-transfer-screen */ \"../../packages/app/features/wallet/buy-transfer-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_buy_transfer_screen__WEBPACK_IMPORTED_MODULE_1__.BuyTransferScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/buyTransfer.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvYnV5VHJhbnNmZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQzJFO0FBRTVELFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCxzRkFBaUJBOzs7OztBQUMzQjtLQUZ3QkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcGFnZXMvd2FsbGV0L2J1eVRyYW5zZmVyLnRzeD85NjFlIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgQnV5VHJhbnNmZXJTY3JlZW4gfSBmcm9tICdhcHAvZmVhdHVyZXMvd2FsbGV0L2J1eS10cmFuc2Zlci1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8QnV5VHJhbnNmZXJTY3JlZW4gLz5cbn0iXSwibmFtZXMiOlsiQnV5VHJhbnNmZXJTY3JlZW4iLCJQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/wallet/buyTransfer.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/buy-transfer-screen.tsx":
/*!******************************************************************!*\
  !*** ../../packages/app/features/wallet/buy-transfer-screen.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BuyTransferScreen: function() { return /* binding */ BuyTransferScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_convert3_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/convert3.png */ \"../../packages/assets/images/wallet/convert3.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_2__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_2__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 60\n});\n_c = Underline;\nfunction BuyTransferScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.YStack, {\n        \"data-at\": \"buy-transfer-screen.tsx:22\",\n        \"data-in\": \"BuyTransferScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        position: \"relative\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.XStack, {\n                \"data-at\": \"buy-transfer-screen.tsx:23\",\n                \"data-in\": \"BuyTransferScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.NavBar, {\n                    title: \"\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.YStack, {\n                \"data-at\": \"buy-transfer-screen.tsx:26\",\n                \"data-in\": \"BuyTransferScreen\",\n                \"data-is\": \"YStack\",\n                mt: 60,\n                px: 16,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Image, {\n                        \"data-at\": \"buy-transfer-screen.tsx:27\",\n                        \"data-in\": \"BuyTransferScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_wallet_convert3_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                        width: 112,\n                        height: 112,\n                        margin: \"auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.H5, {\n                        \"data-at\": \"buy-transfer-screen.tsx:28\",\n                        \"data-in\": \"BuyTransferScreen\",\n                        \"data-is\": \"H5\",\n                        width: \"70%\",\n                        textAlign: \"center\",\n                        margin: \"auto\",\n                        children: \"购买或转账加密货币\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        \"data-at\": \"buy-transfer-screen.tsx:30\",\n                        \"data-in\": \"BuyTransferScreen\",\n                        \"data-is\": \"Text\",\n                        width: \"80%\",\n                        textAlign: \"center\",\n                        margin: \"auto\",\n                        fontSize: 14,\n                        fontWeight: 500,\n                        color: \"$accent11\",\n                        children: \"您可以使用 Coinbase 账户为您的钱包充值。如果 没有 Coinbase 账户，您也可以使用借记卡每周买 入 500 美元。\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.YStack, {\n                \"data-at\": \"buy-transfer-screen.tsx:34\",\n                \"data-in\": \"BuyTransferScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 100,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        \"data-at\": \"buy-transfer-screen.tsx:35-43\",\n                        \"data-in\": \"BuyTransferScreen\",\n                        \"data-is\": \"Button\",\n                        rounded: 30,\n                        style: {\n                            // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',\n                            background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                        },\n                        onPress: ()=>{\n                            window.location.href = \"https://login.coinbase.com/signin?client_id=f33720f734c7cdff79159d84a909a4adea7bc3a8b62e8a162391a53d17a1bdcc&oauth_challenge=39dfb00d-222a-4e53-998b-090b572df707\";\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            \"data-at\": \"buy-transfer-screen.tsx:44\",\n                            \"data-in\": \"BuyTransferScreen\",\n                            \"data-is\": \"Text\",\n                            color: \"$black1\",\n                            fontSize: 14,\n                            fontWeight: \"bold\",\n                            children: \"通过 Coinbase 购买或转移\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        \"data-at\": \"buy-transfer-screen.tsx:46\",\n                        \"data-in\": \"BuyTransferScreen\",\n                        \"data-is\": \"Button\",\n                        rounded: 30,\n                        mt: 10,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                            \"data-at\": \"buy-transfer-screen.tsx:47\",\n                            \"data-in\": \"BuyTransferScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 14,\n                            fontWeight: \"bold\",\n                            children: \"继续使用借记卡\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/buy-transfer-screen.tsx\",\n        lineNumber: 16,\n        columnNumber: 10\n    }, this);\n}\n_s(BuyTransferScreen, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c1 = BuyTransferScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"Underline\");\n$RefreshReg$(_c1, \"BuyTransferScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/buy-transfer-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FbuyTransfer.tsx&page=%2Fwallet%2FbuyTransfer!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);