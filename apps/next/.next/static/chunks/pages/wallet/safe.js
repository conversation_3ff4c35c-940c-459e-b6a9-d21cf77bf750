/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/safe"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsafe.tsx&page=%2Fwallet%2Fsafe!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsafe.tsx&page=%2Fwallet%2Fsafe! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/safe\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/safe.tsx */ \"./pages/wallet/safe.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/safe\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRnNhZmUudHN4JnBhZ2U9JTJGd2FsbGV0JTJGc2FmZSEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyx3REFBeUI7QUFDaEQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2E5MDEiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvc2FmZVwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L3NhZmUudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvc2FmZVwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsafe.tsx&page=%2Fwallet%2Fsafe!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/arrowright.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/wallet/arrowright.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/arrowright.f0d2eb68.png\",\"height\":22,\"width\":14,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Farrowright.f0d2eb68.png&w=5&q=70\",\"blurWidth\":5,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYXJyb3dyaWdodC5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9hcnJvd3JpZ2h0LnBuZz8wNDgwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9hcnJvd3JpZ2h0LmYwZDJlYjY4LnBuZ1wiLFwiaGVpZ2h0XCI6MjIsXCJ3aWR0aFwiOjE0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFycm93cmlnaHQuZjBkMmViNjgucG5nJnc9NSZxPTcwXCIsXCJibHVyV2lkdGhcIjo1LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/arrowright.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/eth.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/wallet/eth.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/eth.b241a439.png\",\"height\":64,\"width\":64,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Feth.b241a439.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZXRoLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TEFBd0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2V0aC5wbmc/NjAwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZXRoLmIyNDFhNDM5LnBuZ1wiLFwiaGVpZ2h0XCI6NjQsXCJ3aWR0aFwiOjY0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmV0aC5iMjQxYTQzOS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/eth.png\n"));

/***/ }),

/***/ "./pages/wallet/safe.tsx":
/*!*******************************!*\
  !*** ./pages/wallet/safe.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_safe_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/safe-screen */ \"../../packages/app/features/wallet/safe-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_safe_screen__WEBPACK_IMPORTED_MODULE_1__.SafeScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/safe.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvc2FmZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFNEQ7QUFFN0MsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELHVFQUFVQTs7Ozs7QUFDcEI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3dhbGxldC9zYWZlLnRzeD8xMWMzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBTYWZlU2NyZWVuIH0gZnJvbSAnYXBwL2ZlYXR1cmVzL3dhbGxldC9zYWZlLXNjcmVlbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAgcmV0dXJuIDxTYWZlU2NyZWVuIC8+XG59Il0sIm5hbWVzIjpbIlNhZmVTY3JlZW4iLCJQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/wallet/safe.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/safe-screen.tsx":
/*!**********************************************************!*\
  !*** ../../packages/app/features/wallet/safe-screen.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SafeScreen: function() { return /* binding */ SafeScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/arrowright.png */ \"../../packages/assets/images/wallet/arrowright.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst Line = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Line;\nconst ActiveText = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n    color: \"#4575FF\",\n    marginBottom: 2\n});\n_c1 = ActiveText;\nconst Underline = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2,\n    backgroundColor: \"#4575FF\"\n});\n_c2 = Underline;\nconst Underlineblock = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n    position: \"absolute\",\n    bottom: -2,\n    left: 0,\n    right: 0,\n    height: 2\n});\n_c3 = Underlineblock;\nfunction SafeScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [netList, setNetList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            name: \"Ethereum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 2,\n            name: \"Arbitrum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 3,\n            name: \"Avalanche C-Chain\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 4,\n            name: \"Base\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 5,\n            name: \"BNB (Binance Smart) Chain\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 6,\n            name: \"Fantom Opera\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 7,\n            name: \"Gnosis\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 8,\n            name: \"OP Mainnet\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 9,\n            name: \"Ethereum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        },\n        {\n            id: 10,\n            name: \"Ethereum\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"safe-screen.tsx:111\",\n        \"data-in\": \"SafeScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                \"data-at\": \"safe-screen.tsx:112\",\n                \"data-in\": \"SafeScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                pr: 16,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                    title: t(\"navigation.security\") || \"安全\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"safe-screen.tsx:116\",\n                \"data-in\": \"SafeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"safe-screen.tsx:117\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"备份\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"safe-screen.tsx:120\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"safe-screen.tsx:121\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: \"钱包1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"safe-screen.tsx:124\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"safe-screen.tsx:125-135\",\n                                        \"data-in\": \"SafeScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#C7545E\",\n                                        fontSize: 12,\n                                        mr: 10,\n                                        bg: \"rgba(199,84,94,0.16)\",\n                                        width: 45,\n                                        height: 20,\n                                        rounded: 4,\n                                        textAlign: \"center\",\n                                        lineHeight: 20,\n                                        children: \"未备份\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                        \"data-at\": \"safe-screen.tsx:138\",\n                                        \"data-in\": \"SafeScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                        width: 6,\n                                        height: 9\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"safe-screen.tsx:144\",\n                \"data-in\": \"SafeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"safe-screen.tsx:145\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"安全锁定\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"safe-screen.tsx:148\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"safe-screen.tsx:149\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: \"锁定方式\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"safe-screen.tsx:152\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"safe-screen.tsx:153\",\n                                    \"data-in\": \"SafeScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: 500,\n                                    color: \"$accent11\",\n                                    children: \"密码\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"safe-screen.tsx:160\",\n                \"data-in\": \"SafeScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 30,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"safe-screen.tsx:161\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 18,\n                        fontWeight: \"bold\",\n                        children: \"以下情况需要解锁\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"safe-screen.tsx:164\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 14,\n                        color: \"$accent11\",\n                        mt: 10,\n                        children: \"必须至少选择一个选项。\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"safe-screen.tsx:167\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"safe-screen.tsx:168\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: \"打开应用\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"safe-screen.tsx:171-175\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    \"data-at\": \"safe-screen.tsx:176-184\",\n                                    \"data-in\": \"SafeScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    backgroundColor: \"#3873F5\",\n                                    borderColor: \"#3873F5\",\n                                    checked: true,\n                                    style: {\n                                        backgroundColor: \"#3873F5\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                        animation: \"bouncy\",\n                                        backgroundColor: \"white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                        \"data-at\": \"safe-screen.tsx:189\",\n                        \"data-in\": \"SafeScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"safe-screen.tsx:190\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"safe-screen.tsx:191\",\n                                        \"data-in\": \"SafeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"进行交易\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"safe-screen.tsx:194\",\n                                        \"data-in\": \"SafeScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"仅影响标准钱包\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"safe-screen.tsx:198-202\",\n                                \"data-in\": \"SafeScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                    \"data-at\": \"safe-screen.tsx:203-211\",\n                                    \"data-in\": \"SafeScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    backgroundColor: \"#3873F5\",\n                                    borderColor: \"#3873F5\",\n                                    checked: true,\n                                    style: {\n                                        backgroundColor: \"#3873F5\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                        animation: \"bouncy\",\n                                        backgroundColor: \"white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/safe-screen.tsx\",\n        lineNumber: 84,\n        columnNumber: 10\n    }, this);\n}\n_s(SafeScreen, \"vbDc6QvONHXcupqueA2UOtGmNBw=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c4 = SafeScreen;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Line\");\n$RefreshReg$(_c1, \"ActiveText\");\n$RefreshReg$(_c2, \"Underline\");\n$RefreshReg$(_c3, \"Underlineblock\");\n$RefreshReg$(_c4, \"SafeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL3dhbGxldC9zYWZlLXNjcmVlbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQVdlO0FBQzhCO0FBQ3NCO0FBR25DO0FBSVM7QUFDa0I7QUFDYztBQUV6RSxNQUFNYSxPQUFPTiwrQ0FBTUEsQ0FBQ0gseUNBQUlBLEVBQUU7SUFDeEJVLE9BQU87SUFDUEMsUUFBUTtJQUNSQyxpQkFBaUI7SUFDakJDLElBQUk7QUFDTjtLQUxNSjtBQU9OLE1BQU1LLGFBQWFYLCtDQUFNQSxDQUFDRix5Q0FBSUEsRUFBRTtJQUM5QmMsT0FBTztJQUNQQyxjQUFjO0FBQ2hCOztBQUVBLE1BQU1DLFlBQVlkLCtDQUFNQSxDQUFDSCx5Q0FBSUEsRUFBRTtJQUM3QmtCLFVBQVU7SUFDVkMsUUFBUSxDQUFDO0lBQ1RDLE1BQU07SUFDTkMsT0FBTztJQUNQVixRQUFRO0lBQ1JDLGlCQUFpQjtBQUNuQjs7QUFDQSxNQUFNVSxpQkFBaUJuQiwrQ0FBTUEsQ0FBQ0gseUNBQUlBLEVBQUU7SUFDbENrQixVQUFVO0lBQ1ZDLFFBQVEsQ0FBQztJQUNUQyxNQUFNO0lBQ05DLE9BQU87SUFDUFYsUUFBUTtBQUNWOztBQUVPLFNBQVNZOztJQUNkLE1BQU1DLFNBQVN6Qiw0REFBU0E7SUFDeEIsTUFBTSxFQUFFMEIsQ0FBQUEsRUFBRyxHQUFHbkIsd0RBQWNBO0lBQzVCLE1BQU0sQ0FBQ29CLFlBQVlDLGNBQWMsR0FBR3RCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ3VCLFNBQVNDLFdBQVcsR0FBR3hCLCtDQUFRQSxDQUFDO1FBQ3JDO1lBQ0V5QixJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7UUFDQTtZQUNFSCxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTXpCLHlFQUFRMEI7UUFDaEI7S0FDRDtJQUVELHFCQUNFLDhEQUFDbkMsMENBQU1BO1FBQUFvQyxXQUFBO1FBQUFDLFdBQUE7UUFBQUMsV0FBQTtRQUFDQyxJQUFHO1FBQWMxQixRQUFROzswQkFDL0IsOERBQUNkLDBDQUFNQTtnQkFBQXFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNFLGdCQUFlO2dCQUFnQkMsT0FBTTtnQkFBU0MsSUFBSTswQkFDeEQsNEVBQUM1QywwQ0FBTUE7b0JBQUM2QyxPQUFPaEIsRUFBRSwwQkFBMEI7b0JBQU1pQixRQUFRLElBQU1sQixPQUFPbUIsSUFBSTs7Ozs7Ozs7Ozs7MEJBRzVFLDhEQUFDN0MsMENBQU1BO2dCQUFBb0MsV0FBQTtnQkFBQUMsV0FBQTtnQkFBQUMsV0FBQTtnQkFBQ1EsSUFBSTtnQkFBSS9CLElBQUk7Z0JBQUlnQyxJQUFJOztrQ0FDMUIsOERBQUM1Qyx5Q0FBSUE7d0JBQUFpQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDVSxVQUFVO3dCQUFJQyxZQUFXO2tDQUFNOzs7Ozs7a0NBR3JDLDhEQUFDbEQsMENBQU1BO3dCQUFBcUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ0UsZ0JBQWU7d0JBQWdCekIsSUFBSTs7MENBQ3pDLDhEQUFDWix5Q0FBSUE7Z0NBQUFpQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFDVSxVQUFVO2dDQUFJQyxZQUFXOzBDQUFNOzs7Ozs7MENBR3JDLDhEQUFDbEQsMENBQU1BO2dDQUFBcUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ0csT0FBTTs7a0RBQ1osOERBQUN0Qyx5Q0FBSUE7d0NBQUFpQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUNIckIsT0FBTTt3Q0FDTitCLFVBQVU7d0NBQ1ZFLElBQUk7d0NBQ0pYLElBQUc7d0NBQ0gzQixPQUFPO3dDQUNQQyxRQUFRO3dDQUNSc0MsU0FBUzt3Q0FDVEMsV0FBVTt3Q0FDVkMsWUFBWTtrREFBRzs7Ozs7O2tEQUlqQiw4REFBQ2pELDBDQUFLQTt3Q0FBQWdDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNnQixRQUFRNUMsZ0ZBQWtCO3dDQUFFRSxPQUFPO3dDQUFHQyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBSTNELDhEQUFDRjs7Ozs7MEJBRUQsOERBQUNYLDBDQUFNQTtnQkFBQW9DLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNRLElBQUk7Z0JBQUkvQixJQUFJO2dCQUFJZ0MsSUFBSTs7a0NBQzFCLDhEQUFDNUMseUNBQUlBO3dCQUFBaUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ1UsVUFBVTt3QkFBSUMsWUFBVztrQ0FBTTs7Ozs7O2tDQUdyQyw4REFBQ2xELDBDQUFNQTt3QkFBQXFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNFLGdCQUFlO3dCQUFnQnpCLElBQUk7OzBDQUN6Qyw4REFBQ1oseUNBQUlBO2dDQUFBaUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQ1UsVUFBVTtnQ0FBSUMsWUFBVzswQ0FBTTs7Ozs7OzBDQUdyQyw4REFBQ2xELDBDQUFNQTtnQ0FBQXFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUNHLE9BQU07MENBQ1osNEVBQUN0Qyx5Q0FBSUE7b0NBQUFpQyxXQUFBO29DQUFBQyxXQUFBO29DQUFBQyxXQUFBO29DQUFDVSxVQUFVO29DQUFJQyxZQUFZO29DQUFLaEMsT0FBTTs4Q0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTVELDhEQUFDTjs7Ozs7MEJBQ0QsOERBQUNYLDBDQUFNQTtnQkFBQW9DLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUFDLFdBQUE7Z0JBQUNRLElBQUk7Z0JBQUkvQixJQUFJO2dCQUFJZ0MsSUFBSTs7a0NBQzFCLDhEQUFDNUMseUNBQUlBO3dCQUFBaUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ1UsVUFBVTt3QkFBSUMsWUFBVztrQ0FBTTs7Ozs7O2tDQUdyQyw4REFBQzlDLHlDQUFJQTt3QkFBQWlDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNVLFVBQVU7d0JBQUkvQixPQUFNO3dCQUFZRixJQUFJO2tDQUFHOzs7Ozs7a0NBRzdDLDhEQUFDaEIsMENBQU1BO3dCQUFBcUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ0UsZ0JBQWU7d0JBQWdCekIsSUFBSTs7MENBQ3pDLDhEQUFDWix5Q0FBSUE7Z0NBQUFpQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFDVSxVQUFVO2dDQUFJQyxZQUFXOzBDQUFNOzs7Ozs7MENBR3JDLDhEQUFDL0MseUNBQUlBO2dDQUFBa0MsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FDSGlCLFNBQVM7b0NBQ1BDLFFBQVFDLEdBQUcsQ0FBQztnQ0FDZDswQ0FFQSw0RUFBQ25ELDJDQUFNQTtvQ0FBQThCLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQ0xvQixNQUFLO29DQUNMNUMsaUJBQWdCO29DQUNoQjZDLGFBQVk7b0NBQ1pDLFNBQVM7b0NBQ1RDLE9BQU87d0NBQ0wvQyxpQkFBaUI7b0NBQ25COzhDQUVBLDRFQUFDUiwyQ0FBTUEsQ0FBQ3dELEtBQUs7d0NBQUNDLFdBQVU7d0NBQVNqRCxpQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSXZELDhEQUFDZiwwQ0FBTUE7d0JBQUFxQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDRSxnQkFBZTt3QkFBZ0J6QixJQUFJOzswQ0FDekMsOERBQUNiLHlDQUFJQTtnQ0FBQWtDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7O2tEQUNILDhEQUFDbkMseUNBQUlBO3dDQUFBaUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ1UsVUFBVTt3Q0FBSUMsWUFBVztrREFBTTs7Ozs7O2tEQUdyQyw4REFBQzlDLHlDQUFJQTt3Q0FBQWlDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNVLFVBQVU7d0NBQUlDLFlBQVc7d0NBQU9oQyxPQUFNO3dDQUFZRixJQUFJO2tEQUFFOzs7Ozs7Ozs7Ozs7MENBSWhFLDhEQUFDYix5Q0FBSUE7Z0NBQUFrQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUNIaUIsU0FBUztvQ0FDUEMsUUFBUUMsR0FBRyxDQUFDO2dDQUNkOzBDQUVBLDRFQUFDbkQsMkNBQU1BO29DQUFBOEIsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FDTG9CLE1BQUs7b0NBQ0w1QyxpQkFBZ0I7b0NBQ2hCNkMsYUFBWTtvQ0FDWkMsU0FBUztvQ0FDVEMsT0FBTzt3Q0FDTC9DLGlCQUFpQjtvQ0FDbkI7OENBRUEsNEVBQUNSLDJDQUFNQSxDQUFDd0QsS0FBSzt3Q0FBQ0MsV0FBVTt3Q0FBU2pELGlCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNekQsOERBQUNIOzs7Ozs7Ozs7OztBQUdQO0dBeEtnQmM7O1FBQ0N4Qix3REFBU0E7UUFDVk8sb0RBQWNBOzs7TUFGZGlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hcHAvZmVhdHVyZXMvd2FsbGV0L3NhZmUtc2NyZWVuLnRzeD9iOWI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7XG4gIEF2YXRhcixcbiAgQnV0dG9uLFxuICBIMyxcbiAgSDUsXG4gIE5hdkJhcixcbiAgUGFyYWdyYXBoLFxuICBTd2l0Y2hUaGVtZUJ1dHRvbixcbiAgWFN0YWNrLFxuICBZU3RhY2ssXG4gIElucHV0LFxufSBmcm9tICdAbXkvdWknXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICdzb2xpdG8vbmF2aWdhdGlvbidcbmltcG9ydCB7IFZpZXcsIFRleHQsIEltYWdlLCBIMSwgSDIsIHN0eWxlZCwgU3dpdGNoIH0gZnJvbSAndGFtYWd1aSdcblxuaW1wb3J0IHsgUHJlc3NhYmxlIH0gZnJvbSAncmVhY3QtbmF0aXZlJ1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENoZXZyb25SaWdodCB9IGZyb20gJ0B0YW1hZ3VpL2x1Y2lkZS1pY29ucydcbmltcG9ydCBtYWluQ29ubmV0SWNvbiBmcm9tICcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL21haW4tY29ubmVjdC5wbmcnXG5pbXBvcnQgY2hvb3NlMUljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy93YWxsZXQvY2hvb3NlMS5wbmcnXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ2FwcC9pMThuJ1xuaW1wb3J0IGV0aEljb24gZnJvbSAnLi4vLi4vLi4vYXNzZXRzL2ltYWdlcy93YWxsZXQvZXRoLnBuZydcbmltcG9ydCBhcnJvd1JpZ2h0SWNvbiBmcm9tICcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL3dhbGxldC9hcnJvd3JpZ2h0LnBuZydcblxuY29uc3QgTGluZSA9IHN0eWxlZChWaWV3LCB7XG4gIHdpZHRoOiAnMTAwJScsXG4gIGhlaWdodDogMSxcbiAgYmFja2dyb3VuZENvbG9yOiAnIzIxMjIyNCcsXG4gIG10OiAxMCxcbn0pXG5cbmNvbnN0IEFjdGl2ZVRleHQgPSBzdHlsZWQoVGV4dCwge1xuICBjb2xvcjogJyM0NTc1RkYnLFxuICBtYXJnaW5Cb3R0b206IDIsXG59KVxuXG5jb25zdCBVbmRlcmxpbmUgPSBzdHlsZWQoVmlldywge1xuICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgYm90dG9tOiAtMixcbiAgbGVmdDogMCxcbiAgcmlnaHQ6IDAsXG4gIGhlaWdodDogMixcbiAgYmFja2dyb3VuZENvbG9yOiAnIzQ1NzVGRicsXG59KVxuY29uc3QgVW5kZXJsaW5lYmxvY2sgPSBzdHlsZWQoVmlldywge1xuICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgYm90dG9tOiAtMixcbiAgbGVmdDogMCxcbiAgcmlnaHQ6IDAsXG4gIGhlaWdodDogMixcbn0pXG5cbmV4cG9ydCBmdW5jdGlvbiBTYWZlU2NyZWVuKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKClcbiAgY29uc3QgW2N1cnJlbnRUYWIsIHNldEN1cnJlbnRUYWJdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgW25ldExpc3QsIHNldE5ldExpc3RdID0gdXNlU3RhdGUoW1xuICAgIHtcbiAgICAgIGlkOiAxLFxuICAgICAgbmFtZTogJ0V0aGVyZXVtJyxcbiAgICAgIGljb246IGV0aEljb24uc3JjLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDIsXG4gICAgICBuYW1lOiAnQXJiaXRydW0nLFxuICAgICAgaWNvbjogZXRoSWNvbi5zcmMsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogMyxcbiAgICAgIG5hbWU6ICdBdmFsYW5jaGUgQy1DaGFpbicsXG4gICAgICBpY29uOiBldGhJY29uLnNyYyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiA0LFxuICAgICAgbmFtZTogJ0Jhc2UnLFxuICAgICAgaWNvbjogZXRoSWNvbi5zcmMsXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogNSxcbiAgICAgIG5hbWU6ICdCTkIgKEJpbmFuY2UgU21hcnQpIENoYWluJyxcbiAgICAgIGljb246IGV0aEljb24uc3JjLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDYsXG4gICAgICBuYW1lOiAnRmFudG9tIE9wZXJhJyxcbiAgICAgIGljb246IGV0aEljb24uc3JjLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDcsXG4gICAgICBuYW1lOiAnR25vc2lzJyxcbiAgICAgIGljb246IGV0aEljb24uc3JjLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDgsXG4gICAgICBuYW1lOiAnT1AgTWFpbm5ldCcsXG4gICAgICBpY29uOiBldGhJY29uLnNyYyxcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiA5LFxuICAgICAgbmFtZTogJ0V0aGVyZXVtJyxcbiAgICAgIGljb246IGV0aEljb24uc3JjLFxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDEwLFxuICAgICAgbmFtZTogJ0V0aGVyZXVtJyxcbiAgICAgIGljb246IGV0aEljb24uc3JjLFxuICAgIH0sXG4gIF0pXG5cbiAgcmV0dXJuIChcbiAgICA8WVN0YWNrIGJnPVwiJGJhY2tncm91bmRcIiBoZWlnaHQ9ezgwMH0+XG4gICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIGl0ZW1zPVwiY2VudGVyXCIgcHI9ezE2fT5cbiAgICAgICAgPE5hdkJhciB0aXRsZT17dCgnbmF2aWdhdGlvbi5zZWN1cml0eScpIHx8ICflronlhagnfSBvbkJhY2s9eygpID0+IHJvdXRlci5iYWNrKCl9IC8+XG4gICAgICAgIHsvKiA8SW1hZ2Ugc291cmNlPXtjaG9vc2UxSWNvbi5zcmN9IHdpZHRoPXsxNn0gaGVpZ2h0PXsxNn0gLz4gKi99XG4gICAgICA8L1hTdGFjaz5cbiAgICAgIDxZU3RhY2sgcHg9ezE2fSBtdD17MzB9IG1iPXsyMH0+XG4gICAgICAgIDxUZXh0IGZvbnRTaXplPXsxOH0gZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICDlpIfku71cbiAgICAgICAgPC9UZXh0PlxuICAgICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIG10PXsyMH0+XG4gICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAg6ZKx5YyFMVxuICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICA8WFN0YWNrIGl0ZW1zPVwiY2VudGVyXCI+XG4gICAgICAgICAgICA8VGV4dFxuICAgICAgICAgICAgICBjb2xvcj1cIiNDNzU0NUVcIlxuICAgICAgICAgICAgICBmb250U2l6ZT17MTJ9XG4gICAgICAgICAgICAgIG1yPXsxMH1cbiAgICAgICAgICAgICAgYmc9XCJyZ2JhKDE5OSw4NCw5NCwwLjE2KVwiXG4gICAgICAgICAgICAgIHdpZHRoPXs0NX1cbiAgICAgICAgICAgICAgaGVpZ2h0PXsyMH1cbiAgICAgICAgICAgICAgcm91bmRlZD17NH1cbiAgICAgICAgICAgICAgdGV4dEFsaWduPVwiY2VudGVyXCJcbiAgICAgICAgICAgICAgbGluZUhlaWdodD17MjB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOacquWkh+S7vVxuICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgPEltYWdlIHNvdXJjZT17YXJyb3dSaWdodEljb24uc3JjfSB3aWR0aD17Nn0gaGVpZ2h0PXs5fSAvPlxuICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICA8L1hTdGFjaz5cbiAgICAgIDwvWVN0YWNrPlxuICAgICAgPExpbmUgLz5cblxuICAgICAgPFlTdGFjayBweD17MTZ9IG10PXszMH0gbWI9ezIwfT5cbiAgICAgICAgPFRleHQgZm9udFNpemU9ezE4fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgIOWuieWFqOmUgeWumlxuICAgICAgICA8L1RleHQ+XG4gICAgICAgIDxYU3RhY2sganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezIwfT5cbiAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCI+XG4gICAgICAgICAgICDplIHlrprmlrnlvI9cbiAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgPFhTdGFjayBpdGVtcz1cImNlbnRlclwiPlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PXs1MDB9IGNvbG9yPVwiJGFjY2VudDExXCI+XG4gICAgICAgICAgICAgIOWvhueggVxuICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDwvWFN0YWNrPlxuICAgICAgICA8L1hTdGFjaz5cbiAgICAgIDwvWVN0YWNrPlxuICAgICAgPExpbmUgLz5cbiAgICAgIDxZU3RhY2sgcHg9ezE2fSBtdD17MzB9IG1iPXsyMH0+XG4gICAgICAgIDxUZXh0IGZvbnRTaXplPXsxOH0gZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICDku6XkuIvmg4XlhrXpnIDopoHop6PplIFcbiAgICAgICAgPC9UZXh0PlxuICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGNvbG9yPVwiJGFjY2VudDExXCIgbXQ9ezEwfT5cbiAgICAgICAgICDlv4Xpobvoh7PlsJHpgInmi6nkuIDkuKrpgInpobnjgIJcbiAgICAgICAgPC9UZXh0PlxuICAgICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIG10PXsyMH0+XG4gICAgICAgICAgPFRleHQgZm9udFNpemU9ezE0fSBmb250V2VpZ2h0PVwiYm9sZFwiPlxuICAgICAgICAgICAg5omT5byA5bqU55SoXG4gICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDxWaWV3XG4gICAgICAgICAgICBvblByZXNzPXsoKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKDExKVxuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICAgIHNpemU9XCIkNFwiXG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcj1cIiMzODczRjVcIlxuICAgICAgICAgICAgICBib3JkZXJDb2xvcj1cIiMzODczRjVcIlxuICAgICAgICAgICAgICBjaGVja2VkPXt0cnVlfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyMzODczRjUnLFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8U3dpdGNoLlRodW1iIGFuaW1hdGlvbj1cImJvdW5jeVwiIGJhY2tncm91bmRDb2xvcj1cIndoaXRlXCIgLz5cbiAgICAgICAgICAgIDwvU3dpdGNoPlxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgIDxYU3RhY2sganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezIwfT5cbiAgICAgICAgICA8Vmlldz5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNH0gZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICAgICAg6L+b6KGM5Lqk5piTXG4gICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTR9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCIkYWNjZW50MTFcIiBtdD17Mn0+XG4gICAgICAgICAgICAgIOS7heW9seWTjeagh+WHhumSseWMhVxuICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICA8Vmlld1xuICAgICAgICAgICAgb25QcmVzcz17KCkgPT4ge1xuICAgICAgICAgICAgICBjb25zb2xlLmxvZygxMSlcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgICBzaXplPVwiJDRcIlxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I9XCIjMzg3M0Y1XCJcbiAgICAgICAgICAgICAgYm9yZGVyQ29sb3I9XCIjMzg3M0Y1XCJcbiAgICAgICAgICAgICAgY2hlY2tlZD17dHJ1ZX1cbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMzg3M0Y1JyxcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFN3aXRjaC5UaHVtYiBhbmltYXRpb249XCJib3VuY3lcIiBiYWNrZ3JvdW5kQ29sb3I9XCJ3aGl0ZVwiIC8+XG4gICAgICAgICAgICA8L1N3aXRjaD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgPC9ZU3RhY2s+XG5cbiAgICAgIDxMaW5lIC8+XG4gICAgPC9ZU3RhY2s+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJOYXZCYXIiLCJYU3RhY2siLCJZU3RhY2siLCJ1c2VSb3V0ZXIiLCJWaWV3IiwiVGV4dCIsIkltYWdlIiwic3R5bGVkIiwiU3dpdGNoIiwidXNlU3RhdGUiLCJ1c2VUcmFuc2xhdGlvbiIsImV0aEljb24iLCJhcnJvd1JpZ2h0SWNvbiIsIkxpbmUiLCJ3aWR0aCIsImhlaWdodCIsImJhY2tncm91bmRDb2xvciIsIm10IiwiQWN0aXZlVGV4dCIsImNvbG9yIiwibWFyZ2luQm90dG9tIiwiVW5kZXJsaW5lIiwicG9zaXRpb24iLCJib3R0b20iLCJsZWZ0IiwicmlnaHQiLCJVbmRlcmxpbmVibG9jayIsIlNhZmVTY3JlZW4iLCJyb3V0ZXIiLCJ0IiwiY3VycmVudFRhYiIsInNldEN1cnJlbnRUYWIiLCJuZXRMaXN0Iiwic2V0TmV0TGlzdCIsImlkIiwibmFtZSIsImljb24iLCJzcmMiLCJkYXRhLWF0IiwiZGF0YS1pbiIsImRhdGEtaXMiLCJiZyIsImp1c3RpZnlDb250ZW50IiwiaXRlbXMiLCJwciIsInRpdGxlIiwib25CYWNrIiwiYmFjayIsInB4IiwibWIiLCJmb250U2l6ZSIsImZvbnRXZWlnaHQiLCJtciIsInJvdW5kZWQiLCJ0ZXh0QWxpZ24iLCJsaW5lSGVpZ2h0Iiwic291cmNlIiwib25QcmVzcyIsImNvbnNvbGUiLCJsb2ciLCJzaXplIiwiYm9yZGVyQ29sb3IiLCJjaGVja2VkIiwic3R5bGUiLCJUaHVtYiIsImFuaW1hdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/safe-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsafe.tsx&page=%2Fwallet%2Fsafe!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);