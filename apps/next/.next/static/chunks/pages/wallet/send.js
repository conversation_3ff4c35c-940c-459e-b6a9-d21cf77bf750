/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/send"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsend.tsx&page=%2Fwallet%2Fsend!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsend.tsx&page=%2Fwallet%2Fsend! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/send\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/send.tsx */ \"./pages/wallet/send.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/send\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRnNlbmQudHN4JnBhZ2U9JTJGd2FsbGV0JTJGc2VuZCEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyx3REFBeUI7QUFDaEQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2RhNmYiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvc2VuZFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L3NlbmQudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvc2VuZFwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsend.tsx&page=%2Fwallet%2Fsend!\n"));

/***/ }),

/***/ "../../packages/assets/images/buy/buy12.png":
/*!**************************************************!*\
  !*** ../../packages/assets/images/buy/buy12.png ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy12.1678ebed.png\",\"height\":64,\"width\":64,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy12.1678ebed.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTIucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTIucG5nPzRmMmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2J1eTEyLjE2NzhlYmVkLnBuZ1wiLFwiaGVpZ2h0XCI6NjQsXCJ3aWR0aFwiOjY0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTEyLjE2NzhlYmVkLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/buy/buy12.png\n"));

/***/ }),

/***/ "../../packages/assets/images/buy/buy13.png":
/*!**************************************************!*\
  !*** ../../packages/assets/images/buy/buy13.png ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy13.6d7c50e1.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy13.6d7c50e1.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTMucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTMucG5nPzY1ZGEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2J1eTEzLjZkN2M1MGUxLnBuZ1wiLFwiaGVpZ2h0XCI6MjQsXCJ3aWR0aFwiOjI0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTEzLjZkN2M1MGUxLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/buy/buy13.png\n"));

/***/ }),

/***/ "../../packages/assets/images/buy/buy14.png":
/*!**************************************************!*\
  !*** ../../packages/assets/images/buy/buy14.png ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy14.a7929604.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy14.a7929604.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTQucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTQucG5nPzEyOTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2J1eTE0LmE3OTI5NjA0LnBuZ1wiLFwiaGVpZ2h0XCI6MjQsXCJ3aWR0aFwiOjI0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTE0LmE3OTI5NjA0LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/buy/buy14.png\n"));

/***/ }),

/***/ "../../packages/assets/images/buy/buy15.png":
/*!**************************************************!*\
  !*** ../../packages/assets/images/buy/buy15.png ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy15.1f057604.png\",\"height\":24,\"width\":24,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy15.1f057604.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTUucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTUucG5nPzBiMWIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2J1eTE1LjFmMDU3NjA0LnBuZ1wiLFwiaGVpZ2h0XCI6MjQsXCJ3aWR0aFwiOjI0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTE1LjFmMDU3NjA0LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/buy/buy15.png\n"));

/***/ }),

/***/ "../../packages/assets/images/buy/buy16.png":
/*!**************************************************!*\
  !*** ../../packages/assets/images/buy/buy16.png ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy16.16f56a55.png\",\"height\":26,\"width\":19,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy16.16f56a55.png&w=6&q=70\",\"blurWidth\":6,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTYucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTYucG5nPzBhN2EiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2J1eTE2LjE2ZjU2YTU1LnBuZ1wiLFwiaGVpZ2h0XCI6MjYsXCJ3aWR0aFwiOjE5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTE2LjE2ZjU2YTU1LnBuZyZ3PTYmcT03MFwiLFwiYmx1cldpZHRoXCI6NixcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/buy/buy16.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/arrowright.png":
/*!**********************************************************!*\
  !*** ../../packages/assets/images/wallet/arrowright.png ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/arrowright.f0d2eb68.png\",\"height\":22,\"width\":14,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Farrowright.f0d2eb68.png&w=5&q=70\",\"blurWidth\":5,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYXJyb3dyaWdodC5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9hcnJvd3JpZ2h0LnBuZz8wNDgwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9hcnJvd3JpZ2h0LmYwZDJlYjY4LnBuZ1wiLFwiaGVpZ2h0XCI6MjIsXCJ3aWR0aFwiOjE0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFycm93cmlnaHQuZjBkMmViNjgucG5nJnc9NSZxPTcwXCIsXCJibHVyV2lkdGhcIjo1LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/arrowright.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/bnb.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/wallet/bnb.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/bnb.2bf43860.png\",\"height\":64,\"width\":64,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbnb.2bf43860.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYm5iLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TEFBd0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2JuYi5wbmc/YjI5ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvYm5iLjJiZjQzODYwLnBuZ1wiLFwiaGVpZ2h0XCI6NjQsXCJ3aWR0aFwiOjY0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJuYi4yYmY0Mzg2MC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/bnb.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/eth.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/wallet/eth.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/eth.b241a439.png\",\"height\":64,\"width\":64,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Feth.b241a439.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZXRoLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TEFBd0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2V0aC5wbmc/NjAwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZXRoLmIyNDFhNDM5LnBuZ1wiLFwiaGVpZ2h0XCI6NjQsXCJ3aWR0aFwiOjY0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmV0aC5iMjQxYTQzOS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/eth.png\n"));

/***/ }),

/***/ "./pages/wallet/send.tsx":
/*!*******************************!*\
  !*** ./pages/wallet/send.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_send_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/send-screen */ \"../../packages/app/features/wallet/send-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_send_screen__WEBPACK_IMPORTED_MODULE_1__.SendScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/send.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvc2VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDNEQ7QUFFN0MsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELHVFQUFVQTs7Ozs7QUFDcEI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3dhbGxldC9zZW5kLnRzeD8wNTNkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgU2VuZFNjcmVlbiB9IGZyb20gJ2FwcC9mZWF0dXJlcy93YWxsZXQvc2VuZC1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8U2VuZFNjcmVlbiAvPlxufSJdLCJuYW1lcyI6WyJTZW5kU2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/send.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/send-screen.tsx":
/*!**********************************************************!*\
  !*** ../../packages/app/features/wallet/send-screen.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SendScreen: function() { return /* binding */ SendScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! app/stores/transactionStore */ \"../../packages/app/stores/transactionStore.ts\");\n/* harmony import */ var app_services_transactionService__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! app/services/transactionService */ \"../../packages/app/services/transactionService.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_buy_buy15_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/buy/buy15.png */ \"../../packages/assets/images/buy/buy15.png\");\n/* harmony import */ var _assets_images_buy_buy12_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/buy/buy12.png */ \"../../packages/assets/images/buy/buy12.png\");\n/* harmony import */ var _assets_images_buy_buy14_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/buy/buy14.png */ \"../../packages/assets/images/buy/buy14.png\");\n/* harmony import */ var _assets_images_buy_buy13_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/buy/buy13.png */ \"../../packages/assets/images/buy/buy13.png\");\n/* harmony import */ var _assets_images_wallet_bnb_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/wallet/bnb.png */ \"../../packages/assets/images/wallet/bnb.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../assets/images/wallet/arrowright.png */ \"../../packages/assets/images/wallet/arrowright.png\");\n/* harmony import */ var _assets_images_buy_buy16_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../assets/images/buy/buy16.png */ \"../../packages/assets/images/buy/buy16.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst Line = (0,tamagui__WEBPACK_IMPORTED_MODULE_10__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_10__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Line;\nfunction SendScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_12__.useWalletStore)();\n    const transactionStore = (0,app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_13__.useTransactionStore)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation)();\n    const [amount, setAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [toAddress, setToAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [estimatedFee, setEstimatedFee] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const currentAccount = walletStore.currentAccount;\n    // 获取当前钱包中可用的链\n    const getAvailableChains = ()=>{\n        if (!currentAccount) return [];\n        const chains = [];\n        if (currentAccount.eth) chains.push({\n            key: \"eth\",\n            name: \"Ethereum\",\n            symbol: \"ETH\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"].src\n        });\n        if (currentAccount.bsc) chains.push({\n            key: \"bsc\",\n            name: \"BSC\",\n            symbol: \"BNB\",\n            icon: _assets_images_wallet_bnb_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src\n        });\n        if (currentAccount.btc) chains.push({\n            key: \"btc\",\n            name: \"Bitcoin\",\n            symbol: \"BTC\",\n            icon: _assets_images_wallet_bnb_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src\n        });\n        if (currentAccount.solana) chains.push({\n            key: \"solana\",\n            name: \"Solana\",\n            symbol: \"SOL\",\n            icon: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"].src\n        });\n        return chains;\n    };\n    const availableChains = getAvailableChains();\n    // 设置默认选中的链（优先选择第一个可用的链）\n    const [selectedChain, setSelectedChain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        if (availableChains.length > 0) {\n            return availableChains[0].key;\n        }\n        return \"eth\"; // 默认值\n    });\n    // 获取当前链的余额和信息\n    const getCurrentChainInfo = ()=>{\n        if (!currentAccount || !currentAccount[selectedChain]) {\n            return null;\n        }\n        const chainInfo = currentAccount[selectedChain];\n        const symbols = {\n            eth: \"ETH\",\n            bsc: \"BNB\",\n            btc: \"BTC\",\n            solana: \"SOL\"\n        };\n        return {\n            ...chainInfo,\n            symbol: symbols[selectedChain],\n            balance: chainInfo.balance || \"0\",\n            address: chainInfo.address || \"\"\n        };\n    };\n    // 获取链图标\n    const getChainIcon = ()=>{\n        switch(selectedChain){\n            case \"eth\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"].src;\n            case \"bsc\":\n                return _assets_images_wallet_bnb_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src;\n            case \"btc\":\n                return _assets_images_wallet_bnb_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src;\n            // 暂时使用 BNB 图标\n            case \"solana\":\n                return _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"].src;\n            // 暂时使用 ETH 图标\n            default:\n                return _assets_images_wallet_bnb_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src;\n        }\n    };\n    // 获取链名称\n    const getChainName = ()=>{\n        switch(selectedChain){\n            case \"eth\":\n                return \"Ethereum\";\n            case \"bsc\":\n                return \"BSC\";\n            case \"btc\":\n                return \"Bitcoin\";\n            case \"solana\":\n                return \"Solana\";\n            default:\n                return \"BSC\";\n        }\n    };\n    // 获取链符号\n    const getChainSymbol = ()=>{\n        switch(selectedChain){\n            case \"eth\":\n                return \"ETH\";\n            case \"bsc\":\n                return \"BNB\";\n            case \"btc\":\n                return \"BTC\";\n            case \"solana\":\n                return \"SOL\";\n            default:\n                return \"BNB\";\n        }\n    };\n    const chainInfo = getCurrentChainInfo();\n    // 如果没有当前账户，显示提示\n    if (!currentAccount || !currentAccount[selectedChain]) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n            \"data-at\": \"send-screen.tsx:147\",\n            \"data-in\": \"SendScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            px: 16,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.NavBar, {\n                    title: t(\"wallet.send\") || \"发送\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n                    \"data-at\": \"send-screen.tsx:149\",\n                    \"data-in\": \"SendScreen\",\n                    \"data-is\": \"YStack\",\n                    mt: 100,\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                            \"data-at\": \"send-screen.tsx:150\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 18,\n                            fontWeight: \"bold\",\n                            mb: 10,\n                            children: \"未找到钱包账户\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                            \"data-at\": \"send-screen.tsx:153\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 14,\n                            textAlign: \"center\",\n                            mb: 20,\n                            children: \"请先创建或导入钱包账户\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                            \"data-at\": \"send-screen.tsx:156\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"Button\",\n                            onPress: ()=>router.push(\"/wallet/new\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"send-screen.tsx:157\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"Text\",\n                                color: \"white\",\n                                children: \"创建钱包\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n            lineNumber: 157,\n            columnNumber: 12\n        }, this);\n    }\n    // 地址验证函数\n    const validateAddress = (address, chain)=>{\n        if (!address) return false;\n        try {\n            switch(chain){\n                case \"eth\":\n                case \"bsc\":\n                    // EVM地址验证\n                    return /^0x[a-fA-F0-9]{40}$/.test(address);\n                case \"solana\":\n                    // Solana地址验证 (Base58格式，32-44个字符)\n                    return /^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(address);\n                case \"btc\":\n                    // BTC地址验证 (简化版)\n                    return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) || /^bc1[a-z0-9]{39,59}$/.test(address) || /^[2mn][a-km-zA-HJ-NP-Z1-9]{33,34}$/.test(address);\n                default:\n                    return false;\n            }\n        } catch (e) {\n            return false;\n        }\n    };\n    // 金额验证函数  \n    const validateAmount = (value)=>{\n        if (!value || value.trim() === \"\") {\n            return {\n                isValid: false,\n                error: t(\"form.required\") || \"请输入金额\"\n            };\n        }\n        const num = parseFloat(value);\n        if (isNaN(num) || !isFinite(num)) {\n            return {\n                isValid: false,\n                error: \"请输入有效的数字\"\n            };\n        }\n        if (num <= 0) {\n            return {\n                isValid: false,\n                error: \"金额必须大于0\"\n            };\n        }\n        if (num > parseFloat((chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.balance) || \"0\")) {\n            return {\n                isValid: false,\n                error: t(\"wallet.insufficientBalance\") || \"余额不足\"\n            };\n        }\n        // 检查小数位数 (最多8位)\n        const decimalPlaces = (value.split(\".\")[1] || \"\").length;\n        if (decimalPlaces > 8) {\n            return {\n                isValid: false,\n                error: \"小数位数不能超过8位\"\n            };\n        }\n        return {\n            isValid: true\n        };\n    };\n    // 实时验证\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (amount) {\n            const validation = validateAmount(amount);\n            if (!validation.isValid && validation.error !== (t(\"wallet.insufficientBalance\") || \"余额不足\")) {\n                setError(validation.error || \"\");\n            } else if (validation.isValid) {\n                setError(\"\");\n            }\n        }\n    }, [\n        amount,\n        chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.balance\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (toAddress) {\n            var _chainInfo_address;\n            if (!validateAddress(toAddress, selectedChain)) {\n                setError(\"无效的\".concat(selectedChain.toUpperCase(), \"地址格式\"));\n            } else if (toAddress.toLowerCase() === (chainInfo === null || chainInfo === void 0 ? void 0 : (_chainInfo_address = chainInfo.address) === null || _chainInfo_address === void 0 ? void 0 : _chainInfo_address.toLowerCase())) {\n                setError(\"不能给自己转账\");\n            } else {\n                setError(\"\");\n            }\n        }\n    }, [\n        toAddress,\n        selectedChain,\n        chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.address\n    ]);\n    // 估算手续费\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (amount && toAddress && (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.address) && validateAddress(toAddress, selectedChain) && validateAmount(amount).isValid) {\n            setIsLoading(true);\n            (0,app_services_transactionService__WEBPACK_IMPORTED_MODULE_16__.estimateTransactionFee)({\n                fromAddress: chainInfo.address,\n                toAddress,\n                amount,\n                chain: selectedChain\n            }).then((result)=>{\n                setEstimatedFee(result.estimatedFee);\n            }).catch((error)=>{\n                console.error(\"估算手续费失败:\", error);\n                setEstimatedFee(\"0.001\"); // 设置默认手续费\n            }).finally(()=>{\n                setIsLoading(false);\n            });\n        } else {\n            setEstimatedFee(\"0\");\n        }\n    }, [\n        amount,\n        toAddress,\n        selectedChain,\n        chainInfo\n    ]);\n    // 发送交易\n    const handleSendTransaction = async ()=>{\n        // 基础验证\n        if (!chainInfo || !chainInfo.address || !chainInfo.privateKey) {\n            setError(\"钱包信息不完整\");\n            return;\n        }\n        if (!amount || !toAddress) {\n            setError(t(\"form.required\") || \"请填写完整信息\");\n            return;\n        }\n        // 金额验证\n        const amountValidation = validateAmount(amount);\n        if (!amountValidation.isValid) {\n            setError(amountValidation.error || \"金额验证失败\");\n            return;\n        }\n        // 地址验证\n        if (!validateAddress(toAddress, selectedChain)) {\n            setError(\"无效的\".concat(selectedChain.toUpperCase(), \"地址格式\"));\n            return;\n        }\n        // 检查是否自己给自己转账\n        if (toAddress.toLowerCase() === chainInfo.address.toLowerCase()) {\n            setError(\"不能给自己转账\");\n            return;\n        }\n        // 检查链是否支持\n        if (selectedChain === \"btc\") {\n            setError(\"BTC转账功能暂未开放\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        console.log(\"准备发送 \".concat(selectedChain.toUpperCase(), \" 交易:\"), {\n            from: chainInfo.address,\n            to: toAddress,\n            amount: \"\".concat(amount, \" \").concat(chainInfo.symbol),\n            chain: selectedChain\n        });\n        try {\n            // 添加待确认交易记录\n            transactionStore.addTransaction({\n                txHash: \"\",\n                // 暂时为空，发送成功后更新\n                fromAddress: chainInfo.address || \"\",\n                toAddress,\n                amount,\n                chain: selectedChain,\n                status: \"pending\",\n                type: \"send\",\n                symbol: chainInfo.symbol || selectedChain.toUpperCase()\n            });\n            // 发送交易\n            const result = await (0,app_services_transactionService__WEBPACK_IMPORTED_MODULE_16__.sendTransaction)({\n                fromAddress: chainInfo.address || \"\",\n                toAddress,\n                amount,\n                privateKey: chainInfo.privateKey || \"\",\n                chain: selectedChain\n            });\n            if (result.success && result.txHash) {\n                console.log(\"交易发送成功:\", result.txHash);\n                // 更新交易记录\n                const transactions = transactionStore.transactions;\n                const pendingTx = transactions.find((tx)=>tx.fromAddress === chainInfo.address && tx.toAddress === toAddress && tx.amount === amount && tx.status === \"pending\" && !tx.txHash);\n                if (pendingTx) {\n                    transactionStore.updateTransaction(pendingTx.id, {\n                        txHash: result.txHash,\n                        status: \"confirmed\",\n                        gasUsed: result.gasUsed,\n                        blockNumber: result.blockNumber\n                    });\n                }\n                // 清空表单\n                setAmount(\"\");\n                setToAddress(\"\");\n                setError(\"\");\n                // 延迟刷新余额，避免过于频繁的请求\n                setTimeout(()=>{\n                    walletStore.fetchAllBalances();\n                }, 3000);\n                // 跳转到交易结果页面\n                const params = new URLSearchParams({\n                    txHash: result.txHash,\n                    amount: amount,\n                    symbol: chainInfo.symbol || selectedChain.toUpperCase(),\n                    toAddress: toAddress,\n                    status: \"success\",\n                    chain: selectedChain\n                });\n                router.push(\"/wallet/transactionResult?\".concat(params.toString()));\n            } else {\n                console.error(\"交易发送失败:\", result.error);\n                setError(result.error || \"交易发送失败，请重试\");\n                // 更新失败的交易记录\n                const transactions = transactionStore.transactions;\n                const pendingTx = transactions.find((tx)=>tx.fromAddress === chainInfo.address && tx.toAddress === toAddress && tx.amount === amount && tx.status === \"pending\" && !tx.txHash);\n                if (pendingTx) {\n                    transactionStore.updateTransaction(pendingTx.id, {\n                        status: \"failed\",\n                        error: result.error\n                    });\n                }\n            }\n        } catch (error) {\n            var _error_message, _error_message1, _error_message2;\n            console.error(\"交易处理异常:\", error);\n            let errorMessage = \"交易处理失败，请重试\";\n            if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"network\")) {\n                errorMessage = \"网络连接异常，请检查网络后重试\";\n            } else if ((_error_message1 = error.message) === null || _error_message1 === void 0 ? void 0 : _error_message1.includes(\"timeout\")) {\n                errorMessage = \"网络超时，请稍后重试\";\n            } else if ((_error_message2 = error.message) === null || _error_message2 === void 0 ? void 0 : _error_message2.includes(\"insufficient\")) {\n                errorMessage = \"余额不足\";\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            setError(errorMessage);\n            // 更新失败的交易记录\n            const transactions = transactionStore.transactions;\n            const pendingTx = transactions.find((tx)=>tx.fromAddress === (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.address) && tx.toAddress === toAddress && tx.amount === amount && tx.status === \"pending\" && !tx.txHash);\n            if (pendingTx) {\n                transactionStore.updateTransaction(pendingTx.id, {\n                    status: \"failed\",\n                    error: errorMessage\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!chainInfo) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n            \"data-at\": \"send-screen.tsx:439\",\n            \"data-in\": \"SendScreen\",\n            \"data-is\": \"YStack\",\n            bg: \"$background\",\n            flex: 1,\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                \"data-at\": \"send-screen.tsx:440\",\n                \"data-in\": \"SendScreen\",\n                \"data-is\": \"Text\",\n                color: \"white\",\n                children: \"请先选择钱包账户\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 429,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n            lineNumber: 428,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n        \"data-at\": \"send-screen.tsx:446\",\n        \"data-in\": \"SendScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        height: 800,\n        position: \"relative\",\n        children: [\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.View, {\n                \"data-at\": \"send-screen.tsx:449-459\",\n                \"data-in\": \"SendScreen\",\n                \"data-is\": \"View\",\n                position: \"absolute\",\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                backgroundColor: \"rgba(0, 0, 0, 0.7)\",\n                zIndex: 1000,\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n                    \"data-at\": \"send-screen.tsx:460\",\n                    \"data-in\": \"SendScreen\",\n                    \"data-is\": \"YStack\",\n                    alignItems: \"center\",\n                    backgroundColor: \"#1A1A1A\",\n                    borderRadius: 12,\n                    padding: 24,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.View, {\n                            \"data-at\": \"send-screen.tsx:461-469\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"View\",\n                            width: 40,\n                            height: 40,\n                            borderRadius: 20,\n                            borderWidth: 3,\n                            borderColor: \"#4575FF\",\n                            borderTopColor: \"transparent\",\n                            mb: 16\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                            \"data-at\": \"send-screen.tsx:470\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"Text\",\n                            color: \"white\",\n                            fontSize: 16,\n                            fontWeight: \"bold\",\n                            children: t(\"common.loading\") || \"发送中...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                            \"data-at\": \"send-screen.tsx:473\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"Text\",\n                            color: \"#8B8F9A\",\n                            fontSize: 14,\n                            mt: 8,\n                            textAlign: \"center\",\n                            children: \"请稍候，正在处理您的交易\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 434,\n                columnNumber: 20\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.NavBar, {\n                title: t(\"wallet.send\") || \"发送\",\n                onBack: ()=>router.back()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 446,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                \"data-at\": \"send-screen.tsx:481\",\n                \"data-in\": \"SendScreen\",\n                \"data-is\": \"XStack\",\n                mt: 20,\n                position: \"relative\",\n                px: 16,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                        \"data-at\": \"send-screen.tsx:482-498\",\n                        \"data-in\": \"SendScreen\",\n                        \"data-is\": \"Input\",\n                        placeholder: (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.symbol) || getChainSymbol(),\n                        value: amount,\n                        onChangeText: setAmount,\n                        width: \"100%\",\n                        height: 100,\n                        bg: \"$black1\",\n                        borderColor: \"$black1\",\n                        borderRadius: 10,\n                        fontSize: 54,\n                        fontWeight: \"bold\",\n                        p: 0,\n                        color: \"#3C72F9\",\n                        borderWidth: 0,\n                        focusStyle: {\n                            borderColor: \"transparent\",\n                            outlineWidth: 0\n                        },\n                        keyboardType: \"numeric\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_17__.Pressable, {\n                        onPress: ()=>{\n                            // 设置为当前链的最大余额（减去一些手续费）\n                            const maxBalance = parseFloat((chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.balance) || \"0\");\n                            const feeReserve = 0.001; // 预留手续费\n                            const maxAmount = Math.max(0, maxBalance - feeReserve);\n                            setAmount(maxAmount.toString());\n                        },\n                        style: {\n                            position: \"absolute\",\n                            right: 16,\n                            top: 40,\n                            backgroundColor: \"#282B32\",\n                            borderWidth: 1,\n                            borderColor: \"#92929A\",\n                            width: 53,\n                            height: 28,\n                            borderRadius: 30,\n                            justifyContent: \"center\",\n                            alignItems: \"center\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                            \"data-at\": \"send-screen.tsx:521\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 12,\n                            color: \"white\",\n                            children: t(\"wallet.max\") || \"上限\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 447,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n                \"data-at\": \"send-screen.tsx:526\",\n                \"data-in\": \"SendScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 20,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_17__.Pressable, {\n                        onPress: ()=>{\n                            // 在可用的链之间切换\n                            if (availableChains.length > 1) {\n                                const currentIndex = availableChains.findIndex((chain)=>chain.key === selectedChain);\n                                const nextIndex = (currentIndex + 1) % availableChains.length;\n                                setSelectedChain(availableChains[nextIndex].key);\n                                setError(\"\"); // 清除错误\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                            \"data-at\": \"send-screen.tsx:545\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"XStack\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            mb: 10,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                                    \"data-at\": \"send-screen.tsx:546\",\n                                    \"data-in\": \"SendScreen\",\n                                    \"data-is\": \"XStack\",\n                                    position: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"send-screen.tsx:547\",\n                                            \"data-in\": \"SendScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: getChainIcon(),\n                                            width: 32,\n                                            height: 32,\n                                            mr: 10\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"send-screen.tsx:548-556\",\n                                            \"data-in\": \"SendScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_buy_buy15_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                            width: 16,\n                                            height: 16,\n                                            mr: 16,\n                                            position: \"absolute\",\n                                            right: 20,\n                                            top: 20\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"send-screen.tsx:557\",\n                                            \"data-in\": \"SendScreen\",\n                                            \"data-is\": \"Text\",\n                                            color: \"white\",\n                                            children: (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.symbol) || getChainSymbol()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                                    \"data-at\": \"send-screen.tsx:559\",\n                                    \"data-in\": \"SendScreen\",\n                                    \"data-is\": \"XStack\",\n                                    alignItems: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"send-screen.tsx:560\",\n                                            \"data-in\": \"SendScreen\",\n                                            \"data-is\": \"Text\",\n                                            mr: 10,\n                                            fontSize: 12,\n                                            fontWeight: \"bold\",\n                                            color: \"white\",\n                                            children: [\n                                                (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.balance) || \"0\",\n                                                \" \",\n                                                (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.symbol) || getChainSymbol()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                            \"data-at\": \"send-screen.tsx:563\",\n                                            \"data-in\": \"SendScreen\",\n                                            \"data-is\": \"Image\",\n                                            source: _assets_images_wallet_arrowright_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"].src,\n                                            width: 5,\n                                            height: 9\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                        \"data-at\": \"send-screen.tsx:567\",\n                        \"data-in\": \"SendScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_buy_buy16_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"].src,\n                        width: 5,\n                        height: 12,\n                        ml: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                        \"data-at\": \"send-screen.tsx:568\",\n                        \"data-in\": \"SendScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        mb: 10,\n                        mt: 10,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                                \"data-at\": \"send-screen.tsx:569\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"XStack\",\n                                position: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"send-screen.tsx:570\",\n                                        \"data-in\": \"SendScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_buy_buy12_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                        width: 32,\n                                        height: 32,\n                                        mr: 10\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"send-screen.tsx:571-579\",\n                                        \"data-in\": \"SendScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_buy_buy15_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                        width: 16,\n                                        height: 16,\n                                        mr: 16,\n                                        position: \"absolute\",\n                                        right: -10,\n                                        top: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                                \"data-at\": \"send-screen.tsx:581-590\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"Input\",\n                                placeholder: t(\"wallet.enterRecipientAddress\") || \"输入接收地址\",\n                                value: toAddress,\n                                onChangeText: setToAddress,\n                                fontSize: 14,\n                                color: \"white\",\n                                borderWidth: 0,\n                                backgroundColor: \"transparent\",\n                                flex: 1\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n                \"data-at\": \"send-screen.tsx:594\",\n                \"data-in\": \"SendScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                    \"data-at\": \"send-screen.tsx:595\",\n                    \"data-in\": \"SendScreen\",\n                    \"data-is\": \"XStack\",\n                    mt: 30,\n                    alignItems: \"center\",\n                    justifyContent: \"space-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                            \"data-at\": \"send-screen.tsx:596\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"XStack\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                    \"data-at\": \"send-screen.tsx:597\",\n                                    \"data-in\": \"SendScreen\",\n                                    \"data-is\": \"Image\",\n                                    source: getChainIcon(),\n                                    width: 32,\n                                    height: 32,\n                                    mr: 6\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"send-screen.tsx:598\",\n                                    \"data-in\": \"SendScreen\",\n                                    \"data-is\": \"Text\",\n                                    color: \"white\",\n                                    fontSize: 14,\n                                    fontWeight: \"bold\",\n                                    children: (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.symbol) || getChainSymbol()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                            \"data-at\": \"send-screen.tsx:602\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"XStack\",\n                            justifyContent: \"flex-end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.View, {\n                                    \"data-at\": \"send-screen.tsx:603\",\n                                    \"data-in\": \"SendScreen\",\n                                    \"data-is\": \"View\",\n                                    mr: 5,\n                                    flexDirection: \"column\",\n                                    alignItems: \"flex-end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"send-screen.tsx:604\",\n                                            \"data-in\": \"SendScreen\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 12,\n                                            fontWeight: \"bold\",\n                                            children: [\n                                                (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.balance) || \"0\",\n                                                \" \",\n                                                (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.symbol) || getChainSymbol()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"send-screen.tsx:607\",\n                                            \"data-in\": \"SendScreen\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 12,\n                                            color: \"#8B8F9A\",\n                                            children: \"可用\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_18__.ChevronRight, {\n                                    size: 20,\n                                    color: \"$white6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 517,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n                \"data-at\": \"send-screen.tsx:616\",\n                \"data-in\": \"SendScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                        \"data-at\": \"send-screen.tsx:617\",\n                        \"data-in\": \"SendScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 30,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"send-screen.tsx:618\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: t(\"wallet.usingWallet\") || \"使用的钱包\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 541,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                                \"data-at\": \"send-screen.tsx:621\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                        \"data-at\": \"send-screen.tsx:622\",\n                                        \"data-in\": \"SendScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.address) ? \"\".concat(chainInfo.address.slice(0, 6), \"...\").concat(chainInfo.address.slice(-4)) : \"未连接钱包\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"send-screen.tsx:628\",\n                                        \"data-in\": \"SendScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_buy_buy14_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                        width: 12,\n                                        height: 12,\n                                        ml: 6\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                        \"data-at\": \"send-screen.tsx:631\",\n                        \"data-in\": \"SendScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                \"data-at\": \"send-screen.tsx:632\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: \"bold\",\n                                children: t(\"wallet.network\") || \"网络\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                                \"data-at\": \"send-screen.tsx:635\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"send-screen.tsx:636\",\n                                    \"data-in\": \"SendScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: \"bold\",\n                                    children: getChainName()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                        \"data-at\": \"send-screen.tsx:641\",\n                        \"data-in\": \"SendScreen\",\n                        \"data-is\": \"XStack\",\n                        mt: 10,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                                \"data-at\": \"send-screen.tsx:642\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                        \"data-at\": \"send-screen.tsx:643\",\n                                        \"data-in\": \"SendScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: t(\"wallet.networkFee\") || \"网络费用\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"send-screen.tsx:646\",\n                                        \"data-in\": \"SendScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_buy_buy13_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                                        width: 12,\n                                        height: 12,\n                                        ml: 6\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                                \"data-at\": \"send-screen.tsx:648\",\n                                \"data-in\": \"SendScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                    \"data-at\": \"send-screen.tsx:649\",\n                                    \"data-in\": \"SendScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: \"bold\",\n                                    children: [\n                                        estimatedFee,\n                                        \" \",\n                                        (chainInfo === null || chainInfo === void 0 ? void 0 : chainInfo.symbol) || getChainSymbol()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 539,\n                columnNumber: 7\n            }, this),\n            error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.YStack, {\n                \"data-at\": \"send-screen.tsx:658\",\n                \"data-in\": \"SendScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 10,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                    \"data-at\": \"send-screen.tsx:659\",\n                    \"data-in\": \"SendScreen\",\n                    \"data-is\": \"Text\",\n                    color: \"red\",\n                    fontSize: 14,\n                    textAlign: \"center\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 577,\n                columnNumber: 16\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.XStack, {\n                \"data-at\": \"send-screen.tsx:665-673\",\n                \"data-in\": \"SendScreen\",\n                \"data-is\": \"XStack\",\n                px: 16,\n                py: 24,\n                justifyContent: \"space-between\",\n                position: \"absolute\",\n                bottom: 0,\n                left: 0,\n                right: 0,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.Button, {\n                        \"data-at\": \"send-screen.tsx:674\",\n                        \"data-in\": \"SendScreen\",\n                        \"data-is\": \"Button\",\n                        rounded: 30,\n                        width: \"45%\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                            \"data-at\": \"send-screen.tsx:675\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"Text\",\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 584,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_15__.ConfirmButton, {\n                        onPress: handleSendTransaction,\n                        disabled: isLoading || !amount || !toAddress,\n                        style: {\n                            width: \"45%\",\n                            borderRadius: 30,\n                            textAlign: \"center\",\n                            lineHeight: 40,\n                            opacity: !amount || !toAddress ? 0.5 : 1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                            \"data-at\": \"send-screen.tsx:688\",\n                            \"data-in\": \"SendScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 14,\n                            fontWeight: 700,\n                            color: \"$black1\",\n                            children: t(\"common.confirm\") || \"确认\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                        lineNumber: 587,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n                lineNumber: 583,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/send-screen.tsx\",\n        lineNumber: 432,\n        columnNumber: 10\n    }, this);\n}\n_s(SendScreen, \"7ufMlYgDGjb124+oWHp7JQJ9zug=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_12__.useWalletStore,\n        app_stores_transactionStore__WEBPACK_IMPORTED_MODULE_13__.useTransactionStore,\n        app_i18n__WEBPACK_IMPORTED_MODULE_14__.useTranslation\n    ];\n});\n_c1 = SendScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"Line\");\n$RefreshReg$(_c1, \"SendScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/send-screen.tsx\n"));

/***/ }),

/***/ "../../packages/app/services/transactionService.ts":
/*!*********************************************************!*\
  !*** ../../packages/app/services/transactionService.ts ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   estimateTransactionFee: function() { return /* binding */ estimateTransactionFee; },\n/* harmony export */   sendBtcTransaction: function() { return /* binding */ sendBtcTransaction; },\n/* harmony export */   sendEvmTransaction: function() { return /* binding */ sendEvmTransaction; },\n/* harmony export */   sendSolanaTransaction: function() { return /* binding */ sendSolanaTransaction; },\n/* harmony export */   sendTransaction: function() { return /* binding */ sendTransaction; }\n/* harmony export */ });\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"../../node_modules/ethers/lib.esm/index.js\");\n/* harmony import */ var _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @solana/web3.js */ \"../../node_modules/@solana/web3.js/lib/index.browser.esm.js\");\n/* harmony import */ var app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! app/utils/networkManager */ \"../../packages/app/utils/networkManager.ts\");\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"../../node_modules/buffer/index.js\")[\"Buffer\"];\n\n\n\n// 地址验证\nconst validateAddress = (address, chain)=>{\n    try {\n        switch(chain){\n            case \"eth\":\n            case \"bsc\":\n                return ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.isAddress(address);\n            case \"solana\":\n                new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(address);\n                return true;\n            case \"btc\":\n                // 简单的BTC地址验证\n                return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) || /^bc1[a-z0-9]{39,59}$/.test(address) || /^[2mn][a-km-zA-HJ-NP-Z1-9]{33,34}$/.test(address);\n            default:\n                return false;\n        }\n    } catch (e) {\n        return false;\n    }\n};\n// 金额验证\nconst validateAmount = (amount)=>{\n    try {\n        const num = parseFloat(amount);\n        return num > 0 && !isNaN(num) && isFinite(num);\n    } catch (e) {\n        return false;\n    }\n};\n// EVM链交易发送 (ETH, BSC)\nconst sendEvmTransaction = async (params)=>{\n    try {\n        var _receipt_gasUsed;\n        const { fromAddress, toAddress, amount, privateKey, chain, gasPrice, gasLimit } = params;\n        // 选择RPC - 确保是EVM链\n        if (chain !== \"eth\" && chain !== \"bsc\") {\n            return {\n                success: false,\n                error: \"EVM交易不支持链类型: \".concat(chain)\n            };\n        }\n        const rpc = app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.networkManager.getAvailableRpc(chain);\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.JsonRpcProvider(rpc, undefined, {\n            staticNetwork: true,\n            batchMaxCount: 1\n        });\n        // 设置超时\n        provider._getConnection().timeout = 10000 // 10秒超时\n        ;\n        // 验证私钥格式\n        if (!privateKey.startsWith(\"0x\") || privateKey.length !== 66) {\n            return {\n                success: false,\n                error: \"无效的私钥格式\"\n            };\n        }\n        // 创建钱包\n        const wallet = new ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.Wallet(privateKey, provider);\n        // 验证from地址是否匹配\n        if (wallet.address.toLowerCase() !== fromAddress.toLowerCase()) {\n            return {\n                success: false,\n                error: \"私钥与发送地址不匹配\"\n            };\n        }\n        // 检查余额\n        const balance = await provider.getBalance(fromAddress);\n        const amountWei = ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.parseEther(amount);\n        // 获取gas费用信息\n        let feeData;\n        try {\n            feeData = await provider.getFeeData();\n        } catch (error) {\n            console.warn(\"Failed to get fee data, using fallback:\", error);\n            feeData = {\n                gasPrice: ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.parseUnits(\"20\", \"gwei\"),\n                maxFeePerGas: null,\n                maxPriorityFeePerGas: null\n            };\n        }\n        const estimatedGasLimit = gasLimit ? BigInt(gasLimit) : 21000n;\n        const estimatedGasPrice = gasPrice ? ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.parseUnits(gasPrice, \"gwei\") : feeData.gasPrice || ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.parseUnits(\"20\", \"gwei\");\n        const estimatedGasCost = estimatedGasLimit * estimatedGasPrice;\n        const totalCost = amountWei + estimatedGasCost;\n        if (balance < totalCost) {\n            const balanceEth = ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.formatEther(balance);\n            const totalCostEth = ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.formatEther(totalCost);\n            return {\n                success: false,\n                error: \"余额不足: 当前余额 \".concat(balanceEth, \" \").concat(chain.toUpperCase(), \", 需要 \").concat(totalCostEth, \" \").concat(chain.toUpperCase())\n            };\n        }\n        // 构建交易\n        const transaction = {\n            to: toAddress,\n            value: amountWei,\n            gasLimit: estimatedGasLimit,\n            gasPrice: estimatedGasPrice\n        };\n        // 发送交易\n        console.log(\"Sending \".concat(chain.toUpperCase(), \" transaction:\"), {\n            from: fromAddress,\n            to: toAddress,\n            amount: \"\".concat(amount, \" \").concat(chain.toUpperCase()),\n            gasLimit: estimatedGasLimit.toString(),\n            gasPrice: ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.formatUnits(estimatedGasPrice, \"gwei\") + \" gwei\"\n        });\n        const tx = await wallet.sendTransaction(transaction);\n        console.log(\"Transaction sent: \".concat(tx.hash));\n        // 等待确认 (最多等待30秒)\n        const receipt = await tx.wait(1, 30000);\n        if (!receipt) {\n            return {\n                success: false,\n                error: \"交易确认超时，请稍后查看交易状态\"\n            };\n        }\n        console.log(\"Transaction confirmed: \".concat(tx.hash), receipt);\n        return {\n            success: true,\n            txHash: tx.hash,\n            gasUsed: (_receipt_gasUsed = receipt.gasUsed) === null || _receipt_gasUsed === void 0 ? void 0 : _receipt_gasUsed.toString(),\n            blockNumber: receipt.blockNumber\n        };\n    } catch (error) {\n        console.error(\"EVM transaction error:\", error);\n        let errorMessage = \"交易失败\";\n        // 解析特定错误\n        if (error.message.includes(\"insufficient funds\")) {\n            errorMessage = \"余额不足\";\n        } else if (error.message.includes(\"gas required exceeds allowance\")) {\n            errorMessage = \"Gas费用不足\";\n        } else if (error.message.includes(\"nonce too low\")) {\n            errorMessage = \"交易序号错误，请稍后重试\";\n        } else if (error.message.includes(\"replacement transaction underpriced\")) {\n            errorMessage = \"Gas价格过低，请提高Gas价格\";\n        } else if (error.message.includes(\"timeout\")) {\n            errorMessage = \"网络超时，请检查网络连接\";\n        } else if (error.message) {\n            errorMessage = error.message;\n        }\n        // 网络错误时标记节点失败\n        if (error.message.includes(\"timeout\") || error.message.includes(\"network\")) {\n            try {\n                if (params.chain === \"eth\" || params.chain === \"bsc\") {\n                    const rpc = app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.networkManager.getAvailableRpc(params.chain);\n                    app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.networkManager.markNodeFailed(rpc);\n                }\n            } catch (e) {\n                console.warn(\"Failed to mark node as failed:\", e);\n            }\n        }\n        return {\n            success: false,\n            error: errorMessage\n        };\n    }\n};\n// Solana交易发送\nconst sendSolanaTransaction = async (params)=>{\n    try {\n        const { fromAddress, toAddress, amount, privateKey } = params;\n        const rpc = app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.networkManager.getAvailableRpc(\"solana\");\n        const connection = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.Connection(rpc);\n        // 从私钥创建Keypair\n        const fromKeypair = _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.Keypair.fromSecretKey(new Uint8Array(Buffer.from(privateKey.replace(\"0x\", \"\"), \"hex\")));\n        // 检查余额\n        const balance = await connection.getBalance(new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(fromAddress));\n        const amountLamports = parseFloat(amount) * 1e9 // SOL to lamports\n        ;\n        if (balance < amountLamports) {\n            return {\n                success: false,\n                error: \"余额不足\"\n            };\n        }\n        // 创建转账交易\n        const transaction = new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.Transaction().add(_solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.SystemProgram.transfer({\n            fromPubkey: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(fromAddress),\n            toPubkey: new _solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.PublicKey(toAddress),\n            lamports: amountLamports\n        }));\n        // 发送交易\n        const signature = await (0,_solana_web3_js__WEBPACK_IMPORTED_MODULE_0__.sendAndConfirmTransaction)(connection, transaction, [\n            fromKeypair\n        ]);\n        return {\n            success: true,\n            txHash: signature\n        };\n    } catch (error) {\n        console.error(\"Solana transaction error:\", error);\n        return {\n            success: false,\n            error: error.message || \"交易失败\"\n        };\n    }\n};\n// BTC交易发送 (简化版本，实际需要更复杂的UTXO处理)\nconst sendBtcTransaction = async (params)=>{\n    try {\n        // 这里是简化的BTC交易逻辑\n        // 实际实现需要处理UTXO、计算手续费等\n        console.log(\"BTC transaction not fully implemented yet\");\n        return {\n            success: false,\n            error: \"BTC交易功能暂未完全实现\"\n        };\n    } catch (error) {\n        console.error(\"BTC transaction error:\", error);\n        return {\n            success: false,\n            error: error.message || \"交易失败\"\n        };\n    }\n};\n// 统一的交易发送接口\nconst sendTransaction = async (params)=>{\n    // 基础验证\n    if (!params.fromAddress || !params.toAddress || !params.amount || !params.privateKey) {\n        return {\n            success: false,\n            error: \"参数不完整：缺少必要的交易信息\"\n        };\n    }\n    // 地址格式验证\n    if (!validateAddress(params.toAddress, params.chain)) {\n        return {\n            success: false,\n            error: \"无效的\".concat(params.chain.toUpperCase(), \"地址格式\")\n        };\n    }\n    // 金额验证\n    if (!validateAmount(params.amount)) {\n        return {\n            success: false,\n            error: \"无效的金额：金额必须大于0\"\n        };\n    }\n    // 防止自己给自己转账\n    if (params.fromAddress.toLowerCase() === params.toAddress.toLowerCase()) {\n        return {\n            success: false,\n            error: \"不能给自己转账\"\n        };\n    }\n    try {\n        switch(params.chain){\n            case \"eth\":\n            case \"bsc\":\n                return await sendEvmTransaction(params);\n            case \"solana\":\n                return await sendSolanaTransaction(params);\n            case \"btc\":\n                return await sendBtcTransaction(params);\n            default:\n                return {\n                    success: false,\n                    error: \"不支持的链类型: \".concat(params.chain)\n                };\n        }\n    } catch (error) {\n        console.error(\"Transaction failed:\", error);\n        return {\n            success: false,\n            error: error.message || \"交易发送失败\"\n        };\n    }\n};\n// 估算交易费用\nconst estimateTransactionFee = async (params)=>{\n    try {\n        const { chain, fromAddress, toAddress, amount } = params;\n        if (chain === \"eth\" || chain === \"bsc\") {\n            const rpc = app_utils_networkManager__WEBPACK_IMPORTED_MODULE_2__.networkManager.getAvailableRpc(chain);\n            const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.JsonRpcProvider(rpc);\n            const feeData = await provider.getFeeData();\n            const gasLimit = 21000n;\n            const gasPrice = feeData.gasPrice || ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.parseUnits(\"20\", \"gwei\");\n            const estimatedFee = ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.formatEther(gasPrice * gasLimit);\n            return {\n                estimatedFee,\n                gasPrice: ethers__WEBPACK_IMPORTED_MODULE_1__.ethers.formatUnits(gasPrice, \"gwei\"),\n                gasLimit: gasLimit.toString()\n            };\n        } else if (chain === \"solana\") {\n            // Solana交易费用通常是固定的\n            return {\n                estimatedFee: \"0.000005\"\n            };\n        } else {\n            return {\n                estimatedFee: \"0\"\n            };\n        }\n    } catch (error) {\n        console.error(\"Fee estimation error:\", error);\n        return {\n            estimatedFee: \"0\"\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/services/transactionService.ts\n"));

/***/ }),

/***/ "../../packages/app/stores/transactionStore.ts":
/*!*****************************************************!*\
  !*** ../../packages/app/stores/transactionStore.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatTransaction: function() { return /* binding */ formatTransaction; },\n/* harmony export */   getChainDisplayName: function() { return /* binding */ getChainDisplayName; },\n/* harmony export */   getStatusDisplayText: function() { return /* binding */ getStatusDisplayText; },\n/* harmony export */   getTypeDisplayText: function() { return /* binding */ getTypeDisplayText; },\n/* harmony export */   useTransactionStore: function() { return /* binding */ useTransactionStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"../../node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var app_utils_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/utils/storage */ \"../../packages/app/utils/storage.ts\");\n/* harmony import */ var app_utils_bip39__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! app/utils/bip39 */ \"../../packages/app/utils/bip39.ts\");\n\n\n\nconst STORAGE_KEY = \"TRANSACTION_HISTORY\";\nconst useTransactionStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        transactions: [],\n        isLoading: false,\n        loadTransactions: async ()=>{\n            set({\n                isLoading: true\n            });\n            try {\n                const stored = await app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].getItem(STORAGE_KEY);\n                if (stored) {\n                    const transactions = JSON.parse(stored);\n                    set({\n                        transactions\n                    });\n                }\n            } catch (error) {\n                console.error(\"Failed to load transactions:\", error);\n            } finally{\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        addTransaction: (transactionData)=>{\n            const transaction = {\n                ...transactionData,\n                id: (0,app_utils_bip39__WEBPACK_IMPORTED_MODULE_2__.generateUID)(),\n                timestamp: Date.now()\n            };\n            const transactions = [\n                transaction,\n                ...get().transactions\n            ];\n            set({\n                transactions\n            });\n            // 持久化存储\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].setItem(STORAGE_KEY, JSON.stringify(transactions));\n        },\n        updateTransaction: (id, updates)=>{\n            const transactions = get().transactions.map((tx)=>tx.id === id ? {\n                    ...tx,\n                    ...updates\n                } : tx);\n            set({\n                transactions\n            });\n            // 持久化存储\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].setItem(STORAGE_KEY, JSON.stringify(transactions));\n        },\n        getTransactionsByAddress: (address)=>{\n            return get().transactions.filter((tx)=>tx.fromAddress.toLowerCase() === address.toLowerCase() || tx.toAddress.toLowerCase() === address.toLowerCase());\n        },\n        getTransactionsByChain: (chain)=>{\n            return get().transactions.filter((tx)=>tx.chain === chain);\n        },\n        getTransactionsByAddresses: (addresses)=>{\n            const allTransactions = [];\n            const seenTransactionIds = new Set();\n            addresses.forEach((address)=>{\n                if (address) {\n                    const addressTransactions = get().transactions.filter((tx)=>tx.fromAddress.toLowerCase() === address.toLowerCase() || tx.toAddress.toLowerCase() === address.toLowerCase());\n                    addressTransactions.forEach((tx)=>{\n                        // 使用交易ID去重，避免重复添加同一个交易\n                        if (!seenTransactionIds.has(tx.id)) {\n                            seenTransactionIds.add(tx.id);\n                            allTransactions.push(tx);\n                        }\n                    });\n                }\n            });\n            // 按时间戳排序，最新的在前面\n            return allTransactions.sort((a, b)=>b.timestamp - a.timestamp);\n        },\n        clearTransactions: ()=>{\n            set({\n                transactions: []\n            });\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].removeItem(STORAGE_KEY);\n        },\n        removeTransaction: (id)=>{\n            const transactions = get().transactions.filter((tx)=>tx.id !== id);\n            set({\n                transactions\n            });\n            app_utils_storage__WEBPACK_IMPORTED_MODULE_1__[\"default\"].setItem(STORAGE_KEY, JSON.stringify(transactions));\n        }\n    }));\n// 辅助函数：格式化交易显示\nconst formatTransaction = (transaction)=>{\n    const { amount, symbol, type, toAddress, fromAddress } = transaction;\n    return {\n        ...transaction,\n        displayAmount: \"\".concat(type === \"send\" ? \"-\" : \"+\").concat(amount, \" \").concat(symbol),\n        displayAddress: type === \"send\" ? \"至 \".concat(toAddress.slice(0, 6), \"...\").concat(toAddress.slice(-4)) : \"来自 \".concat(fromAddress.slice(0, 6), \"...\").concat(fromAddress.slice(-4)),\n        displayTime: new Date(transaction.timestamp).toLocaleString()\n    };\n};\n// 辅助函数：获取链的显示名称\nconst getChainDisplayName = (chain)=>{\n    const chainNames = {\n        eth: \"Ethereum\",\n        bsc: \"BSC\",\n        btc: \"Bitcoin\",\n        solana: \"Solana\"\n    };\n    return chainNames[chain] || chain.toUpperCase();\n};\n// 辅助函数：获取交易状态的显示文本\nconst getStatusDisplayText = (status)=>{\n    const statusTexts = {\n        pending: \"待确认\",\n        confirmed: \"已确认\",\n        failed: \"失败\"\n    };\n    return statusTexts[status] || status;\n};\n// 辅助函数：获取交易类型的显示文本\nconst getTypeDisplayText = (type)=>{\n    const typeTexts = {\n        send: \"已发送\",\n        receive: \"已接收\"\n    };\n    return typeTexts[type] || type;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/stores/transactionStore.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fsend.tsx&page=%2Fwallet%2Fsend!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);