/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/noticeSetting"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FnoticeSetting.tsx&page=%2Fwallet%2FnoticeSetting!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FnoticeSetting.tsx&page=%2Fwallet%2FnoticeSetting! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/noticeSetting\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/noticeSetting.tsx */ \"./pages/wallet/noticeSetting.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/noticeSetting\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRm5vdGljZVNldHRpbmcudHN4JnBhZ2U9JTJGd2FsbGV0JTJGbm90aWNlU2V0dGluZyEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQywwRUFBa0M7QUFDekQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzI4ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvbm90aWNlU2V0dGluZ1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L25vdGljZVNldHRpbmcudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvbm90aWNlU2V0dGluZ1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FnoticeSetting.tsx&page=%2Fwallet%2FnoticeSetting!\n"));

/***/ }),

/***/ "./pages/wallet/noticeSetting.tsx":
/*!****************************************!*\
  !*** ./pages/wallet/noticeSetting.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_notice_seeting_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/notice-seeting-screen */ \"../../packages/app/features/wallet/notice-seeting-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_notice_seeting_screen__WEBPACK_IMPORTED_MODULE_1__.NoticeSettingScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/noticeSetting.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvbm90aWNlU2V0dGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDK0U7QUFFaEUsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDBGQUFtQkE7Ozs7O0FBQzdCO0tBRndCQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy93YWxsZXQvbm90aWNlU2V0dGluZy50c3g/NDAwNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IE5vdGljZVNldHRpbmdTY3JlZW4gfSBmcm9tICdhcHAvZmVhdHVyZXMvd2FsbGV0L25vdGljZS1zZWV0aW5nLXNjcmVlbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAgcmV0dXJuIDxOb3RpY2VTZXR0aW5nU2NyZWVuIC8+XG59XG4iXSwibmFtZXMiOlsiTm90aWNlU2V0dGluZ1NjcmVlbiIsIlBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/wallet/noticeSetting.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/notice-seeting-screen.tsx":
/*!********************************************************************!*\
  !*** ../../packages/app/features/wallet/notice-seeting-screen.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoticeSettingScreen: function() { return /* binding */ NoticeSettingScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction NoticeSettingScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n        \"data-at\": \"notice-seeting-screen.tsx:15\",\n        \"data-in\": \"NoticeSettingScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                \"data-at\": \"notice-seeting-screen.tsx:16\",\n                \"data-in\": \"NoticeSettingScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.NavBar, {\n                    title: \"通知\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.YStack, {\n                \"data-at\": \"notice-seeting-screen.tsx:19\",\n                \"data-in\": \"NoticeSettingScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 10,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:20\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:21\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:22\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"产品公告\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:23\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"率先了解最新功能\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:25\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:26\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:31\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:32\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:33\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: \"bold\",\n                                    children: \"见解和技巧\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:35\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:36\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:41\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:42\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:43\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: \"bold\",\n                                    children: \"特别优惠\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:45\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:46\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:51\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:52\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:53\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:54\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"资产价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:56\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:57\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:62\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:63\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:64\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"BTC 价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:65\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"BTC 价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:67\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:68\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:73\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:74\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:75\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"ETH 价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:76\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"ETH 价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:78\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:79\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:84\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:85\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                width: \"80%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:86\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"NFT 报价提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:87\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"当有人对您的 NFT 出价时收到通知。您不会 收到极低报价的通知。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:90\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:91\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_2__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:96\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:97\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:98\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: \"bold\",\n                                    children: \"账户活动\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:100\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                onPress: ()=>{\n                                    console.log(11);\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:101\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Switch\",\n                                    size: \"$4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Switch.Thumb, {\n                                        animation: \"bouncy\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n_s(NoticeSettingScreen, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter\n    ];\n});\n_c = NoticeSettingScreen;\nvar _c;\n$RefreshReg$(_c, \"NoticeSettingScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/notice-seeting-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FnoticeSetting.tsx&page=%2Fwallet%2FnoticeSetting!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);