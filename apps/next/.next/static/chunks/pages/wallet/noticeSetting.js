/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/noticeSetting"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FnoticeSetting.tsx&page=%2Fwallet%2FnoticeSetting!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FnoticeSetting.tsx&page=%2Fwallet%2FnoticeSetting! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/noticeSetting\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/noticeSetting.tsx */ \"./pages/wallet/noticeSetting.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/noticeSetting\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRm5vdGljZVNldHRpbmcudHN4JnBhZ2U9JTJGd2FsbGV0JTJGbm90aWNlU2V0dGluZyEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQywwRUFBa0M7QUFDekQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzI4ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvbm90aWNlU2V0dGluZ1wiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L25vdGljZVNldHRpbmcudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvbm90aWNlU2V0dGluZ1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FnoticeSetting.tsx&page=%2Fwallet%2FnoticeSetting!\n"));

/***/ }),

/***/ "./pages/wallet/noticeSetting.tsx":
/*!****************************************!*\
  !*** ./pages/wallet/noticeSetting.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_notice_seeting_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/notice-seeting-screen */ \"../../packages/app/features/wallet/notice-seeting-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_notice_seeting_screen__WEBPACK_IMPORTED_MODULE_1__.NoticeSettingScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/noticeSetting.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvbm90aWNlU2V0dGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDK0U7QUFFaEUsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDBGQUFtQkE7Ozs7O0FBQzdCO0tBRndCQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy93YWxsZXQvbm90aWNlU2V0dGluZy50c3g/NDAwNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IE5vdGljZVNldHRpbmdTY3JlZW4gfSBmcm9tICdhcHAvZmVhdHVyZXMvd2FsbGV0L25vdGljZS1zZWV0aW5nLXNjcmVlbidcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUGFnZSgpIHtcbiAgcmV0dXJuIDxOb3RpY2VTZXR0aW5nU2NyZWVuIC8+XG59XG4iXSwibmFtZXMiOlsiTm90aWNlU2V0dGluZ1NjcmVlbiIsIlBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/wallet/noticeSetting.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/notice-seeting-screen.tsx":
/*!********************************************************************!*\
  !*** ../../packages/app/features/wallet/notice-seeting-screen.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoticeSettingScreen: function() { return /* binding */ NoticeSettingScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction NoticeSettingScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // 通知开关状态管理\n    const [productAnnouncement, setProductAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [insightsAndTips, setInsightsAndTips] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [specialOffers, setSpecialOffers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [priceAlerts, setPriceAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [btcPriceAlerts, setBtcPriceAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [ethPriceAlerts, setEthPriceAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [nftOfferAlerts, setNftOfferAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [accountActivity, setAccountActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n        \"data-at\": \"notice-seeting-screen.tsx:25\",\n        \"data-in\": \"NoticeSettingScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                \"data-at\": \"notice-seeting-screen.tsx:26\",\n                \"data-in\": \"NoticeSettingScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.NavBar, {\n                    title: \"通知\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.YStack, {\n                \"data-at\": \"notice-seeting-screen.tsx:29\",\n                \"data-in\": \"NoticeSettingScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 10,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:30\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:31\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:32\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"产品公告\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:33\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"率先了解最新功能\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:35-44\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: productAnnouncement ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: productAnnouncement ? \"#3873F5\" : \"#3A3D44\",\n                                checked: productAnnouncement,\n                                onCheckedChange: setProductAnnouncement,\n                                style: {\n                                    backgroundColor: productAnnouncement ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:48\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:49\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:50\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 15,\n                                    fontWeight: \"bold\",\n                                    children: \"见解和技巧\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:52-61\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: insightsAndTips ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: insightsAndTips ? \"#3873F5\" : \"#3A3D44\",\n                                checked: insightsAndTips,\n                                onCheckedChange: setInsightsAndTips,\n                                style: {\n                                    backgroundColor: insightsAndTips ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:65\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:66\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:67\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 15,\n                                    fontWeight: \"bold\",\n                                    children: \"特别优惠\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:69-78\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: specialOffers ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: specialOffers ? \"#3873F5\" : \"#3A3D44\",\n                                checked: specialOffers,\n                                onCheckedChange: setSpecialOffers,\n                                style: {\n                                    backgroundColor: specialOffers ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:82\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:83\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:84\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:85\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"资产价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:87-96\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: priceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: priceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                checked: priceAlerts,\n                                onCheckedChange: setPriceAlerts,\n                                style: {\n                                    backgroundColor: priceAlerts ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:100\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:101\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:102\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"BTC 价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:103\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"BTC 价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:105-114\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: btcPriceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: btcPriceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                checked: btcPriceAlerts,\n                                onCheckedChange: setBtcPriceAlerts,\n                                style: {\n                                    backgroundColor: btcPriceAlerts ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:118\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:119\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:120\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"ETH 价格提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:121\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"ETH 价格发生大幅变化时收到自动通知\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:123-132\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: ethPriceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: ethPriceAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                checked: ethPriceAlerts,\n                                onCheckedChange: setEthPriceAlerts,\n                                style: {\n                                    backgroundColor: ethPriceAlerts ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:136\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:137\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                width: \"80%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:138\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        children: \"NFT 报价提醒\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"notice-seeting-screen.tsx:139\",\n                                        \"data-in\": \"NoticeSettingScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 15,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 2,\n                                        children: \"当有人对您的 NFT 出价时收到通知。您不会 收到极低报价的通知。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:142-151\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: nftOfferAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: nftOfferAlerts ? \"#3873F5\" : \"#3A3D44\",\n                                checked: nftOfferAlerts,\n                                onCheckedChange: setNftOfferAlerts,\n                                style: {\n                                    backgroundColor: nftOfferAlerts ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_3__.XStack, {\n                        \"data-at\": \"notice-seeting-screen.tsx:155\",\n                        \"data-in\": \"NoticeSettingScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                \"data-at\": \"notice-seeting-screen.tsx:156\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"View\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                    \"data-at\": \"notice-seeting-screen.tsx:157\",\n                                    \"data-in\": \"NoticeSettingScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 15,\n                                    fontWeight: \"bold\",\n                                    children: \"账户活动\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                \"data-at\": \"notice-seeting-screen.tsx:159-168\",\n                                \"data-in\": \"NoticeSettingScreen\",\n                                \"data-is\": \"Switch\",\n                                size: \"$3\",\n                                backgroundColor: accountActivity ? \"#3873F5\" : \"#3A3D44\",\n                                borderColor: accountActivity ? \"#3873F5\" : \"#3A3D44\",\n                                checked: accountActivity,\n                                onCheckedChange: setAccountActivity,\n                                style: {\n                                    backgroundColor: accountActivity ? \"#3873F5\" : \"#3A3D44\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Switch.Thumb, {\n                                    animation: \"bouncy\",\n                                    backgroundColor: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/notice-seeting-screen.tsx\",\n        lineNumber: 21,\n        columnNumber: 10\n    }, this);\n}\n_s(NoticeSettingScreen, \"bOnEtxfGudAFZPFveHmZwIvscQ8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NoticeSettingScreen;\nvar _c;\n$RefreshReg$(_c, \"NoticeSettingScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL3dhbGxldC9ub3RpY2Utc2VldGluZy1zY3JlZW4udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBNEc7QUFDL0Q7QUFDc0I7QUFLbkM7QUFHekIsU0FBU1E7O0lBQ2QsTUFBTUMsU0FBU04sNERBQVNBO0lBRXhCO0lBQ0EsTUFBTSxDQUFDTyxxQkFBcUJDLHVCQUF1QixHQUFHSiwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNLLGlCQUFpQkMsbUJBQW1CLEdBQUdOLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ08sZUFBZUMsaUJBQWlCLEdBQUdSLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ1MsYUFBYUMsZUFBZSxHQUFHViwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNXLGdCQUFnQkMsa0JBQWtCLEdBQUdaLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ2EsZ0JBQWdCQyxrQkFBa0IsR0FBR2QsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDZSxnQkFBZ0JDLGtCQUFrQixHQUFHaEIsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDaUIsaUJBQWlCQyxtQkFBbUIsR0FBR2xCLCtDQUFRQSxDQUFDO0lBRXZELHFCQUNFLDhEQUFDTCwwQ0FBTUE7UUFBQXdCLFdBQUE7UUFBQUMsV0FBQTtRQUFBQyxXQUFBO1FBQUNDLElBQUc7OzBCQUNULDhEQUFDNUIsMENBQU1BO2dCQUFBeUIsV0FBQTtnQkFBQUMsV0FBQTtnQkFBQUMsV0FBQTtnQkFBQ0UsZ0JBQWU7Z0JBQWdCQyxPQUFNOzBCQUMzQyw0RUFBQy9CLDBDQUFNQTtvQkFBQ2dDLE9BQU07b0JBQUtDLFFBQVEsSUFBTXhCLE9BQU95QixJQUFJOzs7Ozs7Ozs7OzswQkFFOUMsOERBQUNoQywwQ0FBTUE7Z0JBQUF3QixXQUFBO2dCQUFBQyxXQUFBO2dCQUFBQyxXQUFBO2dCQUFDTyxJQUFJO2dCQUFJQyxJQUFJO2dCQUFJQyxJQUFJOztrQ0FDMUIsOERBQUNwQywwQ0FBTUE7d0JBQUF5QixXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDRSxnQkFBZTt3QkFBZ0JNLElBQUk7OzBDQUN6Qyw4REFBQ2hDLHlDQUFJQTtnQ0FBQXNCLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7O2tEQUNILDhEQUFDdkIseUNBQUlBO3dDQUFBcUIsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ1UsVUFBVTt3Q0FBSUMsWUFBVztrREFBTzs7Ozs7O2tEQUN0Qyw4REFBQ2xDLHlDQUFJQTt3Q0FBQXFCLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNVLFVBQVU7d0NBQUlDLFlBQVc7d0NBQU9DLE9BQU07d0NBQVlKLElBQUk7a0RBQUc7Ozs7Ozs7Ozs7OzswQ0FFakUsOERBQUM5QiwyQ0FBTUE7Z0NBQUFvQixXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUNMYSxNQUFLO2dDQUNMQyxpQkFBaUJoQyxzQkFBc0IsWUFBWTtnQ0FDbkRpQyxhQUFhakMsc0JBQXNCLFlBQVk7Z0NBQy9Da0MsU0FBU2xDO2dDQUNUbUMsaUJBQWlCbEM7Z0NBQ2pCbUMsT0FBTztvQ0FDTEosaUJBQWlCaEMsc0JBQXNCLFlBQVk7Z0NBQ3JEOzBDQUVBLDRFQUFDSiwyQ0FBTUEsQ0FBQ3lDLEtBQUs7b0NBQUNDLFdBQVU7b0NBQVNOLGlCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR3JELDhEQUFDekMsMENBQU1BO3dCQUFBeUIsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ0UsZ0JBQWU7d0JBQWdCTSxJQUFJOzswQ0FDekMsOERBQUNoQyx5Q0FBSUE7Z0NBQUFzQixXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBOzBDQUNILDRFQUFDdkIseUNBQUlBO29DQUFBcUIsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQUMsV0FBQTtvQ0FBQ1UsVUFBVTtvQ0FBSUMsWUFBVzs4Q0FBTzs7Ozs7Ozs7Ozs7MENBRXhDLDhEQUFDakMsMkNBQU1BO2dDQUFBb0IsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FDTGEsTUFBSztnQ0FDTEMsaUJBQWlCOUIsa0JBQWtCLFlBQVk7Z0NBQy9DK0IsYUFBYS9CLGtCQUFrQixZQUFZO2dDQUMzQ2dDLFNBQVNoQztnQ0FDVGlDLGlCQUFpQmhDO2dDQUNqQmlDLE9BQU87b0NBQ0xKLGlCQUFpQjlCLGtCQUFrQixZQUFZO2dDQUNqRDswQ0FFQSw0RUFBQ04sMkNBQU1BLENBQUN5QyxLQUFLO29DQUFDQyxXQUFVO29DQUFTTixpQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUdyRCw4REFBQ3pDLDBDQUFNQTt3QkFBQXlCLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNFLGdCQUFlO3dCQUFnQk0sSUFBSTs7MENBQ3pDLDhEQUFDaEMseUNBQUlBO2dDQUFBc0IsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTswQ0FDSCw0RUFBQ3ZCLHlDQUFJQTtvQ0FBQXFCLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUFDLFdBQUE7b0NBQUNVLFVBQVU7b0NBQUlDLFlBQVc7OENBQU87Ozs7Ozs7Ozs7OzBDQUV4Qyw4REFBQ2pDLDJDQUFNQTtnQ0FBQW9CLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQ0xhLE1BQUs7Z0NBQ0xDLGlCQUFpQjVCLGdCQUFnQixZQUFZO2dDQUM3QzZCLGFBQWE3QixnQkFBZ0IsWUFBWTtnQ0FDekM4QixTQUFTOUI7Z0NBQ1QrQixpQkFBaUI5QjtnQ0FDakIrQixPQUFPO29DQUNMSixpQkFBaUI1QixnQkFBZ0IsWUFBWTtnQ0FDL0M7MENBRUEsNEVBQUNSLDJDQUFNQSxDQUFDeUMsS0FBSztvQ0FBQ0MsV0FBVTtvQ0FBU04saUJBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FHckQsOERBQUN6QywwQ0FBTUE7d0JBQUF5QixXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDRSxnQkFBZTt3QkFBZ0JNLElBQUk7OzBDQUN6Qyw4REFBQ2hDLHlDQUFJQTtnQ0FBQXNCLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7O2tEQUNILDhEQUFDdkIseUNBQUlBO3dDQUFBcUIsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ1UsVUFBVTt3Q0FBSUMsWUFBVztrREFBTzs7Ozs7O2tEQUN0Qyw4REFBQ2xDLHlDQUFJQTt3Q0FBQXFCLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNVLFVBQVU7d0NBQUlDLFlBQVc7d0NBQU9DLE9BQU07d0NBQVlKLElBQUk7a0RBQUc7Ozs7Ozs7Ozs7OzswQ0FFakUsOERBQUM5QiwyQ0FBTUE7Z0NBQUFvQixXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUNMYSxNQUFLO2dDQUNMQyxpQkFBaUIxQixjQUFjLFlBQVk7Z0NBQzNDMkIsYUFBYTNCLGNBQWMsWUFBWTtnQ0FDdkM0QixTQUFTNUI7Z0NBQ1Q2QixpQkFBaUI1QjtnQ0FDakI2QixPQUFPO29DQUNMSixpQkFBaUIxQixjQUFjLFlBQVk7Z0NBQzdDOzBDQUVBLDRFQUFDViwyQ0FBTUEsQ0FBQ3lDLEtBQUs7b0NBQUNDLFdBQVU7b0NBQVNOLGlCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR3JELDhEQUFDekMsMENBQU1BO3dCQUFBeUIsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQUMsV0FBQTt3QkFBQ0UsZ0JBQWU7d0JBQWdCTSxJQUFJOzswQ0FDekMsOERBQUNoQyx5Q0FBSUE7Z0NBQUFzQixXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBOztrREFDSCw4REFBQ3ZCLHlDQUFJQTt3Q0FBQXFCLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNVLFVBQVU7d0NBQUlDLFlBQVc7a0RBQU87Ozs7OztrREFDdEMsOERBQUNsQyx5Q0FBSUE7d0NBQUFxQixXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDVSxVQUFVO3dDQUFJQyxZQUFXO3dDQUFPQyxPQUFNO3dDQUFZSixJQUFJO2tEQUFHOzs7Ozs7Ozs7Ozs7MENBRWpFLDhEQUFDOUIsMkNBQU1BO2dDQUFBb0IsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FDTGEsTUFBSztnQ0FDTEMsaUJBQWlCeEIsaUJBQWlCLFlBQVk7Z0NBQzlDeUIsYUFBYXpCLGlCQUFpQixZQUFZO2dDQUMxQzBCLFNBQVMxQjtnQ0FDVDJCLGlCQUFpQjFCO2dDQUNqQjJCLE9BQU87b0NBQ0xKLGlCQUFpQnhCLGlCQUFpQixZQUFZO2dDQUNoRDswQ0FFQSw0RUFBQ1osMkNBQU1BLENBQUN5QyxLQUFLO29DQUFDQyxXQUFVO29DQUFTTixpQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUdyRCw4REFBQ3pDLDBDQUFNQTt3QkFBQXlCLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUFDLFdBQUE7d0JBQUNFLGdCQUFlO3dCQUFnQk0sSUFBSTs7MENBQ3pDLDhEQUFDaEMseUNBQUlBO2dDQUFBc0IsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTs7a0RBQ0gsOERBQUN2Qix5Q0FBSUE7d0NBQUFxQixXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDVSxVQUFVO3dDQUFJQyxZQUFXO2tEQUFPOzs7Ozs7a0RBQ3RDLDhEQUFDbEMseUNBQUlBO3dDQUFBcUIsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQUMsV0FBQTt3Q0FBQ1UsVUFBVTt3Q0FBSUMsWUFBVzt3Q0FBT0MsT0FBTTt3Q0FBWUosSUFBSTtrREFBRzs7Ozs7Ozs7Ozs7OzBDQUVqRSw4REFBQzlCLDJDQUFNQTtnQ0FBQW9CLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQ0xhLE1BQUs7Z0NBQ0xDLGlCQUFpQnRCLGlCQUFpQixZQUFZO2dDQUM5Q3VCLGFBQWF2QixpQkFBaUIsWUFBWTtnQ0FDMUN3QixTQUFTeEI7Z0NBQ1R5QixpQkFBaUJ4QjtnQ0FDakJ5QixPQUFPO29DQUNMSixpQkFBaUJ0QixpQkFBaUIsWUFBWTtnQ0FDaEQ7MENBRUEsNEVBQUNkLDJDQUFNQSxDQUFDeUMsS0FBSztvQ0FBQ0MsV0FBVTtvQ0FBU04saUJBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FHckQsOERBQUN6QywwQ0FBTUE7d0JBQUF5QixXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDRSxnQkFBZTt3QkFBZ0JNLElBQUk7OzBDQUN6Qyw4REFBQ2hDLHlDQUFJQTtnQ0FBQXNCLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUNxQixPQUFNOztrREFDViw4REFBQzVDLHlDQUFJQTt3Q0FBQXFCLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUFDLFdBQUE7d0NBQUNVLFVBQVU7d0NBQUlDLFlBQVc7a0RBQU87Ozs7OztrREFDdEMsOERBQUNsQyx5Q0FBSUE7d0NBQUFxQixXQUFBO3dDQUFBQyxXQUFBO3dDQUFBQyxXQUFBO3dDQUFDVSxVQUFVO3dDQUFJQyxZQUFXO3dDQUFPQyxPQUFNO3dDQUFZSixJQUFJO2tEQUFHOzs7Ozs7Ozs7Ozs7MENBR2pFLDhEQUFDOUIsMkNBQU1BO2dDQUFBb0IsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FBQUMsV0FBQTtnQ0FDTGEsTUFBSztnQ0FDTEMsaUJBQWlCcEIsaUJBQWlCLFlBQVk7Z0NBQzlDcUIsYUFBYXJCLGlCQUFpQixZQUFZO2dDQUMxQ3NCLFNBQVN0QjtnQ0FDVHVCLGlCQUFpQnRCO2dDQUNqQnVCLE9BQU87b0NBQ0xKLGlCQUFpQnBCLGlCQUFpQixZQUFZO2dDQUNoRDswQ0FFQSw0RUFBQ2hCLDJDQUFNQSxDQUFDeUMsS0FBSztvQ0FBQ0MsV0FBVTtvQ0FBU04saUJBQWdCOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FHckQsOERBQUN6QywwQ0FBTUE7d0JBQUF5QixXQUFBO3dCQUFBQyxXQUFBO3dCQUFBQyxXQUFBO3dCQUFDRSxnQkFBZTt3QkFBZ0JNLElBQUk7OzBDQUN6Qyw4REFBQ2hDLHlDQUFJQTtnQ0FBQXNCLFdBQUE7Z0NBQUFDLFdBQUE7Z0NBQUFDLFdBQUE7MENBQ0gsNEVBQUN2Qix5Q0FBSUE7b0NBQUFxQixXQUFBO29DQUFBQyxXQUFBO29DQUFBQyxXQUFBO29DQUFDVSxVQUFVO29DQUFJQyxZQUFXOzhDQUFPOzs7Ozs7Ozs7OzswQ0FFeEMsOERBQUNqQywyQ0FBTUE7Z0NBQUFvQixXQUFBO2dDQUFBQyxXQUFBO2dDQUFBQyxXQUFBO2dDQUNMYSxNQUFLO2dDQUNMQyxpQkFBaUJsQixrQkFBa0IsWUFBWTtnQ0FDL0NtQixhQUFhbkIsa0JBQWtCLFlBQVk7Z0NBQzNDb0IsU0FBU3BCO2dDQUNUcUIsaUJBQWlCcEI7Z0NBQ2pCcUIsT0FBTztvQ0FDTEosaUJBQWlCbEIsa0JBQWtCLFlBQVk7Z0NBQ2pEOzBDQUVBLDRFQUFDbEIsMkNBQU1BLENBQUN5QyxLQUFLO29DQUFDQyxXQUFVO29DQUFTTixpQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzdEO0dBcktnQmxDOztRQUNDTCx3REFBU0E7OztLQURWSyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXBwL2ZlYXR1cmVzL3dhbGxldC9ub3RpY2Utc2VldGluZy1zY3JlZW4udHN4P2FjZWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXZhdGFyLCBCdXR0b24sIEgzLCBINSwgTmF2QmFyLCBQYXJhZ3JhcGgsIFN3aXRjaFRoZW1lQnV0dG9uLCBYU3RhY2ssIFlTdGFjaywgSW5wdXQgfSBmcm9tICdAbXkvdWknXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICdzb2xpdG8vbmF2aWdhdGlvbidcbmltcG9ydCB7IFZpZXcsIFRleHQsIEltYWdlLCBIMSwgSDIsIHN0eWxlZCwgU3dpdGNoIH0gZnJvbSAndGFtYWd1aSdcbmltcG9ydCBlbm90aWNlSWNvbiBmcm9tICcuLi8uLi8uLi9hc3NldHMvaW1hZ2VzL3dhbGxldC9ub3RpY2UucG5nJ1xuaW1wb3J0IHNldHRpbmdJY29uIGZyb20gJy4uLy4uLy4uL2Fzc2V0cy9pbWFnZXMvc2V0dGluZy5wbmcnXG5cbmltcG9ydCB7IFByZXNzYWJsZSB9IGZyb20gJ3JlYWN0LW5hdGl2ZSdcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDaGV2cm9uUmlnaHQgfSBmcm9tICdAdGFtYWd1aS9sdWNpZGUtaWNvbnMnXG5cbmV4cG9ydCBmdW5jdGlvbiBOb3RpY2VTZXR0aW5nU2NyZWVuKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuXG4gIC8vIOmAmuefpeW8gOWFs+eKtuaAgeeuoeeQhlxuICBjb25zdCBbcHJvZHVjdEFubm91bmNlbWVudCwgc2V0UHJvZHVjdEFubm91bmNlbWVudF0gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbaW5zaWdodHNBbmRUaXBzLCBzZXRJbnNpZ2h0c0FuZFRpcHNdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3NwZWNpYWxPZmZlcnMsIHNldFNwZWNpYWxPZmZlcnNdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3ByaWNlQWxlcnRzLCBzZXRQcmljZUFsZXJ0c10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbYnRjUHJpY2VBbGVydHMsIHNldEJ0Y1ByaWNlQWxlcnRzXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtldGhQcmljZUFsZXJ0cywgc2V0RXRoUHJpY2VBbGVydHNdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW25mdE9mZmVyQWxlcnRzLCBzZXROZnRPZmZlckFsZXJ0c10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbYWNjb3VudEFjdGl2aXR5LCBzZXRBY2NvdW50QWN0aXZpdHldID0gdXNlU3RhdGUodHJ1ZSlcblxuICByZXR1cm4gKFxuICAgIDxZU3RhY2sgYmc9XCIkYmFja2dyb3VuZFwiPlxuICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD0nc3BhY2UtYmV0d2VlbicgaXRlbXM9XCJjZW50ZXJcIj5cbiAgICAgICAgPE5hdkJhciB0aXRsZT1cIumAmuefpVwiIG9uQmFjaz17KCkgPT4gcm91dGVyLmJhY2soKX0gLz5cbiAgICAgIDwvWFN0YWNrPlxuICAgICAgPFlTdGFjayBweD17MTZ9IG10PXsxMH0gbWI9ezIwfT5cbiAgICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIiBtdD17MjB9PlxuICAgICAgICAgIDxWaWV3PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE1fSBmb250V2VpZ2h0PVwiYm9sZFwiPuS6p+WTgeWFrOWRijwvVGV4dD5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNX0gZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cIiRhY2NlbnQxMVwiIG10PXsyfT7njoflhYjkuobop6PmnIDmlrDlip/og708L1RleHQ+XG4gICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgIHNpemU9XCIkM1wiXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I9e3Byb2R1Y3RBbm5vdW5jZW1lbnQgPyBcIiMzODczRjVcIiA6IFwiIzNBM0Q0NFwifVxuICAgICAgICAgICAgYm9yZGVyQ29sb3I9e3Byb2R1Y3RBbm5vdW5jZW1lbnQgPyBcIiMzODczRjVcIiA6IFwiIzNBM0Q0NFwifVxuICAgICAgICAgICAgY2hlY2tlZD17cHJvZHVjdEFubm91bmNlbWVudH1cbiAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17c2V0UHJvZHVjdEFubm91bmNlbWVudH1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogcHJvZHVjdEFubm91bmNlbWVudCA/ICcjMzg3M0Y1JyA6ICcjM0EzRDQ0JyxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFN3aXRjaC5UaHVtYiBhbmltYXRpb249XCJib3VuY3lcIiBiYWNrZ3JvdW5kQ29sb3I9XCJ3aGl0ZVwiIC8+XG4gICAgICAgICAgPC9Td2l0Y2g+XG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIG10PXsyMH0+XG4gICAgICAgICAgPFZpZXc+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTV9IGZvbnRXZWlnaHQ9XCJib2xkXCI+6KeB6Kej5ZKM5oqA5benPC9UZXh0PlxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICA8U3dpdGNoXG4gICAgICAgICAgICBzaXplPVwiJDNcIlxuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yPXtpbnNpZ2h0c0FuZFRpcHMgPyBcIiMzODczRjVcIiA6IFwiIzNBM0Q0NFwifVxuICAgICAgICAgICAgYm9yZGVyQ29sb3I9e2luc2lnaHRzQW5kVGlwcyA/IFwiIzM4NzNGNVwiIDogXCIjM0EzRDQ0XCJ9XG4gICAgICAgICAgICBjaGVja2VkPXtpbnNpZ2h0c0FuZFRpcHN9XG4gICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9e3NldEluc2lnaHRzQW5kVGlwc31cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogaW5zaWdodHNBbmRUaXBzID8gJyMzODczRjUnIDogJyMzQTNENDQnLFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U3dpdGNoLlRodW1iIGFuaW1hdGlvbj1cImJvdW5jeVwiIGJhY2tncm91bmRDb2xvcj1cIndoaXRlXCIgLz5cbiAgICAgICAgICA8L1N3aXRjaD5cbiAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgIDxYU3RhY2sganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezIwfT5cbiAgICAgICAgICA8Vmlldz5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNX0gZm9udFdlaWdodD1cImJvbGRcIj7nibnliKvkvJjmg6A8L1RleHQ+XG4gICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgIHNpemU9XCIkM1wiXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I9e3NwZWNpYWxPZmZlcnMgPyBcIiMzODczRjVcIiA6IFwiIzNBM0Q0NFwifVxuICAgICAgICAgICAgYm9yZGVyQ29sb3I9e3NwZWNpYWxPZmZlcnMgPyBcIiMzODczRjVcIiA6IFwiIzNBM0Q0NFwifVxuICAgICAgICAgICAgY2hlY2tlZD17c3BlY2lhbE9mZmVyc31cbiAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17c2V0U3BlY2lhbE9mZmVyc31cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogc3BlY2lhbE9mZmVycyA/ICcjMzg3M0Y1JyA6ICcjM0EzRDQ0JyxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFN3aXRjaC5UaHVtYiBhbmltYXRpb249XCJib3VuY3lcIiBiYWNrZ3JvdW5kQ29sb3I9XCJ3aGl0ZVwiIC8+XG4gICAgICAgICAgPC9Td2l0Y2g+XG4gICAgICAgIDwvWFN0YWNrPlxuICAgICAgICA8WFN0YWNrIGp1c3RpZnlDb250ZW50PVwic3BhY2UtYmV0d2VlblwiIG10PXsyMH0+XG4gICAgICAgICAgPFZpZXc+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTV9IGZvbnRXZWlnaHQ9XCJib2xkXCI+5Lu35qC85o+Q6YaSPC9UZXh0PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE1fSBmb250V2VpZ2h0PVwiYm9sZFwiIGNvbG9yPVwiJGFjY2VudDExXCIgbXQ9ezJ9Pui1hOS6p+S7t+agvOWPkeeUn+Wkp+W5heWPmOWMluaXtuaUtuWIsOiHquWKqOmAmuefpTwvVGV4dD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgc2l6ZT1cIiQzXCJcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcj17cHJpY2VBbGVydHMgPyBcIiMzODczRjVcIiA6IFwiIzNBM0Q0NFwifVxuICAgICAgICAgICAgYm9yZGVyQ29sb3I9e3ByaWNlQWxlcnRzID8gXCIjMzg3M0Y1XCIgOiBcIiMzQTNENDRcIn1cbiAgICAgICAgICAgIGNoZWNrZWQ9e3ByaWNlQWxlcnRzfVxuICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXtzZXRQcmljZUFsZXJ0c31cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogcHJpY2VBbGVydHMgPyAnIzM4NzNGNScgOiAnIzNBM0Q0NCcsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTd2l0Y2guVGh1bWIgYW5pbWF0aW9uPVwiYm91bmN5XCIgYmFja2dyb3VuZENvbG9yPVwid2hpdGVcIiAvPlxuICAgICAgICAgIDwvU3dpdGNoPlxuICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIiBtdD17MjB9PlxuICAgICAgICAgIDxWaWV3PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE1fSBmb250V2VpZ2h0PVwiYm9sZFwiPkJUQyDku7fmoLzmj5DphpI8L1RleHQ+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTV9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCIkYWNjZW50MTFcIiBtdD17Mn0+QlRDIOS7t+agvOWPkeeUn+Wkp+W5heWPmOWMluaXtuaUtuWIsOiHquWKqOmAmuefpTwvVGV4dD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgc2l6ZT1cIiQzXCJcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcj17YnRjUHJpY2VBbGVydHMgPyBcIiMzODczRjVcIiA6IFwiIzNBM0Q0NFwifVxuICAgICAgICAgICAgYm9yZGVyQ29sb3I9e2J0Y1ByaWNlQWxlcnRzID8gXCIjMzg3M0Y1XCIgOiBcIiMzQTNENDRcIn1cbiAgICAgICAgICAgIGNoZWNrZWQ9e2J0Y1ByaWNlQWxlcnRzfVxuICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXtzZXRCdGNQcmljZUFsZXJ0c31cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogYnRjUHJpY2VBbGVydHMgPyAnIzM4NzNGNScgOiAnIzNBM0Q0NCcsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTd2l0Y2guVGh1bWIgYW5pbWF0aW9uPVwiYm91bmN5XCIgYmFja2dyb3VuZENvbG9yPVwid2hpdGVcIiAvPlxuICAgICAgICAgIDwvU3dpdGNoPlxuICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIiBtdD17MjB9PlxuICAgICAgICAgIDxWaWV3PlxuICAgICAgICAgICAgPFRleHQgZm9udFNpemU9ezE1fSBmb250V2VpZ2h0PVwiYm9sZFwiPkVUSCDku7fmoLzmj5DphpI8L1RleHQ+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTV9IGZvbnRXZWlnaHQ9XCJib2xkXCIgY29sb3I9XCIkYWNjZW50MTFcIiBtdD17Mn0+RVRIIOS7t+agvOWPkeeUn+Wkp+W5heWPmOWMluaXtuaUtuWIsOiHquWKqOmAmuefpTwvVGV4dD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgPFN3aXRjaFxuICAgICAgICAgICAgc2l6ZT1cIiQzXCJcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcj17ZXRoUHJpY2VBbGVydHMgPyBcIiMzODczRjVcIiA6IFwiIzNBM0Q0NFwifVxuICAgICAgICAgICAgYm9yZGVyQ29sb3I9e2V0aFByaWNlQWxlcnRzID8gXCIjMzg3M0Y1XCIgOiBcIiMzQTNENDRcIn1cbiAgICAgICAgICAgIGNoZWNrZWQ9e2V0aFByaWNlQWxlcnRzfVxuICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXtzZXRFdGhQcmljZUFsZXJ0c31cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogZXRoUHJpY2VBbGVydHMgPyAnIzM4NzNGNScgOiAnIzNBM0Q0NCcsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTd2l0Y2guVGh1bWIgYW5pbWF0aW9uPVwiYm91bmN5XCIgYmFja2dyb3VuZENvbG9yPVwid2hpdGVcIiAvPlxuICAgICAgICAgIDwvU3dpdGNoPlxuICAgICAgICA8L1hTdGFjaz5cbiAgICAgICAgPFhTdGFjayBqdXN0aWZ5Q29udGVudD1cInNwYWNlLWJldHdlZW5cIiBtdD17MjB9PlxuICAgICAgICAgIDxWaWV3IHdpZHRoPVwiODAlXCI+XG4gICAgICAgICAgICA8VGV4dCBmb250U2l6ZT17MTV9IGZvbnRXZWlnaHQ9XCJib2xkXCI+TkZUIOaKpeS7t+aPkOmGkjwvVGV4dD5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNX0gZm9udFdlaWdodD1cImJvbGRcIiBjb2xvcj1cIiRhY2NlbnQxMVwiIG10PXsyfT7lvZPmnInkurrlr7nmgqjnmoQgTkZUIOWHuuS7t+aXtuaUtuWIsOmAmuefpeOAguaCqOS4jeS8mlxuICAgICAgICAgICAgICDmlLbliLDmnoHkvY7miqXku7fnmoTpgJrnn6XjgII8L1RleHQ+XG4gICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgIHNpemU9XCIkM1wiXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I9e25mdE9mZmVyQWxlcnRzID8gXCIjMzg3M0Y1XCIgOiBcIiMzQTNENDRcIn1cbiAgICAgICAgICAgIGJvcmRlckNvbG9yPXtuZnRPZmZlckFsZXJ0cyA/IFwiIzM4NzNGNVwiIDogXCIjM0EzRDQ0XCJ9XG4gICAgICAgICAgICBjaGVja2VkPXtuZnRPZmZlckFsZXJ0c31cbiAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17c2V0TmZ0T2ZmZXJBbGVydHN9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IG5mdE9mZmVyQWxlcnRzID8gJyMzODczRjUnIDogJyMzQTNENDQnLFxuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U3dpdGNoLlRodW1iIGFuaW1hdGlvbj1cImJvdW5jeVwiIGJhY2tncm91bmRDb2xvcj1cIndoaXRlXCIgLz5cbiAgICAgICAgICA8L1N3aXRjaD5cbiAgICAgICAgPC9YU3RhY2s+XG4gICAgICAgIDxYU3RhY2sganVzdGlmeUNvbnRlbnQ9XCJzcGFjZS1iZXR3ZWVuXCIgbXQ9ezIwfT5cbiAgICAgICAgICA8Vmlldz5cbiAgICAgICAgICAgIDxUZXh0IGZvbnRTaXplPXsxNX0gZm9udFdlaWdodD1cImJvbGRcIj7otKbmiLfmtLvliqg8L1RleHQ+XG4gICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgIDxTd2l0Y2hcbiAgICAgICAgICAgIHNpemU9XCIkM1wiXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I9e2FjY291bnRBY3Rpdml0eSA/IFwiIzM4NzNGNVwiIDogXCIjM0EzRDQ0XCJ9XG4gICAgICAgICAgICBib3JkZXJDb2xvcj17YWNjb3VudEFjdGl2aXR5ID8gXCIjMzg3M0Y1XCIgOiBcIiMzQTNENDRcIn1cbiAgICAgICAgICAgIGNoZWNrZWQ9e2FjY291bnRBY3Rpdml0eX1cbiAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17c2V0QWNjb3VudEFjdGl2aXR5fVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBhY2NvdW50QWN0aXZpdHkgPyAnIzM4NzNGNScgOiAnIzNBM0Q0NCcsXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxTd2l0Y2guVGh1bWIgYW5pbWF0aW9uPVwiYm91bmN5XCIgYmFja2dyb3VuZENvbG9yPVwid2hpdGVcIiAvPlxuICAgICAgICAgIDwvU3dpdGNoPlxuICAgICAgICA8L1hTdGFjaz5cbiAgICAgIDwvWVN0YWNrPlxuICAgIDwvWVN0YWNrID5cblxuICApXG59Il0sIm5hbWVzIjpbIk5hdkJhciIsIlhTdGFjayIsIllTdGFjayIsInVzZVJvdXRlciIsIlZpZXciLCJUZXh0IiwiU3dpdGNoIiwidXNlU3RhdGUiLCJOb3RpY2VTZXR0aW5nU2NyZWVuIiwicm91dGVyIiwicHJvZHVjdEFubm91bmNlbWVudCIsInNldFByb2R1Y3RBbm5vdW5jZW1lbnQiLCJpbnNpZ2h0c0FuZFRpcHMiLCJzZXRJbnNpZ2h0c0FuZFRpcHMiLCJzcGVjaWFsT2ZmZXJzIiwic2V0U3BlY2lhbE9mZmVycyIsInByaWNlQWxlcnRzIiwic2V0UHJpY2VBbGVydHMiLCJidGNQcmljZUFsZXJ0cyIsInNldEJ0Y1ByaWNlQWxlcnRzIiwiZXRoUHJpY2VBbGVydHMiLCJzZXRFdGhQcmljZUFsZXJ0cyIsIm5mdE9mZmVyQWxlcnRzIiwic2V0TmZ0T2ZmZXJBbGVydHMiLCJhY2NvdW50QWN0aXZpdHkiLCJzZXRBY2NvdW50QWN0aXZpdHkiLCJkYXRhLWF0IiwiZGF0YS1pbiIsImRhdGEtaXMiLCJiZyIsImp1c3RpZnlDb250ZW50IiwiaXRlbXMiLCJ0aXRsZSIsIm9uQmFjayIsImJhY2siLCJweCIsIm10IiwibWIiLCJmb250U2l6ZSIsImZvbnRXZWlnaHQiLCJjb2xvciIsInNpemUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJib3JkZXJDb2xvciIsImNoZWNrZWQiLCJvbkNoZWNrZWRDaGFuZ2UiLCJzdHlsZSIsIlRodW1iIiwiYW5pbWF0aW9uIiwid2lkdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/notice-seeting-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FnoticeSetting.tsx&page=%2Fwallet%2FnoticeSetting!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);