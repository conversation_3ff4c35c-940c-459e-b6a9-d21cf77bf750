/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/manager"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fmanager.tsx&page=%2Fwallet%2Fmanager!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fmanager.tsx&page=%2Fwallet%2Fmanager! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/manager\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/manager.tsx */ \"./pages/wallet/manager.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/manager\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRm1hbmFnZXIudHN4JnBhZ2U9JTJGd2FsbGV0JTJGbWFuYWdlciEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw4REFBNEI7QUFDbkQ7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvP2NjM2IiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi93YWxsZXQvbWFuYWdlclwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L21hbmFnZXIudHN4XCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi93YWxsZXQvbWFuYWdlclwiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fmanager.tsx&page=%2Fwallet%2Fmanager!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/add.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/wallet/add.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/add.6c54607f.png\",\"height\":36,\"width\":36,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fadd.6c54607f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvYWRkLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TEFBd0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2FkZC5wbmc/ZWExNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvYWRkLjZjNTQ2MDdmLnBuZ1wiLFwiaGVpZ2h0XCI6MzYsXCJ3aWR0aFwiOjM2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFkZC42YzU0NjA3Zi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/add.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/daoru.png":
/*!*****************************************************!*\
  !*** ../../packages/assets/images/wallet/daoru.png ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/daoru.a55ab101.png\",\"height\":36,\"width\":36,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdaoru.a55ab101.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZGFvcnUucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZGFvcnUucG5nPzI0YzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Rhb3J1LmE1NWFiMTAxLnBuZ1wiLFwiaGVpZ2h0XCI6MzYsXCJ3aWR0aFwiOjM2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmRhb3J1LmE1NWFiMTAxLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/daoru.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/jiesuo.png":
/*!******************************************************!*\
  !*** ../../packages/assets/images/wallet/jiesuo.png ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/jiesuo.03364b06.png\",\"height\":36,\"width\":36,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fjiesuo.03364b06.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvamllc3VvLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyw4TEFBOEwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2ppZXN1by5wbmc/M2JlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvamllc3VvLjAzMzY0YjA2LnBuZ1wiLFwiaGVpZ2h0XCI6MzYsXCJ3aWR0aFwiOjM2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmppZXN1by4wMzM2NGIwNi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/jiesuo.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/new.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/wallet/new.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/new.2de84d62.png\",\"height\":36,\"width\":36,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnew.2de84d62.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvbmV3LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TEFBd0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L25ldy5wbmc/OGYyZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvbmV3LjJkZTg0ZDYyLnBuZ1wiLFwiaGVpZ2h0XCI6MzYsXCJ3aWR0aFwiOjM2LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRm5ldy4yZGU4NGQ2Mi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/new.png\n"));

/***/ }),

/***/ "./pages/wallet/manager.tsx":
/*!**********************************!*\
  !*** ./pages/wallet/manager.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_manager_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/manager-screen */ \"../../packages/app/features/wallet/manager-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_manager_screen__WEBPACK_IMPORTED_MODULE_1__.WalletManagerScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/manager.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvbWFuYWdlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDd0U7QUFFekQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELG1GQUFtQkE7Ozs7O0FBQzdCO0tBRndCQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9wYWdlcy93YWxsZXQvbWFuYWdlci50c3g/NGNmNyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCB7IFdhbGxldE1hbmFnZXJTY3JlZW4gfSBmcm9tICdhcHAvZmVhdHVyZXMvd2FsbGV0L21hbmFnZXItc2NyZWVuJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYWdlKCkge1xuICByZXR1cm4gPFdhbGxldE1hbmFnZXJTY3JlZW4gLz5cbn0iXSwibmFtZXMiOlsiV2FsbGV0TWFuYWdlclNjcmVlbiIsIlBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/wallet/manager.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/manager-screen.tsx":
/*!*************************************************************!*\
  !*** ../../packages/app/features/wallet/manager-screen.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletManagerScreen: function() { return /* binding */ WalletManagerScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_add_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/add.png */ \"../../packages/assets/images/wallet/add.png\");\n/* harmony import */ var _assets_images_wallet_new_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/new.png */ \"../../packages/assets/images/wallet/new.png\");\n/* harmony import */ var _assets_images_wallet_daoru_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/daoru.png */ \"../../packages/assets/images/wallet/daoru.png\");\n/* harmony import */ var _assets_images_wallet_jiesuo_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/wallet/jiesuo.png */ \"../../packages/assets/images/wallet/jiesuo.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction WalletManagerScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_7__.useWalletStore)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation)();\n    const [localWallet, setLocalWallet] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const handleAction = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)((action, wallet)=>{\n        if (action === \"addAddress\") {\n            walletStore.setAddAccountWalletId(wallet.walletId);\n            router.push(\"/wallet/preadd-account\");\n        }\n        if (action === \"addWallet\") {\n            router.push(\"/wallet/unlock?action=addWallet\");\n        }\n        if (action === \"importWallet\") {\n            router.push(\"/wallet/import?action=importWallet\");\n        }\n    }, []);\n    // 处理地址点击，跳转到profile页面\n    const handleAddressClick = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)((wallet, account, index)=>{\n        try {\n            const params = new URLSearchParams({\n                walletId: wallet.walletId || \"\",\n                accountId: account.accountId || \"\",\n                accountName: account.name || \"地址 \".concat(index + 1)\n            });\n            console.log(\"Navigating to profile with params:\", params.toString());\n            router.push(\"/user/profile?\".concat(params.toString()));\n        } catch (error) {\n            console.error(\"Navigation error:\", error);\n            // 降级方案：直接跳转到profile页面\n            router.push(\"/user/profile\");\n        }\n    }, [\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        // const _wallet = walletStore.getLocalWallet()\n        setLocalWallet(walletStore.walletList);\n    }, [\n        walletStore.walletList\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n        \"data-at\": \"manager-screen.tsx:60\",\n        \"data-in\": \"WalletManagerScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        flex: 1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                \"data-at\": \"manager-screen.tsx:61\",\n                \"data-in\": \"WalletManagerScreen\",\n                \"data-is\": \"YStack\",\n                position: \"absolute\",\n                top: 0,\n                left: 0,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.NavBar, {\n                    title: t(\"walletManagement.addAndManage\") || \"添加和管理钱包\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.ScrollView, {\n                \"data-at\": \"manager-screen.tsx:67\",\n                \"data-in\": \"WalletManagerScreen\",\n                \"data-is\": \"ScrollView\",\n                flex: 1,\n                mt: 64,\n                height: \"80vh\",\n                children: [\n                    localWallet.map((wallet, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                            \"data-at\": \"manager-screen.tsx:69\",\n                            \"data-in\": \"WalletManagerScreen\",\n                            \"data-is\": \"YStack\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"manager-screen.tsx:70\",\n                                    \"data-in\": \"WalletManagerScreen\",\n                                    \"data-is\": \"XStack\",\n                                    mt: 32,\n                                    pl: 16,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.H5, {\n                                        \"data-at\": \"manager-screen.tsx:71\",\n                                        \"data-in\": \"WalletManagerScreen\",\n                                        \"data-is\": \"H5\",\n                                        children: [\n                                            \"钱包 \".concat(index + 1),\n                                            \" \"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                wallet.accounts.map((account, index2)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                        \"data-at\": \"manager-screen.tsx:74-81\",\n                                        \"data-in\": \"WalletManagerScreen\",\n                                        \"data-is\": \"XStack\",\n                                        my: 20,\n                                        justify: \"space-between\",\n                                        px: 16,\n                                        items: \"center\",\n                                        onPress: ()=>handleAddressClick(wallet, account, index2),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                                \"data-at\": \"manager-screen.tsx:82\",\n                                                \"data-in\": \"WalletManagerScreen\",\n                                                \"data-is\": \"XStack\",\n                                                items: \"center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.Avatar, {\n                                                        circular: true,\n                                                        size: \"$4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.Avatar.Image, {\n                                                                src: \"https://api.dicebear.com/7.x/identicon/svg?seed=\".concat(account.accountId),\n                                                                accessibilityLabel: account.accountId\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                                lineNumber: 64,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.Avatar.Fallback, {\n                                                                backgroundColor: \"$blue10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                                lineNumber: 65,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                                        \"data-at\": \"manager-screen.tsx:90\",\n                                                        \"data-in\": \"WalletManagerScreen\",\n                                                        \"data-is\": \"YStack\",\n                                                        pl: 16,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                                \"data-at\": \"manager-screen.tsx:91\",\n                                                                \"data-in\": \"WalletManagerScreen\",\n                                                                \"data-is\": \"Text\",\n                                                                fontSize: 14,\n                                                                children: account.name || \"地址 \".concat(index2 + 1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                                lineNumber: 68,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                                \"data-at\": \"manager-screen.tsx:92\",\n                                                                \"data-in\": \"WalletManagerScreen\",\n                                                                \"data-is\": \"Text\",\n                                                                fontSize: 12,\n                                                                color: \"$white11\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    (()=>{\n                                                                        let total = 0;\n                                                                        const chains = [\n                                                                            \"eth\",\n                                                                            \"bsc\",\n                                                                            \"btc\",\n                                                                            \"solana\"\n                                                                        ];\n                                                                        chains.forEach((chain)=>{\n                                                                            var _account_chain;\n                                                                            if ((_account_chain = account[chain]) === null || _account_chain === void 0 ? void 0 : _account_chain.balance) {\n                                                                                total += parseFloat(account[chain].balance) || 0;\n                                                                            }\n                                                                        });\n                                                                        return total.toFixed(4);\n                                                                    })()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                                lineNumber: 69,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__.ChevronRight, {\n                                                size: 20,\n                                                color: \"$white6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, account.accountId, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 68\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                    \"data-at\": \"manager-screen.tsx:111-117\",\n                                    \"data-in\": \"WalletManagerScreen\",\n                                    \"data-is\": \"XStack\",\n                                    my: 20,\n                                    justify: \"space-between\",\n                                    px: 16,\n                                    items: \"center\",\n                                    onPress: ()=>handleAction(\"addAddress\", wallet),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                            \"data-at\": \"manager-screen.tsx:118\",\n                                            \"data-in\": \"WalletManagerScreen\",\n                                            \"data-is\": \"XStack\",\n                                            items: \"center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                                    \"data-at\": \"manager-screen.tsx:119\",\n                                                    \"data-in\": \"WalletManagerScreen\",\n                                                    \"data-is\": \"Image\",\n                                                    source: _assets_images_wallet_add_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                                    width: 36,\n                                                    height: 36\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                                    \"data-at\": \"manager-screen.tsx:120\",\n                                                    \"data-in\": \"WalletManagerScreen\",\n                                                    \"data-is\": \"YStack\",\n                                                    pl: 16,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                                        \"data-at\": \"manager-screen.tsx:121\",\n                                                        \"data-in\": \"WalletManagerScreen\",\n                                                        \"data-is\": \"Text\",\n                                                        fontSize: 14,\n                                                        children: \"添加地址\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__.ChevronRight, {\n                                            size: 20,\n                                            color: \"$white6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, wallet.walletId, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 45\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                        \"data-at\": \"manager-screen.tsx:129\",\n                        \"data-in\": \"WalletManagerScreen\",\n                        \"data-is\": \"YStack\",\n                        height: 1,\n                        bg: \"$color6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"manager-screen.tsx:130-136\",\n                        \"data-in\": \"WalletManagerScreen\",\n                        \"data-is\": \"XStack\",\n                        my: 20,\n                        justify: \"space-between\",\n                        px: 16,\n                        items: \"center\",\n                        onPress: ()=>handleAction(\"addWallet\", \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                \"data-at\": \"manager-screen.tsx:137\",\n                                \"data-in\": \"WalletManagerScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"manager-screen.tsx:138\",\n                                        \"data-in\": \"WalletManagerScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_new_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                        width: 36,\n                                        height: 36\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                        \"data-at\": \"manager-screen.tsx:139\",\n                                        \"data-in\": \"WalletManagerScreen\",\n                                        \"data-is\": \"YStack\",\n                                        pl: 16,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"manager-screen.tsx:140\",\n                                            \"data-in\": \"WalletManagerScreen\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 14,\n                                            children: \"创建新钱包\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__.ChevronRight, {\n                                size: 20,\n                                color: \"$white6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"manager-screen.tsx:145-151\",\n                        \"data-in\": \"WalletManagerScreen\",\n                        \"data-is\": \"XStack\",\n                        my: 20,\n                        justify: \"space-between\",\n                        px: 16,\n                        items: \"center\",\n                        onPress: ()=>handleAction(\"importWallet\", \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                \"data-at\": \"manager-screen.tsx:152\",\n                                \"data-in\": \"WalletManagerScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"manager-screen.tsx:153\",\n                                        \"data-in\": \"WalletManagerScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_daoru_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                        width: 36,\n                                        height: 36\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                        \"data-at\": \"manager-screen.tsx:154\",\n                                        \"data-in\": \"WalletManagerScreen\",\n                                        \"data-is\": \"YStack\",\n                                        pl: 16,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"manager-screen.tsx:155\",\n                                            \"data-in\": \"WalletManagerScreen\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 14,\n                                            children: \"导入钱包\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__.ChevronRight, {\n                                size: 20,\n                                color: \"$white6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                        \"data-at\": \"manager-screen.tsx:160-166\",\n                        \"data-in\": \"WalletManagerScreen\",\n                        \"data-is\": \"XStack\",\n                        my: 20,\n                        justify: \"space-between\",\n                        px: 16,\n                        items: \"center\",\n                        onPress: ()=>router.push(\"/wallet/backup\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.XStack, {\n                                \"data-at\": \"manager-screen.tsx:167\",\n                                \"data-in\": \"WalletManagerScreen\",\n                                \"data-is\": \"XStack\",\n                                items: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Image, {\n                                        \"data-at\": \"manager-screen.tsx:168\",\n                                        \"data-in\": \"WalletManagerScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_jiesuo_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                        width: 36,\n                                        height: 36\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_9__.YStack, {\n                                        \"data-at\": \"manager-screen.tsx:169\",\n                                        \"data-in\": \"WalletManagerScreen\",\n                                        \"data-is\": \"YStack\",\n                                        pl: 16,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_10__.Text, {\n                                            \"data-at\": \"manager-screen.tsx:170\",\n                                            \"data-in\": \"WalletManagerScreen\",\n                                            \"data-is\": \"Text\",\n                                            fontSize: 14,\n                                            children: \"显示恢复短语\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_11__.ChevronRight, {\n                                size: 20,\n                                color: \"$white6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/manager-screen.tsx\",\n        lineNumber: 52,\n        columnNumber: 10\n    }, this);\n}\n_s(WalletManagerScreen, \"mallhakpM5siDGjHRMaX79vMqlM=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_7__.useWalletStore,\n        app_i18n__WEBPACK_IMPORTED_MODULE_8__.useTranslation\n    ];\n});\n_c = WalletManagerScreen;\nvar _c;\n$RefreshReg$(_c, \"WalletManagerScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/manager-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fmanager.tsx&page=%2Fwallet%2Fmanager!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);