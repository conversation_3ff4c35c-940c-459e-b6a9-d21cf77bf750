/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/transactionResult"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FtransactionResult.tsx&page=%2Fwallet%2FtransactionResult!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FtransactionResult.tsx&page=%2Fwallet%2FtransactionResult! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/transactionResult\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/transactionResult.tsx */ \"./pages/wallet/transactionResult.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/transactionResult\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRnRyYW5zYWN0aW9uUmVzdWx0LnRzeCZwYWdlPSUyRndhbGxldCUyRnRyYW5zYWN0aW9uUmVzdWx0ISIsIm1hcHBpbmdzIjoiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLGtGQUFzQztBQUM3RDtBQUNBO0FBQ0EsT0FBTyxJQUFVO0FBQ2pCLE1BQU0sVUFBVTtBQUNoQjtBQUNBLE9BQU87QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZjBhMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL3dhbGxldC90cmFuc2FjdGlvblJlc3VsdFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L3RyYW5zYWN0aW9uUmVzdWx0LnRzeFwiKTtcbiAgICAgIH1cbiAgICBdKTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24gKCkge1xuICAgICAgICB3aW5kb3cuX19ORVhUX1AucHVzaChbXCIvd2FsbGV0L3RyYW5zYWN0aW9uUmVzdWx0XCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FtransactionResult.tsx&page=%2Fwallet%2FtransactionResult!\n"));

/***/ }),

/***/ "../../packages/assets/images/buy/buy16.png":
/*!**************************************************!*\
  !*** ../../packages/assets/images/buy/buy16.png ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/buy16.16f56a55.png\",\"height\":26,\"width\":19,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbuy16.16f56a55.png&w=6&q=70\",\"blurWidth\":6,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTYucG5nIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy9idXkvYnV5MTYucG5nPzBhN2EiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2J1eTE2LjE2ZjU2YTU1LnBuZ1wiLFwiaGVpZ2h0XCI6MjYsXCJ3aWR0aFwiOjE5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJ1eTE2LjE2ZjU2YTU1LnBuZyZ3PTYmcT03MFwiLFwiYmx1cldpZHRoXCI6NixcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../../packages/assets/images/buy/buy16.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/eth.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/wallet/eth.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/eth.b241a439.png\",\"height\":64,\"width\":64,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Feth.b241a439.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvZXRoLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyx3TEFBd0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L2V0aC5wbmc/NjAwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZXRoLmIyNDFhNDM5LnBuZ1wiLFwiaGVpZ2h0XCI6NjQsXCJ3aWR0aFwiOjY0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmV0aC5iMjQxYTQzOS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/eth.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/res1.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/res1.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/res1.d7cf3fdd.png\",\"height\":84,\"width\":84,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fres1.d7cf3fdd.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvcmVzMS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9yZXMxLnBuZz9jNjU1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9yZXMxLmQ3Y2YzZmRkLnBuZ1wiLFwiaGVpZ2h0XCI6ODQsXCJ3aWR0aFwiOjg0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnJlczEuZDdjZjNmZGQucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/res1.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/res2.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/res2.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/res2.334abb74.png\",\"height\":84,\"width\":84,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fres2.334abb74.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvcmVzMi5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9yZXMyLnBuZz8zZWJkIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9yZXMyLjMzNGFiYjc0LnBuZ1wiLFwiaGVpZ2h0XCI6ODQsXCJ3aWR0aFwiOjg0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnJlczIuMzM0YWJiNzQucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/res2.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/res3.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/res3.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/res3.90edb86c.png\",\"height\":84,\"width\":84,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fres3.90edb86c.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvcmVzMy5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9yZXMzLnBuZz9jYTQ3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9yZXMzLjkwZWRiODZjLnBuZ1wiLFwiaGVpZ2h0XCI6ODQsXCJ3aWR0aFwiOjg0LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnJlczMuOTBlZGI4NmMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/res3.png\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/wallet.png":
/*!******************************************************!*\
  !*** ../../packages/assets/images/wallet/wallet.png ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/wallet.d1aaf7d7.png\",\"height\":72,\"width\":72,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwallet.d1aaf7d7.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvd2FsbGV0LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyw4TEFBOEwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvd2FsbGV0L3dhbGxldC5wbmc/MjJlZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvd2FsbGV0LmQxYWFmN2Q3LnBuZ1wiLFwiaGVpZ2h0XCI6NzIsXCJ3aWR0aFwiOjcyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRndhbGxldC5kMWFhZjdkNy5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/wallet.png\n"));

/***/ }),

/***/ "./pages/wallet/transactionResult.tsx":
/*!********************************************!*\
  !*** ./pages/wallet/transactionResult.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_transaction_result_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/transaction-result-screen */ \"../../packages/app/features/wallet/transaction-result-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_transaction_result_screen__WEBPACK_IMPORTED_MODULE_1__.TransactionResultScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/transactionResult.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvdHJhbnNhY3Rpb25SZXN1bHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQ3VGO0FBRXhFLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCxrR0FBdUJBOzs7OztBQUNqQztLQUZ3QkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcGFnZXMvd2FsbGV0L3RyYW5zYWN0aW9uUmVzdWx0LnRzeD9mNDVhIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0IHsgVHJhbnNhY3Rpb25SZXN1bHRTY3JlZW4gfSBmcm9tICdhcHAvZmVhdHVyZXMvd2FsbGV0L3RyYW5zYWN0aW9uLXJlc3VsdC1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8VHJhbnNhY3Rpb25SZXN1bHRTY3JlZW4gLz5cbn0iXSwibmFtZXMiOlsiVHJhbnNhY3Rpb25SZXN1bHRTY3JlZW4iLCJQYWdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/wallet/transactionResult.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/transaction-result-screen.tsx":
/*!************************************************************************!*\
  !*** ../../packages/app/features/wallet/transaction-result-screen.tsx ***!
  \************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionResultScreen: function() { return /* binding */ TransactionResultScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n/* harmony import */ var _assets_images_wallet_wallet_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/wallet.png */ \"../../packages/assets/images/wallet/wallet.png\");\n/* harmony import */ var _assets_images_wallet_res1_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/wallet/res1.png */ \"../../packages/assets/images/wallet/res1.png\");\n/* harmony import */ var _assets_images_wallet_res2_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../assets/images/wallet/res2.png */ \"../../packages/assets/images/wallet/res2.png\");\n/* harmony import */ var _assets_images_wallet_res3_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../assets/images/wallet/res3.png */ \"../../packages/assets/images/wallet/res3.png\");\n/* harmony import */ var _assets_images_buy_buy16_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../assets/images/buy/buy16.png */ \"../../packages/assets/images/buy/buy16.png\");\n/* harmony import */ var _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../assets/images/wallet/eth.png */ \"../../packages/assets/images/wallet/eth.png\");\n/* harmony import */ var react_native__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-native */ \"../../node_modules/react-native-web/dist/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst Line = (0,tamagui__WEBPACK_IMPORTED_MODULE_7__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_7__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10,\n    mb: 20\n});\n_c = Line;\nfunction TransactionResultScreen() {\n    var _currentAccount_eth, _currentAccount_bsc;\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const params = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_9__.useWalletStore)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_10__.useTranslation)();\n    // 从路由参数获取交易信息\n    const txHash = (params === null || params === void 0 ? void 0 : params.get(\"txHash\")) || \"\";\n    const amount = (params === null || params === void 0 ? void 0 : params.get(\"amount\")) || \"0\";\n    const symbol = (params === null || params === void 0 ? void 0 : params.get(\"symbol\")) || \"ETH\";\n    const toAddress = (params === null || params === void 0 ? void 0 : params.get(\"toAddress\")) || \"\";\n    const status = (params === null || params === void 0 ? void 0 : params.get(\"status\")) || \"success\";\n    const currentAccount = walletStore.currentAccount;\n    const fromAddress = (currentAccount === null || currentAccount === void 0 ? void 0 : (_currentAccount_eth = currentAccount.eth) === null || _currentAccount_eth === void 0 ? void 0 : _currentAccount_eth.address) || (currentAccount === null || currentAccount === void 0 ? void 0 : (_currentAccount_bsc = currentAccount.bsc) === null || _currentAccount_bsc === void 0 ? void 0 : _currentAccount_bsc.address) || \"\";\n    // 格式化地址显示\n    const formatAddress = (address)=>{\n        if (!address) return \"\";\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    // 格式化时间\n    const formatTime = ()=>{\n        return new Date().toLocaleString(\"zh-CN\", {\n            year: \"numeric\",\n            month: \"2-digit\",\n            day: \"2-digit\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n        \"data-at\": \"transaction-result-screen.tsx:58\",\n        \"data-in\": \"TransactionResultScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                \"data-at\": \"transaction-result-screen.tsx:59\",\n                \"data-in\": \"TransactionResultScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.NavBar, {\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                \"data-at\": \"transaction-result-screen.tsx:62\",\n                \"data-in\": \"TransactionResultScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 50,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                        \"data-at\": \"transaction-result-screen.tsx:63\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"XStack\",\n                        position: \"relative\",\n                        width: 80,\n                        margin: \"auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                                \"data-at\": \"transaction-result-screen.tsx:64\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src,\n                                width: 60,\n                                height: 60,\n                                margin: \"auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                                \"data-at\": \"transaction-result-screen.tsx:65\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src,\n                                width: 24,\n                                height: 24,\n                                position: \"absolute\",\n                                bottom: 2,\n                                right: 2\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                        \"data-at\": \"transaction-result-screen.tsx:67\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 24,\n                        fontWeight: 800,\n                        mt: 10,\n                        mb: 10,\n                        textAlign: \"center\",\n                        children: status === \"success\" ? t(\"wallet.transactionSuccess\") || \"已发送\" : t(\"wallet.transactionFailed\") || \"发送失败\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                        \"data-at\": \"transaction-result-screen.tsx:70\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 14,\n                        fontWeight: 500,\n                        color: \"$accent11\",\n                        textAlign: \"center\",\n                        children: formatTime()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                \"data-at\": \"transaction-result-screen.tsx:72\",\n                \"data-in\": \"TransactionResultScreen\",\n                \"data-is\": \"YStack\",\n                mx: 16,\n                height: 158,\n                rounded: 20,\n                borderWidth: 1,\n                borderColor: \"#242426\",\n                px: 16,\n                py: 16,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                        \"data-at\": \"transaction-result-screen.tsx:73\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mb: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                                \"data-at\": \"transaction-result-screen.tsx:74\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_wallet_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                width: 36,\n                                height: 36\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                                \"data-at\": \"transaction-result-screen.tsx:75\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"YStack\",\n                                width: \"55%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:76\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"自\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:77\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 12,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 5,\n                                        children: formatAddress(fromAddress)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                                \"data-at\": \"transaction-result-screen.tsx:79\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"YStack\",\n                                flexDirection: \"column\",\n                                alignItems: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:80\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: [\n                                            \"-\",\n                                            amount,\n                                            \" \",\n                                            symbol\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:81\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 12,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 5,\n                                        children: [\n                                            amount,\n                                            \" \",\n                                            symbol\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                        \"data-at\": \"transaction-result-screen.tsx:84\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"Image\",\n                        source: _assets_images_buy_buy16_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"].src,\n                        width: 5,\n                        height: 12,\n                        ml: 16\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                        \"data-at\": \"transaction-result-screen.tsx:85\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        mt: 20,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                                \"data-at\": \"transaction-result-screen.tsx:86\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_wallet_wallet_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                width: 36,\n                                height: 36\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                                \"data-at\": \"transaction-result-screen.tsx:87\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"YStack\",\n                                width: \"55%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:88\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"至\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:89\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 12,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 5,\n                                        children: formatAddress(toAddress)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                                \"data-at\": \"transaction-result-screen.tsx:91\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"YStack\",\n                                flexDirection: \"column\",\n                                alignItems: \"flex-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:92\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        color: \"#55AE80\",\n                                        children: [\n                                            \"+\",\n                                            amount,\n                                            \" \",\n                                            symbol\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:93\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 12,\n                                        fontWeight: \"bold\",\n                                        color: \"$accent11\",\n                                        mt: 5,\n                                        children: [\n                                            amount,\n                                            \" \",\n                                            symbol\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                \"data-at\": \"transaction-result-screen.tsx:97\",\n                \"data-in\": \"TransactionResultScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 40,\n                mb: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                        \"data-at\": \"transaction-result-screen.tsx:98\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"XStack\",\n                        mb: 20,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                \"data-at\": \"transaction-result-screen.tsx:99\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                children: \"交易散列\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                                \"data-at\": \"transaction-result-screen.tsx:100\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                    \"data-at\": \"transaction-result-screen.tsx:101\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: 500,\n                                    color: \"$accent11\",\n                                    children: formatAddress(txHash)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                        \"data-at\": \"transaction-result-screen.tsx:104\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"XStack\",\n                        mb: 20,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                \"data-at\": \"transaction-result-screen.tsx:105\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                children: \"网络\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                                \"data-at\": \"transaction-result-screen.tsx:106\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                        \"data-at\": \"transaction-result-screen.tsx:107\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: 500,\n                                        color: \"$accent11\",\n                                        children: symbol === \"ETH\" ? \"Ethereum\" : symbol === \"BNB\" ? \"BSC\" : \"Solana\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                                        \"data-at\": \"transaction-result-screen.tsx:108\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_eth_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"].src,\n                                        width: 12,\n                                        height: 12,\n                                        ml: 6\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                        \"data-at\": \"transaction-result-screen.tsx:111\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"XStack\",\n                        mb: 20,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                \"data-at\": \"transaction-result-screen.tsx:112\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                children: \"网络费用\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                                \"data-at\": \"transaction-result-screen.tsx:113\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                    \"data-at\": \"transaction-result-screen.tsx:114\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: 500,\n                                    color: \"$accent11\",\n                                    children: [\n                                        \"0.00000223 \",\n                                        symbol\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                        \"data-at\": \"transaction-result-screen.tsx:117\",\n                        \"data-in\": \"TransactionResultScreen\",\n                        \"data-is\": \"XStack\",\n                        mb: 20,\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                \"data-at\": \"transaction-result-screen.tsx:118\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                fontWeight: 500,\n                                children: \"预估合计\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                                \"data-at\": \"transaction-result-screen.tsx:119\",\n                                \"data-in\": \"TransactionResultScreen\",\n                                \"data-is\": \"XStack\",\n                                alignItems: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                    \"data-at\": \"transaction-result-screen.tsx:120\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 14,\n                                    fontWeight: 500,\n                                    color: \"$accent11\",\n                                    children: [\n                                        amount,\n                                        \" \",\n                                        symbol\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.XStack, {\n                \"data-at\": \"transaction-result-screen.tsx:125\",\n                \"data-in\": \"TransactionResultScreen\",\n                \"data-is\": \"XStack\",\n                px: 32,\n                py: 24,\n                justifyContent: \"space-between\",\n                alignItems: \"flex-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_12__.Pressable, {\n                        onPress: ()=>{\n                            // 重新发送逻辑\n                            router.push(\"/wallet/send\");\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                            \"data-at\": \"transaction-result-screen.tsx:130\",\n                            \"data-in\": \"TransactionResultScreen\",\n                            \"data-is\": \"YStack\",\n                            alignItems: \"center\",\n                            width: 80,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.View, {\n                                    \"data-at\": \"transaction-result-screen.tsx:131-139\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"View\",\n                                    width: 48,\n                                    height: 48,\n                                    borderRadius: 24,\n                                    backgroundColor: \"#2A2D36\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    mb: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                                        \"data-at\": \"transaction-result-screen.tsx:140\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_res1_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                        width: 40,\n                                        height: 40\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                    \"data-at\": \"transaction-result-screen.tsx:142\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 12,\n                                    fontWeight: 500,\n                                    textAlign: \"center\",\n                                    color: \"white\",\n                                    lineHeight: 16,\n                                    children: \"重新发送\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_12__.Pressable, {\n                        onPress: ()=>{\n                            // 查看钱包资产\n                            router.push(\"/\");\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                            \"data-at\": \"transaction-result-screen.tsx:152\",\n                            \"data-in\": \"TransactionResultScreen\",\n                            \"data-is\": \"YStack\",\n                            alignItems: \"center\",\n                            width: 80,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.View, {\n                                    \"data-at\": \"transaction-result-screen.tsx:153-161\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"View\",\n                                    width: 48,\n                                    height: 48,\n                                    borderRadius: 24,\n                                    backgroundColor: \"#2A2D36\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    mb: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                                        \"data-at\": \"transaction-result-screen.tsx:162\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_res2_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"].src,\n                                        width: 40,\n                                        height: 40\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                    \"data-at\": \"transaction-result-screen.tsx:164\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 12,\n                                    fontWeight: 500,\n                                    textAlign: \"center\",\n                                    color: \"white\",\n                                    lineHeight: 16,\n                                    children: [\n                                        \"查看钱包中\",\n                                        \"\\n\",\n                                        \"的资产\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_native__WEBPACK_IMPORTED_MODULE_12__.Pressable, {\n                        onPress: ()=>{\n                            // 在区块链浏览器中查看\n                            if (txHash) {\n                                const explorerUrl = symbol === \"ETH\" ? \"https://etherscan.io/tx/\".concat(txHash) : symbol === \"BNB\" ? \"https://bscscan.com/tx/\".concat(txHash) : \"https://explorer.solana.com/tx/\".concat(txHash);\n                                // 这里可以打开外部链接\n                                console.log(\"Open explorer:\", explorerUrl);\n                            }\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_11__.YStack, {\n                            \"data-at\": \"transaction-result-screen.tsx:182\",\n                            \"data-in\": \"TransactionResultScreen\",\n                            \"data-is\": \"YStack\",\n                            alignItems: \"center\",\n                            width: 80,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.View, {\n                                    \"data-at\": \"transaction-result-screen.tsx:183-191\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"View\",\n                                    width: 48,\n                                    height: 48,\n                                    borderRadius: 24,\n                                    backgroundColor: \"#2A2D36\",\n                                    alignItems: \"center\",\n                                    justifyContent: \"center\",\n                                    mb: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Image, {\n                                        \"data-at\": \"transaction-result-screen.tsx:192\",\n                                        \"data-in\": \"TransactionResultScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_res3_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"].src,\n                                        width: 40,\n                                        height: 40\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_7__.Text, {\n                                    \"data-at\": \"transaction-result-screen.tsx:194\",\n                                    \"data-in\": \"TransactionResultScreen\",\n                                    \"data-is\": \"Text\",\n                                    fontSize: 12,\n                                    fontWeight: 500,\n                                    textAlign: \"center\",\n                                    color: \"white\",\n                                    lineHeight: 16,\n                                    children: [\n                                        \"在\",\n                                        \"\\n\",\n                                        \"Etherscan\",\n                                        \"\\n\",\n                                        \"上查看\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/transaction-result-screen.tsx\",\n        lineNumber: 53,\n        columnNumber: 10\n    }, this);\n}\n_s(TransactionResultScreen, \"1FlvJN6nKI+sedWxTbWp0HaRrmM=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_8__.useSearchParams,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_9__.useWalletStore,\n        app_i18n__WEBPACK_IMPORTED_MODULE_10__.useTranslation\n    ];\n});\n_c1 = TransactionResultScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"Line\");\n$RefreshReg$(_c1, \"TransactionResultScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/transaction-result-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2FtransactionResult.tsx&page=%2Fwallet%2FtransactionResult!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);