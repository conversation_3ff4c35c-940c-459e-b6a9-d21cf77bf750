/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/wallet/backup"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fbackup.tsx&page=%2Fwallet%2Fbackup!":
/*!*****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fbackup.tsx&page=%2Fwallet%2Fbackup! ***!
  \*****************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/wallet/backup\",\n      function () {\n        return __webpack_require__(/*! ./pages/wallet/backup.tsx */ \"./pages/wallet/backup.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/wallet/backup\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRndhbGxldCUyRmJhY2t1cC50c3gmcGFnZT0lMkZ3YWxsZXQlMkZiYWNrdXAhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsNERBQTJCO0FBQ2xEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9jZjY1Il0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvd2FsbGV0L2JhY2t1cFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvd2FsbGV0L2JhY2t1cC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL3dhbGxldC9iYWNrdXBcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fbackup.tsx&page=%2Fwallet%2Fbackup!\n"));

/***/ }),

/***/ "../../packages/assets/images/wallet/copy.png":
/*!****************************************************!*\
  !*** ../../packages/assets/images/wallet/copy.png ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/copy.5158dae4.png\",\"height\":40,\"width\":40,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcopy.5158dae4.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy93YWxsZXQvY29weS5wbmciLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLENBQUMsMExBQTBMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9hc3NldHMvaW1hZ2VzL3dhbGxldC9jb3B5LnBuZz85MmMyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9jb3B5LjUxNThkYWU0LnBuZ1wiLFwiaGVpZ2h0XCI6NDAsXCJ3aWR0aFwiOjQwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmNvcHkuNTE1OGRhZTQucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/wallet/copy.png\n"));

/***/ }),

/***/ "./pages/wallet/backup.tsx":
/*!*********************************!*\
  !*** ./pages/wallet/backup.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_wallet_backup_mnemonic_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/wallet/backup-mnemonic-screen */ \"../../packages/app/features/wallet/backup-mnemonic-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_wallet_backup_mnemonic_screen__WEBPACK_IMPORTED_MODULE_1__.BackupMnemonicScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/wallet/backup.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy93YWxsZXQvYmFja3VwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUVpRjtBQUVsRSxTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QsNEZBQW9CQTs7Ozs7QUFDOUI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3dhbGxldC9iYWNrdXAudHN4PzMwMDUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IEJhY2t1cE1uZW1vbmljU2NyZWVuIH0gZnJvbSAnYXBwL2ZlYXR1cmVzL3dhbGxldC9iYWNrdXAtbW5lbW9uaWMtc2NyZWVuJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQYWdlKCkge1xuICByZXR1cm4gPEJhY2t1cE1uZW1vbmljU2NyZWVuIC8+XG59Il0sIm5hbWVzIjpbIkJhY2t1cE1uZW1vbmljU2NyZWVuIiwiUGFnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./pages/wallet/backup.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/wallet/backup-mnemonic-screen.tsx":
/*!*********************************************************************!*\
  !*** ../../packages/app/features/wallet/backup-mnemonic-screen.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackupMnemonicScreen: function() { return /* binding */ BackupMnemonicScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_wallet_copy_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/wallet/copy.png */ \"../../packages/assets/images/wallet/copy.png\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var app_stores_walletStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! app/stores/walletStore */ \"../../packages/app/stores/walletStore.ts\");\n/* harmony import */ var app_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/i18n */ \"../../packages/app/i18n/index.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction BackupMnemonicScreen() {\n    _s();\n    const params = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const themeName = (0,tamagui__WEBPACK_IMPORTED_MODULE_4__.useThemeName)();\n    const oppositeColor = themeName === \"light\" ? \"$color12\" : \"$color1\";\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const walletStore = (0,app_stores_walletStore__WEBPACK_IMPORTED_MODULE_5__.useWalletStore)();\n    const { t } = (0,app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [copyStatus, setCopyStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"idle\");\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if ((params === null || params === void 0 ? void 0 : params.get(\"action\")) === \"importWallet\") {\n        // 导入钱包\n        } else {\n            walletStore.createNewVaultAndGetSeedPhrase();\n        }\n    }, [\n        params\n    ]);\n    const handleCopy = async ()=>{\n        if (!walletStore.lastestMnemonic) return;\n        try {\n            await navigator.clipboard.writeText(walletStore.lastestMnemonic);\n            setCopyStatus(\"success\");\n            setTimeout(()=>setCopyStatus(\"idle\"), 1000);\n        } catch (e) {\n        // 可选：处理复制失败\n        }\n    };\n    const handleToHome = ()=>{\n        router.push(\"/user/home\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n        \"data-at\": \"backup-mnemonic-screen.tsx:47\",\n        \"data-in\": \"BackupMnemonicScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        flex: 1,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"backup-mnemonic-screen.tsx:48\",\n                \"data-in\": \"BackupMnemonicScreen\",\n                \"data-is\": \"YStack\",\n                flex: 1,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.NavBar, {\n                        title: t(\"walletManagement.backupWallet\") || \"备份您的钱包\",\n                        onBack: ()=>router.back()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"backup-mnemonic-screen.tsx:53\",\n                        \"data-in\": \"BackupMnemonicScreen\",\n                        \"data-is\": \"YStack\",\n                        mt: 32,\n                        px: 26,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"backup-mnemonic-screen.tsx:54\",\n                                \"data-in\": \"BackupMnemonicScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 14,\n                                color: \"$color10\",\n                                children: \"绝对不要分享这些词。任何得知它们的人都可以窃取您所有的加密货币。Coinbase 绝不会要求您提供这些信息。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                \"data-at\": \"backup-mnemonic-screen.tsx:58\",\n                                \"data-in\": \"BackupMnemonicScreen\",\n                                \"data-is\": \"Text\",\n                                mt: 24,\n                                fontSize: 14,\n                                color: \"$color10\",\n                                children: \"以下 12 个单词是您钱包的恢复短语。该短语可让您在丢失设备时恢复钱包。将其备份到iCloud(推荐)或记下来。或同时采用这两种方式。\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                        \"data-at\": \"backup-mnemonic-screen.tsx:63\",\n                        \"data-in\": \"BackupMnemonicScreen\",\n                        \"data-is\": \"YStack\",\n                        px: 16,\n                        py: 40,\n                        rowGap: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"backup-mnemonic-screen.tsx:64-74\",\n                                \"data-in\": \"BackupMnemonicScreen\",\n                                \"data-is\": \"XStack\",\n                                height: 110,\n                                px: 12,\n                                py: 12,\n                                borderRadius: 12,\n                                borderWidth: 1,\n                                borderColor: \"$color9\",\n                                borderStyle: \"solid\",\n                                position: \"relative\",\n                                overflow: \"hidden\",\n                                children: [\n                                    !showPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.View, {\n                                        \"data-at\": \"backup-mnemonic-screen.tsx:76-89\",\n                                        \"data-in\": \"BackupMnemonicScreen\",\n                                        \"data-is\": \"View\",\n                                        position: \"absolute\",\n                                        top: 0,\n                                        left: 0,\n                                        right: 0,\n                                        bottom: 0,\n                                        zIndex: 10,\n                                        bg: \"rgba(255,255,255,0.2)\" // 可根据主题调整\n                                        ,\n                                        style: {\n                                            backdropFilter: \"blur(8px)\",\n                                            WebkitBackdropFilter: \"blur(8px)\",\n                                            pointerEvents: \"none\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 31\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"backup-mnemonic-screen.tsx:92\",\n                                        \"data-in\": \"BackupMnemonicScreen\",\n                                        \"data-is\": \"Text\",\n                                        flex: 1,\n                                        fontSize: 14,\n                                        lineHeight: 24,\n                                        mt: 16,\n                                        children: walletStore.lastestMnemonic\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                                        \"data-at\": \"backup-mnemonic-screen.tsx:95\",\n                                        \"data-in\": \"BackupMnemonicScreen\",\n                                        \"data-is\": \"YStack\",\n                                        width: 30,\n                                        bg: \"$color2\",\n                                        items: \"center\",\n                                        justify: \"center\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                                        \"data-at\": \"backup-mnemonic-screen.tsx:96-109\",\n                                        \"data-in\": \"BackupMnemonicScreen\",\n                                        \"data-is\": \"YStack\",\n                                        position: \"absolute\",\n                                        zIndex: 20,\n                                        right: 0,\n                                        top: 0,\n                                        bottom: 0,\n                                        width: 30,\n                                        bg: \"$color2\",\n                                        items: \"center\",\n                                        justify: \"center\",\n                                        onPress: ()=>{\n                                            setShowPassword(!showPassword);\n                                        },\n                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_8__.Eye, {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 31\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_8__.EyeOff, {\n                                            size: 16,\n                                            opacity: 0.8\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 51\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.XStack, {\n                                \"data-at\": \"backup-mnemonic-screen.tsx:113\",\n                                \"data-in\": \"BackupMnemonicScreen\",\n                                \"data-is\": \"XStack\",\n                                mt: 12,\n                                onPress: handleCopy,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Image, {\n                                        \"data-at\": \"backup-mnemonic-screen.tsx:114\",\n                                        \"data-in\": \"BackupMnemonicScreen\",\n                                        \"data-is\": \"Image\",\n                                        source: _assets_images_wallet_copy_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                        width: 20,\n                                        height: 20,\n                                        mr: 8\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                                        \"data-at\": \"backup-mnemonic-screen.tsx:115\",\n                                        \"data-in\": \"BackupMnemonicScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        children: copyStatus === \"success\" ? t(\"success.copySuccess\") || \"复制成功\" : t(\"walletManagement.copyToClipboard\") || \"复制到剪贴板\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.YStack, {\n                \"data-at\": \"backup-mnemonic-screen.tsx:123\",\n                \"data-in\": \"BackupMnemonicScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                py: 24,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_7__.ConfirmButton, {\n                    onPress: handleToHome,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_4__.Text, {\n                        \"data-at\": \"backup-mnemonic-screen.tsx:125\",\n                        \"data-in\": \"BackupMnemonicScreen\",\n                        \"data-is\": \"Text\",\n                        color: oppositeColor,\n                        fontSize: 14,\n                        fontWeight: 700,\n                        children: \"已备份\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/wallet/backup-mnemonic-screen.tsx\",\n        lineNumber: 40,\n        columnNumber: 10\n    }, this);\n}\n_s(BackupMnemonicScreen, \"4ViWrtXKZVcxSuMMM9jL3Eq09Oc=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        tamagui__WEBPACK_IMPORTED_MODULE_4__.useThemeName,\n        solito_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        app_stores_walletStore__WEBPACK_IMPORTED_MODULE_5__.useWalletStore,\n        app_i18n__WEBPACK_IMPORTED_MODULE_6__.useTranslation\n    ];\n});\n_c = BackupMnemonicScreen;\nvar _c;\n$RefreshReg$(_c, \"BackupMnemonicScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/wallet/backup-mnemonic-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fwallet%2Fbackup.tsx&page=%2Fwallet%2Fbackup!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);