/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/user/settingAccount"],{

/***/ "../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fuser%2FsettingAccount.tsx&page=%2Fuser%2FsettingAccount!":
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fuser%2FsettingAccount.tsx&page=%2Fuser%2FsettingAccount! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/user/settingAccount\",\n      function () {\n        return __webpack_require__(/*! ./pages/user/settingAccount.tsx */ \"./pages/user/settingAccount.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/user/settingAccount\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1jbGllbnQtcGFnZXMtbG9hZGVyLmpzP2Fic29sdXRlUGFnZVBhdGg9JTJGVXNlcnMlMkZzeHclMkZEb2N1bWVudHMlMkZQcml2YXRlJTJGYmxvY2stY2hhaW4tcHJvamVjdCUyRmNvaW5iYXNlX3YyJTJGYXBwcyUyRm5leHQlMkZwYWdlcyUyRnVzZXIlMkZzZXR0aW5nQWNjb3VudC50c3gmcGFnZT0lMkZ1c2VyJTJGc2V0dGluZ0FjY291bnQhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsd0VBQWlDO0FBQ3hEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz80MGExIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvdXNlci9zZXR0aW5nQWNjb3VudFwiLFxuICAgICAgZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gcmVxdWlyZShcIi4vcGFnZXMvdXNlci9zZXR0aW5nQWNjb3VudC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL3VzZXIvc2V0dGluZ0FjY291bnRcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fuser%2FsettingAccount.tsx&page=%2Fuser%2FsettingAccount!\n"));

/***/ }),

/***/ "../../packages/assets/images/user/user3.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/user/user3.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/user3.790fcf69.png\",\"height\":64,\"width\":64,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuser3.790fcf69.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy91c2VyL3VzZXIzLnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyw0TEFBNEwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvdXNlci91c2VyMy5wbmc/MzM4YiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvdXNlcjMuNzkwZmNmNjkucG5nXCIsXCJoZWlnaHRcIjo2NCxcIndpZHRoXCI6NjQsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGdXNlcjMuNzkwZmNmNjkucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///../../packages/assets/images/user/user3.png\n"));

/***/ }),

/***/ "../../packages/assets/images/user/user4.png":
/*!***************************************************!*\
  !*** ../../packages/assets/images/user/user4.png ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\"src\":\"/_next/static/media/user4.683cc337.png\",\"height\":156,\"width\":156,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuser4.683cc337.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vLi4vcGFja2FnZXMvYXNzZXRzL2ltYWdlcy91c2VyL3VzZXI0LnBuZyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsQ0FBQyw4TEFBOEwiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL3BhY2thZ2VzL2Fzc2V0cy9pbWFnZXMvdXNlci91c2VyNC5wbmc/ZmE0NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvdXNlcjQuNjgzY2MzMzcucG5nXCIsXCJoZWlnaHRcIjoxNTYsXCJ3aWR0aFwiOjE1NixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZ1c2VyNC42ODNjYzMzNy5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///../../packages/assets/images/user/user4.png\n"));

/***/ }),

/***/ "./pages/user/settingAccount.tsx":
/*!***************************************!*\
  !*** ./pages/user/settingAccount.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Page; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var app_features_user_setting_account_screen__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! app/features/user/setting-account-screen */ \"../../packages/app/features/user/setting-account-screen.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Page() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(app_features_user_setting_account_screen__WEBPACK_IMPORTED_MODULE_1__.SettingAccountScreen, {}, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/apps/next/pages/user/settingAccount.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy91c2VyL3NldHRpbmdBY2NvdW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUMrRTtBQUVoRSxTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QsMEZBQW9CQTs7Ozs7QUFDOUI7S0FGd0JDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3BhZ2VzL3VzZXIvc2V0dGluZ0FjY291bnQudHN4PzY0ODQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgeyBTZXR0aW5nQWNjb3VudFNjcmVlbiB9IGZyb20gJ2FwcC9mZWF0dXJlcy91c2VyL3NldHRpbmctYWNjb3VudC1zY3JlZW4nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKSB7XG4gIHJldHVybiA8U2V0dGluZ0FjY291bnRTY3JlZW4gLz5cbn1cbiJdLCJuYW1lcyI6WyJTZXR0aW5nQWNjb3VudFNjcmVlbiIsIlBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/user/settingAccount.tsx\n"));

/***/ }),

/***/ "../../packages/app/features/user/setting-account-screen.tsx":
/*!*******************************************************************!*\
  !*** ../../packages/app/features/user/setting-account-screen.tsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingAccountScreen: function() { return /* binding */ SettingAccountScreen; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _my_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @my/ui */ \"../../packages/ui/src/index.tsx\");\n/* harmony import */ var solito_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! solito/navigation */ \"../../node_modules/solito/navigation/index.js\");\n/* harmony import */ var tamagui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tamagui */ \"../../node_modules/tamagui/dist/esm/index.mjs\");\n/* harmony import */ var _tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tamagui/lucide-icons */ \"../../node_modules/@tamagui/lucide-icons/dist/esm/index.mjs\");\n/* harmony import */ var _assets_images_user_user3_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../assets/images/user/user3.png */ \"../../packages/assets/images/user/user3.png\");\n/* harmony import */ var _assets_images_user_user4_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../assets/images/user/user4.png */ \"../../packages/assets/images/user/user4.png\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Line = (0,tamagui__WEBPACK_IMPORTED_MODULE_3__.styled)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n    width: \"100%\",\n    height: 1,\n    backgroundColor: \"#212224\",\n    mt: 10\n});\n_c = Line;\nfunction SettingAccountScreen() {\n    _s();\n    const router = (0,solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n        \"data-at\": \"setting-account-screen.tsx:19\",\n        \"data-in\": \"SettingAccountScreen\",\n        \"data-is\": \"YStack\",\n        bg: \"$background\",\n        height: 800,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                \"data-at\": \"setting-account-screen.tsx:20\",\n                \"data-in\": \"SettingAccountScreen\",\n                \"data-is\": \"XStack\",\n                justifyContent: \"space-between\",\n                items: \"center\",\n                pr: 16,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.NavBar, {\n                    title: \"\",\n                    onBack: ()=>router.back()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.YStack, {\n                \"data-at\": \"setting-account-screen.tsx:23\",\n                \"data-in\": \"SettingAccountScreen\",\n                \"data-is\": \"YStack\",\n                px: 16,\n                mt: 20,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"setting-account-screen.tsx:24\",\n                        \"data-in\": \"SettingAccountScreen\",\n                        \"data-is\": \"XStack\",\n                        justifyContent: \"space-between\",\n                        items: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                \"data-at\": \"setting-account-screen.tsx:25\",\n                                \"data-in\": \"SettingAccountScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 24,\n                                fontWeight: 800,\n                                children: \"选择您的用户名\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                \"data-at\": \"setting-account-screen.tsx:26\",\n                                \"data-in\": \"SettingAccountScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_user_user3_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"].src,\n                                width: 32,\n                                height: 32\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"setting-account-screen.tsx:28\",\n                        \"data-in\": \"SettingAccountScreen\",\n                        \"data-is\": \"Text\",\n                        fontSize: 14,\n                        color: \"$accent11\",\n                        mt: 10,\n                        children: \"做出明智的选择!您每年只能更改一次。\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"setting-account-screen.tsx:29\",\n                        \"data-in\": \"SettingAccountScreen\",\n                        \"data-is\": \"Text\",\n                        mt: 30,\n                        mb: 10,\n                        fontSize: 14,\n                        fontWeight: 500,\n                        children: \"用户名\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                        \"data-at\": \"setting-account-screen.tsx:30\",\n                        \"data-in\": \"SettingAccountScreen\",\n                        \"data-is\": \"View\",\n                        position: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                \"data-at\": \"setting-account-screen.tsx:31-32\",\n                                \"data-in\": \"SettingAccountScreen\",\n                                \"data-is\": \"Input\",\n                                width: \"100%\",\n                                height: 54,\n                                borderColor: \"#92929A\",\n                                borderWidth: 1,\n                                borderRadius: 10,\n                                bg: \"transparent\",\n                                fontWeight: \"bold\",\n                                p: 16,\n                                focusStyle: {\n                                    borderColor: \"transparent\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                \"data-at\": \"setting-account-screen.tsx:34\",\n                                \"data-in\": \"SettingAccountScreen\",\n                                \"data-is\": \"Text\",\n                                position: \"absolute\",\n                                right: 16,\n                                top: 16,\n                                color: \"$accent11\",\n                                fontSize: 14,\n                                children: \".cb.id\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                        \"data-at\": \"setting-account-screen.tsx:36\",\n                        \"data-in\": \"SettingAccountScreen\",\n                        \"data-is\": \"Text\",\n                        mt: 10,\n                        mb: 30,\n                        fontSize: 12,\n                        fontWeight: 500,\n                        children: \"由 ENS 提供技术支持\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"setting-account-screen.tsx:37\",\n                        \"data-in\": \"SettingAccountScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Image, {\n                                \"data-at\": \"setting-account-screen.tsx:38\",\n                                \"data-in\": \"SettingAccountScreen\",\n                                \"data-is\": \"Image\",\n                                source: _assets_images_user_user4_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"].src,\n                                width: 78,\n                                height: 78\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.View, {\n                                \"data-at\": \"setting-account-screen.tsx:39\",\n                                \"data-in\": \"SettingAccountScreen\",\n                                \"data-is\": \"View\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"setting-account-screen.tsx:40\",\n                                        \"data-in\": \"SettingAccountScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: \"bold\",\n                                        children: \"使用您自己的 ENS\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"setting-account-screen.tsx:41\",\n                                        \"data-in\": \"SettingAccountScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 12,\n                                        fontWeight: 500,\n                                        color: \"$accent11\",\n                                        mt: 5,\n                                        mb: 5,\n                                        children: \"您可以将您的 ENS 名称导入 Coinbase Wallet。\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"setting-account-screen.tsx:42\",\n                                        \"data-in\": \"SettingAccountScreen\",\n                                        \"data-is\": \"Text\",\n                                        fontSize: 14,\n                                        fontWeight: 500,\n                                        color: \"#3C72F9\",\n                                        children: \"了解操作方式\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.XStack, {\n                        \"data-at\": \"setting-account-screen.tsx:45\",\n                        \"data-in\": \"SettingAccountScreen\",\n                        \"data-is\": \"XStack\",\n                        alignItems: \"center\",\n                        space: \"$2\",\n                        mt: 16,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                \"data-at\": \"setting-account-screen.tsx:46\",\n                                \"data-in\": \"SettingAccountScreen\",\n                                \"data-is\": \"Checkbox\",\n                                size: \"$4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Checkbox.Indicator, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tamagui_lucide_icons__WEBPACK_IMPORTED_MODULE_6__.Check, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                \"data-at\": \"setting-account-screen.tsx:51\",\n                                \"data-in\": \"SettingAccountScreen\",\n                                \"data-is\": \"Text\",\n                                fontSize: 12,\n                                fontWeight: 500,\n                                children: [\n                                    \"我同意\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                        \"data-at\": \"setting-account-screen.tsx:51\",\n                                        \"data-in\": \"SettingAccountScreen\",\n                                        \"data-is\": \"Text\",\n                                        color: \"#3C72F9\",\n                                        children: \"子域条款\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 137\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_my_ui__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        \"data-at\": \"setting-account-screen.tsx:53-58\",\n                        \"data-in\": \"SettingAccountScreen\",\n                        \"data-is\": \"Button\",\n                        my: 40,\n                        rounded: 30,\n                        onPress: ()=>{\n                            console.log(111);\n                        },\n                        style: {\n                            // backgroundColor: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',\n                            background: \"linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tamagui__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                            \"data-at\": \"setting-account-screen.tsx:59\",\n                            \"data-in\": \"SettingAccountScreen\",\n                            \"data-is\": \"Text\",\n                            fontSize: 14,\n                            fontWeight: \"bold\",\n                            color: \"$black1\",\n                            children: \"申请我的用户名\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Private/block-chain-project/coinbase_v2/packages/app/features/user/setting-account-screen.tsx\",\n        lineNumber: 15,\n        columnNumber: 10\n    }, this);\n}\n_s(SettingAccountScreen, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        solito_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c1 = SettingAccountScreen;\nvar _c, _c1;\n$RefreshReg$(_c, \"Line\");\n$RefreshReg$(_c1, \"SettingAccountScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///../../packages/app/features/user/setting-account-screen.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fsxw%2FDocuments%2FPrivate%2Fblock-chain-project%2Fcoinbase_v2%2Fapps%2Fnext%2Fpages%2Fuser%2FsettingAccount.tsx&page=%2Fuser%2FsettingAccount!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);