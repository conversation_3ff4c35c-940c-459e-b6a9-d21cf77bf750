import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON>vBar,
  Paragraph,
  SwitchThemeButton,
  XStack,
  YStack,
} from '@my/ui'
import { ChevronLeft, ArrowLeft, ArrowRight, ChevronRight } from '@tamagui/lucide-icons'
import { useRouter } from 'solito/navigation'
import { Text, Image, Input } from 'tamagui'
import set1Icon from '../../../assets/images/wallet/set1.png'
import set3Icon from '../../../assets/images/wallet/set3.png'
import set4Icon from '../../../assets/images/wallet/set4.png'
import set5Icon from '../../../assets/images/wallet/set5.png'
import set6Icon from '../../../assets/images/wallet/set6.png'
import set7Icon from '../../../assets/images/wallet/set7.png'
import userAvatar from '../../../assets/images/wallet/user.png'
import iconAdd from '../../../assets/images/wallet/add.png'

export function SettingScreen() {
  const router = useRouter()

  return (
    <YStack bg="$background">
      <NavBar title="Settings" onBack={() => router.back()} />
      <XStack pl={16} width="100%">
        <Input placeholder="Search" width="100%" rounded={30}></Input>
      </XStack>
      <XStack mt={32} pl={16} items="center">
        <Image source={set1Icon.src} width={12} height={12} mr={6} />
        <H5>钱包</H5>
      </XStack>
      <XStack
        bg="#001032"
        rounded={20}
        height={66}
        my={20}
        justify="space-between"
        px={16}
        items={'center'}
        onPress={() => router.push('/user/profile')}
      >
        <XStack items={'center'}>
          <Avatar>
            <Avatar.Image src={userAvatar.src} width={36} height={36} />
          </Avatar>
          <YStack pl={16}>
            <Text fontSize={16} color="#8CA5F7">
              地址 1
            </Text>
            <Text fontSize={16} color="#3873F3">
              恢复短语、个人资料、人脉等
            </Text>
          </YStack>
        </XStack>
        <ChevronRight size={20} color="$white6" />
      </XStack>
      <XStack
        justify="space-between"
        px={16}
        items={'center'}
        bg="#141519"
        rounded={20}
        height={66}
      >
        <XStack items={'center'} onPress={() => router.push('/wallet/manager')}>
          <Image source={iconAdd.src} width={20} height={20} />
          <YStack pl={16}>
            <Text fontSize={16}>管理所有钱包</Text>
          </YStack>
        </XStack>
        <ChevronRight size={20} color="$white6" />
      </XStack>
      <XStack
        my={20}
        justify="space-between"
        px={16}
        items={'center'}
        onPress={() => router.push('/wallet/chooseNet')}
      >
        <XStack items={'center'}>
          <Image source={set3Icon.src} width={12} height={12} />
          <YStack pl={16}>
            <Text fontSize={16}>网络</Text>
            <Text fontSize={16} color="$accent11">
              编辑或添加网络
            </Text>
          </YStack>
        </XStack>
        <ChevronRight size={20} color="$white6" />
      </XStack>
      <XStack
        my={10}
        justify="space-between"
        px={16}
        items={'center'}
        onPress={() => router.push('/wallet/safe')}
      >
        <XStack items={'center'}>
          <Image source={set4Icon.src} width={12} height={12} />
          <YStack pl={16}>
            <Text fontSize={16}>安全</Text>
            <Text fontSize={16} color="$accent11">
              密码、备份、面容 ID
            </Text>
          </YStack>
        </XStack>
        <ChevronRight size={20} color="$white6" />
      </XStack>
      <XStack
        my={10}
        justify="space-between"
        px={16}
        items={'center'}
        onPress={() => router.push('/wallet/display')}
      >
        <XStack items={'center'}>
          <Image source={set5Icon.src} width={12} height={12} />
          <YStack pl={16}>
            <Text fontSize={16}>显示</Text>
            <Text fontSize={16} color="$accent11">
              深色模式、货币、余额、语言
            </Text>
          </YStack>
        </XStack>
        <ChevronRight size={20} color="$white6" />
      </XStack>
      <XStack
        my={10}
        justify="space-between"
        px={16}
        items={'center'}
        onPress={() => router.push('/wallet/buyTransfer')}
      >
        <XStack items={'center'}>
          <Image source={set6Icon.src} width={12} height={12} />
          <YStack pl={16}>
            <Text fontSize={16}>连接到 Coinbase</Text>
            <Text fontSize={16} color="$accent11">
              从 Coinbase 购买或转移
            </Text>
          </YStack>
        </XStack>
        <ChevronRight size={20} color="$white6" />
      </XStack>
      <XStack
        my={10}
        justify="space-between"
        px={16}
        items={'center'}
        onPress={() => router.push('/wallet/notice')}
      >
        <XStack items={'center'}>
          <Image source={set7Icon.src} width={12} height={12} />
          <YStack pl={16}>
            <Text fontSize={16}>通知</Text>
            <Text fontSize={16} color="$accent11">
              偏好设置、通知类型
            </Text>
          </YStack>
        </XStack>
        <ChevronRight size={20} color="$white6" />
      </XStack>
    </YStack>
  )
}
