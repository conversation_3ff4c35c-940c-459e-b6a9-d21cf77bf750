import {
  NavBar,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, styled } from 'tamagui'
import net1Icon from '../../../assets/images/wallet/net1.png'
import net2Icon from '../../../assets/images/wallet/net2.png'
import net3Icon from '../../../assets/images/wallet/net3.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { FooterNavBar } from '../home/<USER>'

const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 20,
})
const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2,
})
const ActiveUnderline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF',
})

export function NetworkScreen() {
  const router = useRouter()
  const tabList = ['全部', '交换', '赚取', '社交媒体', '管理', '监听']
  const [currentTab, setCurrentTab] = useState(0)

  // 全部应用数据
  const allAppsData = [
    {
      id: 1,
      name: 'Aerodrome',
      desc: '交易资产',
      isSelected: true,
      url: 'https://aerodrome.finance/',
      icon: 'https://aerodrome.finance/favicon.ico',
    },
    {
      id: 2,
      name: 'Uniswap',
      desc: '通过汇集做市交换代币并赚取费用',
      isSelected: false,
      url: 'https://app.uniswap.org/swap?disableNFTs=true',
      icon: 'https://app.uniswap.org/favicon.ico',
    },
    {
      id: 3,
      name: 'Toshi Mart',
      desc: 'Base 上的模因币生成器和交易平台。',
      isSelected: true,
      url: 'https://toshi.fun/',
      icon: 'https://toshi.fun/favicon.ico',
    },
    {
      id: 4,
      name: 'Matcha',
      desc: '0x 开发的 DEX 聚合器',
      isSelected: true,
      url: 'https://matcha.xyz/',
      icon: 'https://matcha.xyz/favicon.ico',
    },
    {
      id: 5,
      name: 'Alien Base',
      desc: '为非传统地球人而打造的 Base 原生去中心化交易所',
      isSelected: false,
      url: 'https://app.alienbase.xyz/',
      icon: 'https://app.alienbase.xyz/favicon.ico',
    },
    {
      id: 6,
      name: 'PancakeSwap',
      desc: '交易并赚取加密货币',
      isSelected: false,
      url: 'https://pancakeswap.finance/',
      icon: 'https://pancakeswap.finance/favicon.ico',
    },
    {
      id: 7,
      name: 'Seamless Protocol',
      desc: '实现收益最大化',
      isSelected: true,
      url: 'https://seamlessprotocol.com/',
      icon: 'https://seamlessprotocol.com/favicon.ico',
    },
    {
      id: 8,
      name: 'Plaza Finance',
      desc: '债券和杠杆',
      isSelected: true,
      url: 'https://plaza.finance/',
      icon: 'https://plaza.finance/favicon.ico',
    },
    {
      id: 9,
      name: 'MetaLend',
      desc: '在 5 分钟内实现加密货币收益最大化',
      isSelected: true,
      url: 'https://metalend.fi/',
      icon: 'https://metalend.fi/favicon.ico',
    },
    {
      id: 10,
      name: 'DIMO',
      desc: '更聪明地驾驶并获得奖励',
      isSelected: false,
      url: 'https://dimo.zone/',
      icon: 'https://dimo.zone/favicon.ico',
    },
    {
      id: 11,
      name: 'ether.fi',
      desc: '通过 ether.fi 质押、消费、赚钱',
      isSelected: false,
      url: 'https://www.ether.fi/',
      icon: 'https://www.ether.fi/favicon.ico',
    },
    {
      id: 12,
      name: 'Moonwell',
      desc: '使贷款变得简单',
      isSelected: false,
      url: 'https://moonwell.fi/',
      icon: 'https://moonwell.fi/favicon.ico',
    },
  ]

  // 交换应用数据
  const exchangeAppsData = [
    {
      id: 1,
      name: 'Aerodrome',
      desc: '交易资产',
      isSelected: true,
      url: 'https://aerodrome.finance/',
      icon: 'https://aerodrome.finance/favicon.ico',
    },
    {
      id: 2,
      name: 'Uniswap',
      desc: '通过汇集做市交换代币并赚取费用',
      isSelected: false,
      url: 'https://app.uniswap.org/swap?disableNFTs=true',
      icon: 'https://app.uniswap.org/favicon.ico',
    },
    {
      id: 3,
      name: 'Toshi Mart',
      desc: 'Base 上的模因币生成器和交易平台。',
      isSelected: true,
      url: 'https://toshi.fun/',
      icon: 'https://toshi.fun/favicon.ico',
    },
    {
      id: 4,
      name: 'Matcha',
      desc: '0x 开发的 DEX 聚合器',
      isSelected: true,
      url: 'https://matcha.xyz/',
      icon: 'https://matcha.xyz/favicon.ico',
    },
    {
      id: 5,
      name: 'Alien Base',
      desc: '为非传统地球人而打造的 Base 原生去中心化交易所',
      isSelected: false,
      url: 'https://app.alienbase.xyz/',
      icon: 'https://app.alienbase.xyz/favicon.ico',
    },
    {
      id: 6,
      name: 'PancakeSwap',
      desc: '交易并赚取加密货币',
      isSelected: false,
      url: 'https://pancakeswap.finance/',
      icon: 'https://pancakeswap.finance/favicon.ico',
    },
  ]

  // 赚取应用数据
  const earnAppsData = [
    {
      id: 1,
      name: 'Seamless Protocol',
      desc: '实现收益最大化',
      isSelected: true,
      url: 'https://seamlessprotocol.com/',
      icon: 'https://seamlessprotocol.com/favicon.ico',
    },
    {
      id: 2,
      name: 'Plaza Finance',
      desc: '债券和杠杆',
      isSelected: true,
      url: 'https://plaza.finance/',
      icon: 'https://plaza.finance/favicon.ico',
    },
    {
      id: 3,
      name: 'MetaLend',
      desc: '在 5 分钟内实现加密货币收益最大化',
      isSelected: true,
      url: 'https://metalend.fi/',
      icon: 'https://metalend.fi/favicon.ico',
    },
    {
      id: 4,
      name: 'ether.fi',
      desc: '通过 ether.fi 质押、消费、赚钱',
      isSelected: false,
      url: 'https://www.ether.fi/',
      icon: 'https://www.ether.fi/favicon.ico',
    },
    {
      id: 5,
      name: 'Moonwell',
      desc: '使贷款变得简单',
      isSelected: false,
      url: 'https://moonwell.fi/',
      icon: 'https://moonwell.fi/favicon.ico',
    },
  ]

  // 社交媒体应用数据
  const socialMediaAppsData = [
    {
      id: 1,
      name: 'moshi.cam',
      desc: '链上照片共享应用程序',
      isSelected: false,
      url: 'https://moshi.cam/',
      icon: 'https://moshi.cam/favicon.ico',
    },
    {
      id: 2,
      name: 'Virtuals',
      desc: '链上 AI 代理协会',
      isSelected: true,
      url: 'https://virtuals.io/',
      icon: 'https://virtuals.io/favicon.ico',
    },
    {
      id: 3,
      name: 'Hypersub',
      desc: '订阅并赚取',
      isSelected: false,
      url: 'https://hypersub.withfabric.xyz/',
      icon: 'https://hypersub.withfabric.xyz/favicon.ico',
    },
    {
      id: 4,
      name: 'Soulbound TV',
      desc: '实现电视去中心化，新一代直播平台。',
      isSelected: false,
      url: 'https://soulbound.tv/',
      icon: 'https://soulbound.tv/favicon.ico',
    },
    {
      id: 5,
      name: 'Rad TV',
      desc: '为创作者和粉丝提供链上视频流',
      isSelected: false,
      url: 'https://rad.tv/',
      icon: 'https://rad.tv/favicon.ico',
    },
    {
      id: 6,
      name: 'Aura',
      desc: '个人链上克隆体',
      isSelected: false,
      url: 'https://aura.network/',
      icon: 'https://aura.network/favicon.ico',
    },
  ]

  // 管理应用数据
  const managementAppsData = [
    {
      id: 1,
      name: 'Onboard',
      desc: '成为 Onboard 商家，每天最多可赚取 100...',
      isSelected: true,
      url: 'https://onboard.xyz/',
      icon: 'https://onboard.xyz/favicon.ico',
    },
    {
      id: 2,
      name: 'Webacy',
      desc: '管理钱包安全。',
      isSelected: false,
      url: 'https://webacy.com/',
      icon: 'https://webacy.com/favicon.ico',
    },
    {
      id: 3,
      name: 'Dune',
      desc: '在 Base 上开发应用程序',
      isSelected: false,
      url: 'https://dune.com/',
      icon: 'https://dune.com/favicon.ico',
    },
    {
      id: 4,
      name: 'Venice',
      desc: '私有且不受审查的人工智能',
      isSelected: false,
      url: 'https://venice.ai/',
      icon: 'https://venice.ai/favicon.ico',
    },
    {
      id: 5,
      name: 'Quip Network',
      desc: '保护您的资产免受量子计算机黑客的攻击',
      isSelected: false,
      url: 'https://quip.network/',
      icon: 'https://quip.network/favicon.ico',
    },
    {
      id: 6,
      name: 'Daos.world',
      desc: 'Dao 将吞噬世界',
      isSelected: false,
      url: 'https://daos.world/',
      icon: 'https://daos.world/favicon.ico',
    },
  ]

  // 监听应用数据
  const monitoringAppsData = [
    {
      id: 1,
      name: 'Pods',
      desc: '链上播客平台。发布、发现、拥有。',
      isSelected: false,
      url: 'https://pods.media/',
      icon: 'https://pods.media/favicon.ico',
    },
    {
      id: 2,
      name: 'Arpeggi',
      desc: '采样、混音、上传和签署音乐',
      isSelected: false,
      url: 'https://arpeggi.io/',
      icon: 'https://arpeggi.io/favicon.ico',
    },
    {
      id: 3,
      name: 'Spinamp',
      desc: '探索、管理、分享和聆听',
      isSelected: false,
      url: 'https://spinamp.xyz/',
      icon: 'https://spinamp.xyz/favicon.ico',
    },
    {
      id: 4,
      name: 'Spores',
      desc: '与世界共同打造热门作品',
      isSelected: false,
      url: 'https://spores.app/',
      icon: 'https://spores.app/favicon.ico',
    },
    {
      id: 5,
      name: 'Staxe',
      desc: '成为心仪艺术家的创作合作伙伴',
      isSelected: false,
      url: 'https://staxe.xyz/',
      icon: 'https://staxe.xyz/favicon.ico',
    },
    {
      id: 6,
      name: 'anotherblock',
      desc: '拥有您最喜爱歌曲的股权份额',
      isSelected: false,
      url: 'https://anotherblock.io/',
      icon: 'https://anotherblock.io/favicon.ico',
    },
  ]

  // 根据当前标签获取对应的数据
  const getCurrentTabData = () => {
    switch (currentTab) {
      case 0:
        return allAppsData
      case 1:
        return exchangeAppsData
      case 2:
        return earnAppsData
      case 3:
        return socialMediaAppsData
      case 4:
        return managementAppsData
      case 5:
        return monitoringAppsData
      default:
        return allAppsData
    }
  }

  const dataList = getCurrentTabData()

  const handleOpenUrl = (url: string) => {
    window.location.href = url
  }

  return (
    <YStack bg="$background" minHeight={'100vh'}>
      <XStack pl={16} alignItems="center" mb={32} justifyContent="space-between">
        <NavBar title="" onBack={() => router.back()} />
        <Input placeholder="搜索或输入网址" />
        <View flexDirection="row" justifyContent="space-between" ml={30}>
          <Pressable onPress={() => { }}>
            <Image source={net1Icon.src} width={16} height={16} mr={10} />
          </Pressable>
          <Pressable onPress={() => { }}>
            <Image source={net2Icon.src} width={18} height={18} mr={10} />
          </Pressable>
          <Pressable onPress={() => { }}>
            <Image source={net3Icon.src} width={16} height={16} mr={10} />
          </Pressable>
        </View>
      </XStack>

      <XStack
        pl={16}
        bg="#02A9DE"
        width={343}
        height={80}
        borderRadius={20}
        margin="auto"
        pt={6}
        alignItems="center"
      >
        <Image source={net1Icon.src} width={70} height={70} mr={10} />
        <View>
          <Text color="$black1" fontWeight="bold" fontSize={14}>
            用作资金的免费 NFT
          </Text>
          <Text color="$black1" fontSize={14} fontWeight={500} mt={4}>
            获取特别的 NFT 来为钱包注入资金。
          </Text>
        </View>
      </XStack>

      <Underline />
      <YStack px={16} py={20} rowGap={16} flex={1}>
        <Text>热门应用</Text>
        <XStack gap="$5">
          {tabList.map((i, index) => (
            <Pressable onPress={() => setCurrentTab(index)} key={i}>
              <View style={{ position: 'relative' }}>
                {currentTab === index ? (
                  <ActiveText>{i}</ActiveText>
                ) : (
                  <Text color="#fff">{i}</Text>
                )}
                {currentTab === index && <ActiveUnderline />}
              </View>
            </Pressable>
          ))}
        </XStack>
        <YStack
          py={10}
          flex={1}
          maxHeight="55vh"
          overflow="scroll"
        >
          {dataList.map((i) => (
            <XStack items="center" key={i.id} mb={16} onPress={() => handleOpenUrl(i.url)}>
              <Image
                source={{ uri: i.icon }}
                width={40}
                height={40}
                rounded={20}
                mr={10}
                bg="$white1"
              />
              <View flex={1}>
                <XStack alignItems="center">
                  <Text mr={20} flex={1} numberOfLines={1} ellipsizeMode="tail">{i.name}</Text>
                  {i.isSelected && (
                    <Text
                      bg="#141519"
                      rounded={10}
                      width={55}
                      height={22}
                      fontSize={12}
                      textAlign="center"
                      lineHeight={22}
                      flexShrink={0}
                    >
                      精选
                    </Text>
                  )}
                </XStack>
                <Text
                  color="$color10"
                  fontSize={14}
                  mt={11}
                  numberOfLines={2}
                  ellipsizeMode="tail"
                  lineHeight={20}
                >
                  {i.desc}
                </Text>
              </View>
            </XStack>
          ))}
        </YStack>
        <YStack mt={30}></YStack>
      </YStack>
      <FooterNavBar />
    </YStack>
  )
}
