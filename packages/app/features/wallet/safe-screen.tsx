import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON>v<PERSON><PERSON>,
  Paragraph,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled, Switch } from 'tamagui'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import choose1Icon from '../../../assets/images/wallet/choose1.png'
import { useTranslation } from 'app/i18n'
import ethIcon from '../../../assets/images/wallet/eth.png'
import arrowRightIcon from '../../../assets/images/wallet/arrowright.png'

const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10,
})

const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2,
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF',
})
const Underlineblock = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
})

export function SafeScreen() {
  const router = useRouter()
  const { t } = useTranslation()
  const [currentTab, setCurrentTab] = useState(0)
  const [openAppLock, setOpenAppLock] = useState(true)
  const [transactionLock, setTransactionLock] = useState(true)
  const [netList, setNetList] = useState([
    {
      id: 1,
      name: 'Ethereum',
      icon: ethIcon.src,
    },
    {
      id: 2,
      name: 'Arbitrum',
      icon: ethIcon.src,
    },
    {
      id: 3,
      name: 'Avalanche C-Chain',
      icon: ethIcon.src,
    },
    {
      id: 4,
      name: 'Base',
      icon: ethIcon.src,
    },
    {
      id: 5,
      name: 'BNB (Binance Smart) Chain',
      icon: ethIcon.src,
    },
    {
      id: 6,
      name: 'Fantom Opera',
      icon: ethIcon.src,
    },
    {
      id: 7,
      name: 'Gnosis',
      icon: ethIcon.src,
    },
    {
      id: 8,
      name: 'OP Mainnet',
      icon: ethIcon.src,
    },
    {
      id: 9,
      name: 'Ethereum',
      icon: ethIcon.src,
    },
    {
      id: 10,
      name: 'Ethereum',
      icon: ethIcon.src,
    },
  ])

  return (
    <YStack bg="$background" height={800}>
      <XStack justifyContent="space-between" items="center" pr={16}>
        <NavBar title={t('navigation.security') || '安全'} onBack={() => router.back()} />
        {/* <Image source={choose1Icon.src} width={16} height={16} /> */}
      </XStack>
      <YStack px={16} mt={30} mb={20}>
        <Text fontSize={18} fontWeight="bold">
          备份
        </Text>
        <XStack justifyContent="space-between" mt={20}>
          <Text fontSize={14} fontWeight="bold">
            钱包1
          </Text>
          <XStack items="center">
            <Text
              color="#C7545E"
              fontSize={12}
              mr={10}
              bg="rgba(199,84,94,0.16)"
              width={45}
              height={20}
              rounded={4}
              textAlign="center"
              lineHeight={20}
            >
              未备份
            </Text>
            <Image source={arrowRightIcon.src} width={6} height={9} />
          </XStack>
        </XStack>
      </YStack>
      <Line />

      <YStack px={16} mt={30} mb={20}>
        <Text fontSize={18} fontWeight="bold">
          安全锁定
        </Text>
        <XStack justifyContent="space-between" mt={20}>
          <Text fontSize={14} fontWeight="bold">
            锁定方式
          </Text>
          <XStack items="center">
            <Text fontSize={14} fontWeight={500} color="$accent11">
              密码
            </Text>
          </XStack>
        </XStack>
      </YStack>
      <Line />
      <YStack px={16} mt={30} mb={20}>
        <Text fontSize={18} fontWeight="bold">
          以下情况需要解锁
        </Text>
        <Text fontSize={14} color="$accent11" mt={10}>
          必须至少选择一个选项。
        </Text>
        <XStack justifyContent="space-between" mt={20}>
          <Text fontSize={14} fontWeight="bold">
            打开应用
          </Text>
          <Switch
            size="$3"
            backgroundColor={openAppLock ? "#3873F5" : "#3A3D44"}
            borderColor={openAppLock ? "#3873F5" : "#3A3D44"}
            checked={openAppLock}
            onCheckedChange={setOpenAppLock}
            style={{
              backgroundColor: openAppLock ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={14} fontWeight="bold">
              进行交易
            </Text>
            <Text fontSize={14} fontWeight="bold" color="$accent11" mt={2}>
              仅影响标准钱包
            </Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={transactionLock ? "#3873F5" : "#3A3D44"}
            borderColor={transactionLock ? "#3873F5" : "#3A3D44"}
            checked={transactionLock}
            onCheckedChange={setTransactionLock}
            style={{
              backgroundColor: transactionLock ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
      </YStack>

      <Line />
    </YStack>
  )
}
