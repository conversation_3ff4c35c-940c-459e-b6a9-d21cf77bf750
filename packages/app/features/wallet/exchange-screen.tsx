import { <PERSON><PERSON>, <PERSON>3, <PERSON><PERSON><PERSON><PERSON>, YStack } from '@my/ui'
import { View, Text, Image } from 'tamagui'
import excehangIcon from '../../../assets/images/wallet/exchange.png'

import { useEffect } from 'react'
import { FooterNavBar } from '../home/<USER>'
import { useTransactionStore, formatTransaction, getTypeDisplayText } from 'app/stores/transactionStore'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'
import ethIcon from '../../../assets/images/wallet/eth.png'
import { useRouter } from 'solito/navigation'

export function ExchangeScreen() {
  const { t } = useTranslation()
  const transactionStore = useTransactionStore()
  const walletStore = useWalletStore()
  const router = useRouter()

  // 初始化交易数据
  useEffect(() => {
    transactionStore.loadTransactions()
  }, [])

  // 获取当前账户的所有链的交易
  const currentAccount = walletStore.currentAccount
  const transactions = currentAccount
    ? (() => {
      // 获取所有链的地址
      const addresses = [
        currentAccount.eth?.address,
        currentAccount.bsc?.address,
        currentAccount.btc?.address,
        currentAccount.solana?.address,
      ].filter(Boolean) // 过滤掉undefined/null值

      // 使用新的去重方法获取交易记录
      return transactionStore.getTransactionsByAddresses(addresses)
    })()
    : []

  // 按日期分组交易
  const groupTransactionsByDate = (transactions: any[]) => {
    const groups: { [key: string]: any[] } = {}

    transactions.forEach((tx) => {
      const date = new Date(tx.timestamp)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)

      let dateKey = ''
      if (date.toDateString() === today.toDateString()) {
        dateKey = t('time.today') || '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        dateKey = t('time.yesterday') || '昨天'
      } else {
        dateKey = date.toLocaleDateString()
      }

      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(formatTransaction(tx))
    })

    return groups
  }

  const groupedTransactions = groupTransactionsByDate(transactions)

  // 获取链图标
  const getChainIcon = (chain: string) => {
    switch (chain) {
      case 'eth':
        return ethIcon.src
      case 'bsc':
        return ethIcon.src // 暂时使用同一个图标
      case 'btc':
        return ethIcon.src
      case 'solana':
        return ethIcon.src
      default:
        return ethIcon.src
    }
  }
  if (transactionStore.isLoading) {
    return (
      <YStack bg="$background" flex={1} justifyContent="center" alignItems="center">
        <Text color="white">加载中...</Text>
      </YStack>
    )
  }

  return (
    <YStack bg="$background" flex={1}>
      <YStack px={16} py={30} flex={1}>
        <XStack pl={16} justifyContent='space-between'>
          <H3>交易</H3>
          <Text color="#4575FF" fontSize={14} fontWeight={500} mr={16}>
            {/* {t('time.filter') || '筛选'} */}
          </Text>
        </XStack>

        {/* 交易记录列表 */}
        <YStack flex={1} mb={currentAccount ? 80 : 140}>
          {Object.keys(groupedTransactions).length === 0 ? (
            <YStack flex={1} alignItems="center" mt={100}>
              <XStack justifyContent='center' mt={0}>
                <Image source={excehangIcon.src} width={173} height={142} />
              </XStack>
              <YStack justifyContent='center' mt={20}>
                <Text color="$white1" fontSize={16} fontWeight="bold" textAlign='center' mb={10}>还没有交易</Text>
                <Text color="$accent11" width={280} textAlign='center' margin="auto">一旦您开始使用钱包，您的加密货币和 NFT 活动将显示在这里。</Text>
              </YStack>
            </YStack>
          ) : (
            Object.entries(groupedTransactions).map(([date, txs]) => (
              <YStack key={date} mt={20}>
                <Text fontSize={14} fontWeight="bold" color="$accent11" mb={10}>
                  {date}
                </Text>
                {txs.map((tx) => (
                  <XStack key={tx.id} justifyContent="space-between" mt={25} alignItems="center">
                    <XStack alignItems="center">
                      <View position="relative">
                        <Image source={getChainIcon(tx.chain)} width={38} height={38} />
                      </View>
                      <View ml={10}>
                        <Text fontSize={14} fontWeight="bold" color="white">
                          {getTypeDisplayText(tx.type)}
                        </Text>
                        <Text fontSize={12} fontWeight="bold" color="$accent11" mt={2}>
                          {tx.displayAddress}
                        </Text>
                      </View>
                    </XStack>
                    <YStack flexDirection="column" alignItems="flex-end">
                      <Text fontSize={14} fontWeight="bold" color="white">
                        {tx.displayAmount}
                      </Text>
                      <Text fontSize={12} fontWeight="bold" color="$accent11" mt={2}>
                        {tx.displayTime}
                      </Text>
                      {tx.txHash ? (
                        <Text fontSize={10} color="$accent11" mt={1}>
                          {tx.txHash.slice(0, 8)}...
                        </Text>
                      ) : null}
                    </YStack>
                  </XStack>
                ))}
              </YStack>
            ))
          )}
        </YStack>

        {/* 底部按钮 - 根据是否有钱包动态显示 */}
        {currentAccount && (
          <YStack position="absolute" bottom={90} left={16} right={16}>
            <Button rounded={30} width="100%" bg="$accent11" onPress={() => router.push('/wallet/buyCoin')}>
              <Text color="$white1">将加密货币添加到您的钱包</Text>
            </Button>
          </YStack>
        )}
      </YStack>

      <FooterNavBar />
    </YStack>
  )
}