import {
  Avatar,
  Button,
  H3,
  H5,
  <PERSON>vB<PERSON>,
  Paragraph,
  SwitchThemeButton,
  XStack,
  YStack,
  Input,
} from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled, Switch, RadioGroup } from 'tamagui'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import { useTranslation } from 'app/i18n'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import choose1Icon from '../../../assets/images/wallet/choose1.png'
import ethIcon from '../../../assets/images/wallet/eth.png'
import arrowRightIcon from '../../../assets/images/wallet/arrowright.png'

const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10,
})

const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2,
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF',
})
const Underlineblock = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
})

export function DisplayScreen() {
  const router = useRouter()
  const { t, currentLanguage } = useTranslation()

  const handleLanguagePress = () => {
    router.push('/wallet/language')
  }

  const getLanguageDisplayText = () => {
    return currentLanguage === 'zh' ? '简体中文' : 'English'
  }

  const [checked1, setChecked1] = useState(false)
  const [checked2, setChecked2] = useState(false)
  const [checked3, setChecked3] = useState(false)
  const [checked4, setChecked4] = useState(false)

  const [themeType, setThemeType] = useState('1')

  return (
    <YStack bg="$background" pb={100}>
      <XStack justifyContent="space-between" items="center" pr={16}>
        <NavBar title={t('navigation.display') || '显示'} onBack={() => router.back()} />
        {/* <Image source={choose1Icon.src} width={16} height={16} /> */}
      </XStack>

      <YStack px={16} mt={30} mb={20}>
        <Text fontSize={18} fontWeight="bold">
          外观
        </Text>
        <RadioGroup onValueChange={value => {
          setThemeType(value)
        }} value={themeType} gap="$2">
          <XStack justifyContent="space-between" mt={20}>
            <View>
              <Text fontSize={16} fontWeight="bold">
                深色
              </Text>
              <Text fontSize={16} fontWeight="bold" color="$accent11">
                始终使用深色模式
              </Text>
            </View>
            {/* <RadioGroup value="foo" gap="$2"> */}
            <RadioGroup.Item value="1" id="foo-radio-item">
              <RadioGroup.Indicator />
            </RadioGroup.Item>
            {/* </RadioGroup> */}
          </XStack>
          <XStack justifyContent="space-between" alignItems="center" mt={20}>
            <View>
              <Text fontSize={16} fontWeight="bold">
                浅色
              </Text>
              <Text fontSize={16} fontWeight="bold" color="$accent11">
                始终使用浅色模式
              </Text>
            </View>
            {/* <RadioGroup value="foo" gap="$2"> */}
            <RadioGroup.Item value="2" id="foo-radio-item">
              <RadioGroup.Indicator />
            </RadioGroup.Item>
            {/* </RadioGroup> */}
          </XStack>
          <XStack justifyContent="space-between" alignItems="center" mt={20}>
            <View>
              <Text fontSize={16} fontWeight="bold">
                自动
              </Text>
              <Text fontSize={16} fontWeight="bold" color="$accent11">
                使用设备的默认模式
              </Text>
            </View>
            {/* <RadioGroup value="foo" gap="$2"> */}
            <RadioGroup.Item value="3" id="foo-radio-item">
              <RadioGroup.Indicator />
            </RadioGroup.Item>
            {/* </RadioGroup> */}
          </XStack>
        </RadioGroup>
      </YStack>
      <Line />

      <YStack px={16} mt={30} mb={20}>
        <Text fontSize={18} fontWeight="bold">
          {t('navigation.language') || '语言'}
        </Text>
        <Pressable onPress={handleLanguagePress}>
          <XStack justifyContent="space-between" mt={20} alignItems="center">
            <Text fontSize={16} fontWeight="bold">
              {t('navigation.language') || '语言'}
            </Text>
            <XStack items="center">
              <Text fontSize={16} fontWeight={500} mr={10}>
                {getLanguageDisplayText()}
              </Text>
              <Image source={arrowRightIcon.src} width={5} height={9} />
            </XStack>
          </XStack>
        </Pressable>
      </YStack>
      <Line />
      <YStack px={16} mt={30} mb={20}>
        <Text fontSize={18} fontWeight="bold">
          余额
        </Text>
        <XStack justifyContent="space-between" mt={20}>
          <Text fontSize={16} fontWeight="bold">
            本地货币
          </Text>
          <XStack items="center">
            <Text fontSize={16} fontWeight={500} mr={10}>
              USD
            </Text>
            <Image source={arrowRightIcon.src} width={5} height={9} />
          </XStack>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={16} fontWeight="bold">
              私密模式
            </Text>
            <Text fontSize={16} fontWeight="bold" color="$accent11" mt={2}>
              隐藏所有余额
            </Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={checked1 ? "#3873F5" : "#3A3D44"}
            borderColor={checked1 ? "#3873F5" : "#3A3D44"}
            checked={checked1}
            onCheckedChange={setChecked1}
            style={{
              backgroundColor: checked1 ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={16} fontWeight="bold">
              隐藏小余额
            </Text>
            <Text fontSize={16} fontWeight="bold" color="$accent11" mt={2}>
              低于 1.00 美元的余额
            </Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={checked2 ? "#3873F5" : "#3A3D44"}
            borderColor={checked2 ? "#3873F5" : "#3A3D44"}
            checked={checked2}
            onCheckedChange={setChecked2}
            style={{
              backgroundColor: checked2 ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={16} fontWeight="bold">
              测试网
            </Text>
            <Text fontSize={16} fontWeight="bold" color="$accent11" mt={2}>
              显示测试网余额
            </Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={checked3 ? "#3873F5" : "#3A3D44"}
            borderColor={checked3 ? "#3873F5" : "#3A3D44"}
            checked={checked3}
            onCheckedChange={setChecked3}
            style={{
              backgroundColor: checked3 ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
      </YStack>
      <Line />
      <YStack px={16} mt={30} mb={20}>
        <Text fontSize={18} fontWeight="bold">
          选项卡
        </Text>
        <XStack justifyContent="space-between" mt={20}>
          <View width="80%">
            <Text fontSize={16} fontWeight="bold">
              粘性选项卡
            </Text>
            <Text fontSize={16} fontWeight="bold" color="$accent11" mt={2}>
              即使应用程序关闭后，最后选择的选项卡也始 终保持选中状态
            </Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={checked4 ? "#3873F5" : "#3A3D44"}
            borderColor={checked4 ? "#3873F5" : "#3A3D44"}
            checked={checked4}
            onCheckedChange={setChecked4}
            style={{
              backgroundColor: checked4 ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
      </YStack>
    </YStack>
  )
}
