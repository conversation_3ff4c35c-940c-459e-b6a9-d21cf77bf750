import { Avatar, Button, H3, H5, <PERSON><PERSON><PERSON><PERSON>, <PERSON>graph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled } from 'tamagui'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import choose1Icon from '../../../assets/images/wallet/choose1.png'
import ethIcon from '../../../assets/images/wallet/eth.png'
import arrowRightIcon from '../../../assets/images/wallet/arrowright.png'


const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF'
})
const Underlineblock = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
})

export function ChooseNetScreen() {
  const router = useRouter()
  const [currentTab, setCurrentTab] = useState(0)
  const [netList, setNetList] = useState([
    {
      id: 1,
      name: 'Ethereum',
      icon: ethIcon.src
    },
    {
      id: 2,
      name: 'Polygon',
      icon: ethIcon.src
    }, {
      id: 3,
      name: 'BNB Smart Chain',
      icon: ethIcon.src
    }, {
      id: 4,
      name: 'Solana',
      icon: ethIcon.src
    }
  ])

  return (
    <YStack bg="$background" height={800} >
      <XStack justifyContent='space-between' items="center" pr={16}>
        <NavBar title="网络" onBack={() => router.back()} />
        {/* <Image source={choose1Icon.src} width={16} height={16} /> */}
      </XStack >
      <YStack px={16} position='relative' height={700}>
        <XStack mt={10} gap="$5" height={20}>
          <Pressable onPress={() => setCurrentTab(0)}>
            <View style={{ position: 'relative' }} >
              {currentTab === 0 ? <ActiveText>主网</ActiveText> : <Text color="#fff">主网</Text>}
              {currentTab === 0 && <Underline />}
            </View>
          </Pressable>
          <Pressable onPress={() => setCurrentTab(1)}>
            <View style={{ position: 'relative' }} >
              {currentTab === 1 ? <ActiveText>测试网</ActiveText> : <Text color="#fff">测试网</Text>}
              {currentTab === 1 && <Underline />}
            </View>
          </Pressable>
        </XStack>
        <YStack mt={20}>
          {
            netList.map(i => <XStack key={i.id} items="center" justifyContent='space-between' alignItems='center' mt={10} mb={20}>
              <XStack items="center">
                <Image
                  source={i.icon}
                  style={{ width: 24, height: 24 }}
                />
                <Text fontSize={16} color="#fff" fontWeight="bold" ml={6}>{i.name}</Text>
              </XStack>
              {/* <Image source={arrowRightIcon.src} width={6} height={9} /> */}
            </XStack>)
          }

        </YStack>
      </YStack>
    </YStack >
  )
}