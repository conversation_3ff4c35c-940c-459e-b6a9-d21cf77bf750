import { <PERSON>tar, Button, H3, H5, Nav<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled, Switch } from 'tamagui'
import enoticeIcon from '../../../assets/images/wallet/notice.png'
import settingIcon from '../../../assets/images/setting.png'

import { Pressable } from 'react-native'
import { useState } from 'react'
import { ChevronRight } from '@tamagui/lucide-icons'

export function NoticeSettingScreen() {
  const router = useRouter()

  // 通知开关状态管理
  const [productAnnouncement, setProductAnnouncement] = useState(true)
  const [insightsAndTips, setInsightsAndTips] = useState(true)
  const [specialOffers, setSpecialOffers] = useState(true)
  const [priceAlerts, setPriceAlerts] = useState(true)
  const [btcPriceAlerts, setBtcPriceAlerts] = useState(true)
  const [ethPriceAlerts, setEthPriceAlerts] = useState(true)
  const [nftOfferAlerts, setNftOfferAlerts] = useState(true)
  const [accountActivity, setAccountActivity] = useState(true)

  return (
    <YStack bg="$background">
      <XStack justifyContent='space-between' items="center">
        <NavBar title="通知" onBack={() => router.back()} />
      </XStack>
      <YStack px={16} mt={10} mb={20}>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={15} fontWeight="bold">产品公告</Text>
            <Text fontSize={15} fontWeight="bold" color="$accent11" mt={2}>率先了解最新功能</Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={productAnnouncement ? "#3873F5" : "#3A3D44"}
            borderColor={productAnnouncement ? "#3873F5" : "#3A3D44"}
            checked={productAnnouncement}
            onCheckedChange={setProductAnnouncement}
            style={{
              backgroundColor: productAnnouncement ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={15} fontWeight="bold">见解和技巧</Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={insightsAndTips ? "#3873F5" : "#3A3D44"}
            borderColor={insightsAndTips ? "#3873F5" : "#3A3D44"}
            checked={insightsAndTips}
            onCheckedChange={setInsightsAndTips}
            style={{
              backgroundColor: insightsAndTips ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={15} fontWeight="bold">特别优惠</Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={specialOffers ? "#3873F5" : "#3A3D44"}
            borderColor={specialOffers ? "#3873F5" : "#3A3D44"}
            checked={specialOffers}
            onCheckedChange={setSpecialOffers}
            style={{
              backgroundColor: specialOffers ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={15} fontWeight="bold">价格提醒</Text>
            <Text fontSize={15} fontWeight="bold" color="$accent11" mt={2}>资产价格发生大幅变化时收到自动通知</Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={priceAlerts ? "#3873F5" : "#3A3D44"}
            borderColor={priceAlerts ? "#3873F5" : "#3A3D44"}
            checked={priceAlerts}
            onCheckedChange={setPriceAlerts}
            style={{
              backgroundColor: priceAlerts ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={15} fontWeight="bold">BTC 价格提醒</Text>
            <Text fontSize={15} fontWeight="bold" color="$accent11" mt={2}>BTC 价格发生大幅变化时收到自动通知</Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={btcPriceAlerts ? "#3873F5" : "#3A3D44"}
            borderColor={btcPriceAlerts ? "#3873F5" : "#3A3D44"}
            checked={btcPriceAlerts}
            onCheckedChange={setBtcPriceAlerts}
            style={{
              backgroundColor: btcPriceAlerts ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={15} fontWeight="bold">ETH 价格提醒</Text>
            <Text fontSize={15} fontWeight="bold" color="$accent11" mt={2}>ETH 价格发生大幅变化时收到自动通知</Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={ethPriceAlerts ? "#3873F5" : "#3A3D44"}
            borderColor={ethPriceAlerts ? "#3873F5" : "#3A3D44"}
            checked={ethPriceAlerts}
            onCheckedChange={setEthPriceAlerts}
            style={{
              backgroundColor: ethPriceAlerts ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View width="80%">
            <Text fontSize={15} fontWeight="bold">NFT 报价提醒</Text>
            <Text fontSize={15} fontWeight="bold" color="$accent11" mt={2}>当有人对您的 NFT 出价时收到通知。您不会
              收到极低报价的通知。</Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={nftOfferAlerts ? "#3873F5" : "#3A3D44"}
            borderColor={nftOfferAlerts ? "#3873F5" : "#3A3D44"}
            checked={nftOfferAlerts}
            onCheckedChange={setNftOfferAlerts}
            style={{
              backgroundColor: nftOfferAlerts ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
        <XStack justifyContent="space-between" mt={20}>
          <View>
            <Text fontSize={15} fontWeight="bold">账户活动</Text>
          </View>
          <Switch
            size="$3"
            backgroundColor={accountActivity ? "#3873F5" : "#3A3D44"}
            borderColor={accountActivity ? "#3873F5" : "#3A3D44"}
            checked={accountActivity}
            onCheckedChange={setAccountActivity}
            style={{
              backgroundColor: accountActivity ? '#3873F5' : '#3A3D44',
            }}
          >
            <Switch.Thumb animation="bouncy" backgroundColor="white" />
          </Switch>
        </XStack>
      </YStack>
    </YStack >

  )
}