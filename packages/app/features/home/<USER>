import { useState } from 'react'
import { XStack, YStack } from '@my/ui'
import { Image, Text, styled, View, ScrollView } from 'tamagui'
import closeIcon from '../../../assets/images/close.png'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import mint1Img from '../../../assets/images/mint1.png'
import mint2Img from '../../../assets/images/mint2.png'
import { useRouter } from 'solito/navigation'
import { useTranslation } from 'app/i18n'
import { Linking } from 'react-native'

const Underline = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10,
})

const ActiveBlock = styled(View, {
  width: 22,
  height: 2,
  background: '#fff',
  borderRadius: 10,
})

const Block = styled(View, {
  width: 22,
  height: 2,
  background: '#262729',
  borderRadius: 10,
})

const SwiperContainer = styled(View, {
  position: 'relative',
  overflow: 'hidden',
  width: '100%',
})

const SwiperWrapper = styled(View, {
  display: 'flex',
  flexDirection: 'row',
  transition: 'transform 0.3s ease',
  width: '400%', // 4页内容，每页100%
})

const SwiperSlide = styled(View, {
  width: '25%', // 每个slide占25%（因为总宽度是400%）
  flexShrink: 0,
})

export function HomePage({ pagesMode = false }: { pagesMode?: boolean }) {
  const [currentPage, setCurrentPage] = useState(0)
  const totalPages = 4
  const [touchStart, setTouchStart] = useState(0)
  const [touchEnd, setTouchEnd] = useState(0)
  const router = useRouter()
  const { t } = useTranslation()

  // 处理触摸开始
  const handleTouchStart = (e: any) => {
    setTouchEnd(0) // 重置touchEnd
    setTouchStart(e.targetTouches[0].clientX)
  }

  // 处理触摸移动
  const handleTouchMove = (e: any) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  // 处理触摸结束
  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe && currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1)
    }
    if (isRightSwipe && currentPage > 0) {
      setCurrentPage(currentPage - 1)
    }
  }

  // 轮播内容数据
  const swiperData = [
    {
      id: 1,
      title: t('home.earnRewards').replace('{rate}', '4.1') || '获得 4.1% 的奖励',
      desc:
        t('home.addToWallet').replace('{network}', 'Base').replace('{token}', 'USDC') +
        '，' +
        t('home.yearlyEarnings').replace('{rate}', '4.1') ||
        '将 Base 上的 USDC 添加到您的钱包，每年可赚取 4.1% 的奖励',
    },
    {
      id: 2,
      title: t('home.earnRewards').replace('{rate}', '3.8') || '获得 3.8% 的奖励',
      desc:
        t('home.addToWallet').replace('{network}', 'Ethereum').replace('{token}', 'USDT') +
        '，' +
        t('home.yearlyEarnings').replace('{rate}', '3.8') ||
        '将 Ethereum 上的 USDT 添加到您的钱包，每年可赚取 3.8% 的奖励',
    },
    {
      id: 3,
      title: t('home.earnRewards').replace('{rate}', '5.2') || '获得 5.2% 的奖励',
      desc:
        t('home.addToWallet').replace('{network}', 'Polygon').replace('{token}', 'USDC') +
        '，' +
        t('home.yearlyEarnings').replace('{rate}', '5.2') ||
        '将 Polygon 上的 USDC 添加到您的钱包，每年可赚取 5.2% 的奖励',
    },
    {
      id: 4,
      title: t('home.earnRewards').replace('{rate}', '4.5') || '获得 4.5% 的奖励',
      desc:
        t('home.addToWallet').replace('{network}', 'Arbitrum').replace('{token}', 'USDC') +
        '，' +
        t('home.yearlyEarnings').replace('{rate}', '4.5') ||
        '将 Arbitrum 上的 USDC 添加到您的钱包，每年可赚取 4.5% 的奖励',
    },
  ]

  // Trending swaps 测试数据
  const trendingSwapsData = [
    {
      id: 1,
      name: 'KTA',
      price: 'US$0.54',
      change: '15.51%',
      changeColor: '#C7545E',
      swaps: 681,
      buyPercentage: 70,
      sellPercentage: 30,
    },
    {
      id: 2,
      name: 'DEGEN',
      price: 'US$0.012',
      change: '+8.23%',
      changeColor: '#2FAB77',
      swaps: 47,
      buyPercentage: 65,
      sellPercentage: 35,
    },
    {
      id: 3,
      name: 'HIGHER',
      price: 'US$0.089',
      change: '-3.45%',
      changeColor: '#C7545E',
      swaps: 82,
      buyPercentage: 45,
      sellPercentage: 55,
    },
    {
      id: 4,
      name: 'BALD',
      price: 'US$0.0034',
      change: '+12.67%',
      changeColor: '#2FAB77',
      swaps: 156,
      buyPercentage: 78,
      sellPercentage: 22,
    },
  ]

  const handleAction = (action: string) => {
    if (action === 'watchlist') {
      router.push('/user/myAttention')
    }
  }

  // 处理外部链接点击
  const handleExternalLink = (url: string) => {
    window.location.href = url
    // Linking.openURL(url).catch(err => console.error('Failed to open URL:', err))
  }

  return (
    <YStack>
      <Underline />
      <YStack>
        <XStack mt={20} flex={1} alignContent="center" justifyContent="space-between">
          <XStack gap="$2">
            {[0, 1, 2, 3].map((index) =>
              index <= currentPage ? <ActiveBlock key={index} /> : <Block key={index} />
            )}
          </XStack>
          <Image source={closeIcon.src} style={{ width: 12, height: 12 }} />
        </XStack>
        <SwiperContainer
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <SwiperWrapper
            style={{
              transform: `translateX(-${currentPage * 25}%)`,
            }}
          >
            {swiperData.map((item, index) => (
              <SwiperSlide key={item.id}>
                <XStack
                  mt={10}
                  flex={1}
                  gap="$4"
                  alignContent="center"
                  justifyContent="space-between"
                  mb={10}
                >
                  <Image source={closeIcon.src} style={{ width: 50, height: 50 }} />
                  <YStack flex={1} flexWrap="wrap">
                    <Text color="#fff" fontSize={14} fontWeight="bold">
                      {item.title}
                    </Text>
                    <Text color="#8B8F9A" fontSize={12}>
                      {item.desc}
                    </Text>
                  </YStack>
                </XStack>
              </SwiperSlide>
            ))}
          </SwiperWrapper>
        </SwiperContainer>
      </YStack>
      <Underline />
      <YStack mt={20} mb={20} onPress={() => handleAction('watchlist')}>
        <Text color="white" fontSize={16} fontWeight="bold">
          Watchlist
        </Text>
        <XStack
          bg="#141519"
          borderRadius={10}
          p={10}
          mt={10}
          height={70}
          flex={1}
          alignItems="center"
          justifyContent="space-between"
        >
          <YStack>
            <Text color="white" fontSize={14} fontWeight="bold">
              创建“我的关注”
            </Text>
            <Text color="#8B8F9A" fontSize={12} fontWeight={500} mt={6}>
              获取价格提醒并了解最新信息
            </Text>
          </YStack>
          <Image source={mainConnetIcon.src} style={{ width: 70, height: 37 }} />
        </XStack>
      </YStack>
      <Underline />
      <YStack mt={20} mb={20}>
        <Text fontSize={16} color="white" fontWeight={'bold'}>
          Trending swaps on Base
        </Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} mt={10}>
          <XStack gap="$3">
            {trendingSwapsData.map((item) => (
              <View key={item.id} width={260} height={180} borderRadius={10} bg="#141519" px={14} py={14}>
                <YStack>
                  <XStack mb="$4">
                    <Image
                      source={{ uri: '' }}
                      style={{ width: 28, height: 28, borderRadius: '50%', background: '#2B2B2B' }}
                    />
                    <View ml="$2">
                      <Text color="white" fontSize={14} fontWeight="bold">
                        {item.name}
                      </Text>
                      <Text color="#8B8F9A" fontSize={12} fontWeight={500} mt={6}>
                        {item.price}
                      </Text>
                    </View>
                    <Text style={{ color: item.changeColor }} fontSize={14} fontWeight="bold" ml="$4">
                      {item.change}
                    </Text>
                  </XStack>
                  <XStack alignItems="center">
                    <Text color="white" fontSize={20} fontWeight="bold">
                      {item.swaps}
                    </Text>
                    <Text color="white" fontSize={16} fontWeight="bold" ml="$1">
                      兑换
                    </Text>
                    <View
                      bg="#282B32"
                      width={137}
                      height={34}
                      borderRadius={20}
                      ml="$5"
                      onPress={() => { router.push('/wallet/convert') }}
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Text color="white" text="center" lineHeight={34}>
                        兑换
                      </Text>
                    </View>
                  </XStack>
                  <XStack mt={20}>
                    <View
                      width={Math.round(224 * item.buyPercentage / 100)}
                      height={4}
                      bg="#2FAB77"
                      borderRadius={20}
                    />
                    <View
                      width={Math.round(224 * item.sellPercentage / 100)}
                      height={4}
                      bg="#C7545E"
                      borderRadius={20}
                    />
                  </XStack>
                  <XStack mt={20}>
                    <XStack flex={1} alignItems="center">
                      <View width={6} height={6} bg="#2FAB77" borderRadius={3} />
                      <Text fontSize={12} color="#8B8F9A" ml="$2">
                        已购买 {item.buyPercentage}%
                      </Text>
                    </XStack>
                    <XStack flex={1} alignItems="center">
                      <View width={6} height={6} bg="#C7545E" borderRadius={3} />
                      <Text fontSize={12} color="#8B8F9A" ml="$2">
                        已售出 {item.sellPercentage}%
                      </Text>
                    </XStack>
                  </XStack>
                </YStack>
              </View>
            ))}
          </XStack>
        </ScrollView>
      </YStack>
      <Underline />
      <YStack mt={20}>
        <View onPress={() => handleExternalLink('https://bridge.base.org')}>
          <Image source={mint1Img.src} style={{ width: '100%', height: 228, borderRadius: 12 }} />
          <Text color="white" fontSize={12} fontWeight="bold" mt={6}>
            Sponsored
          </Text>
          <Text fontSize={16} color="white" fontWeight="bold" mt={6}>
            In-App Bridging is Here
          </Text>
          <Text color="#8B8F9A" fontSize={12} fontWeight={500} mt={6}>
            For when you really, really want that one token. Onthat other chain.
          </Text>
          <XStack mt="$4" alignItems="center" gap="$5">
            <View
              bg="#282B32"
              width={137}
              height={34}
              borderRadius={20}
              alignItems="center"
              onPress={() => handleExternalLink('https://bridge.base.org')}
            >
              <Text color="white" fontSize={14} fontWeight="bold" lineHeight={34}>
                Learn More
              </Text>
            </View>
            <Text color="white" fontSize={12} fontWeight="bold">
              Dismiss
            </Text>
          </XStack>
        </View>
      </YStack>
      <Underline />
      <YStack mt={20}>
        <View>
          <Text color="white" fontSize={16} fontWeight="bold" mb={10}>
            Trending onchain
          </Text>
          <View onPress={() => handleExternalLink('https://opensea.io/collection/drifters-nft')}>
            <Image source={mint2Img.src} style={{ width: '100%', height: 228, borderRadius: 12 }} />
            <Text fontSize={16} color="white" fontWeight="bold" mt={6}>
              Drifters
            </Text>
            <Text color="#8B8F9A" fontSize={12} fontWeight={500} mt={6}>
              Drifters are handcrafted, fully customiz..
            </Text>
          </View>
        </View>
      </YStack>
      <Underline />
      {/* <YStack mt={20}>
        <Text color="white" fontSize={16}>
          NFT mints for you
        </Text>
      </YStack> */}
    </YStack>
  )
}
