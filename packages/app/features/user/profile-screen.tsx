import { Avatar, Button, H3, H5, <PERSON>v<PERSON><PERSON>, Paragraph, SwitchThemeButton, XStack, YStack, Input } from '@my/ui'
import { useRouter, useSearchParams } from 'solito/navigation'
import { View, Text, Image, H1, H2, styled, Switch } from 'tamagui'
import user1Icon from '../../../assets/images/user/user1.png'

import arrowRightIcon from '../../../assets/images/wallet/arrowright.png'

import { Pressable } from 'react-native'
import { useState, useEffect } from 'react'
// import { Edit } from '@tamagui/lucide-icons'
import { useWalletStore } from 'app/stores/walletStore'
import { useTranslation } from 'app/i18n'


const Line = styled(View, {
  width: '100%',
  height: 1,
  backgroundColor: '#212224',
  mt: 10
})

export function ProfileScreen() {
  const router = useRouter()
  const params = useSearchParams()
  const walletStore = useWalletStore()
  const { t } = useTranslation()

  // 从路由参数获取钱包信息
  const walletId = params?.get('walletId')
  const accountId = params?.get('accountId')
  const accountName = params?.get('accountName')
  const [checked, setChecked4] = useState(false)

  console.log('Profile params:', { walletId, accountId, accountName })

  // 获取当前钱包账户信息
  const getCurrentWallet = () => {
    try {
      if (!walletId || !accountId) {
        console.log('No wallet params, using current account')
        return walletStore.currentAccount
      }

      const wallet = walletStore.walletList.find((w: any) => w.walletId === walletId)
      if (wallet) {
        const account = wallet.accounts.find((a: any) => a.accountId === accountId)
        console.log('Found account:', account)
        return account || walletStore.currentAccount
      }

      console.log('Wallet not found, using current account')
      return walletStore.currentAccount
    } catch (error) {
      console.error('Error getting current wallet:', error)
      return walletStore.currentAccount
    }
  }

  const currentWallet = getCurrentWallet()
  const displayName = accountName || currentWallet?.name || '地址1'

  console.log('Current wallet:', currentWallet)
  console.log('Display name:', displayName)

  // 如果没有钱包数据，显示加载状态
  if (!currentWallet) {
    return (
      <YStack bg="$background" height={800} justifyContent="center" alignItems="center">
        <Text color="white">加载中...</Text>
      </YStack>
    )
  }

  return (
    <YStack bg="$background" height={800} >
      <XStack justifyContent='space-between' alignItems="center" pr={16}>
        <NavBar title="" onBack={() => router.back()} />
      </XStack >
      <YStack px={16} mt={20}>
        <XStack alignItems="center" gap="$2" position='relative'>
          <Avatar circular size="$4">
            <Avatar.Image
              src={`https://api.dicebear.com/7.x/identicon/svg?seed=${currentWallet?.accountId || 'default'}`}
              accessibilityLabel={currentWallet?.accountId || 'wallet'}
            />
            <Avatar.Fallback backgroundColor="$blue10" />
          </Avatar>
          <Pressable
            onPress={() => {
              const params = new URLSearchParams({
                walletId: walletId || currentWallet?.walletId || '',
                accountId: accountId || currentWallet?.accountId || '',
                currentName: displayName,
              })
              console.log('Edit wallet name params:', params.toString())
              router.push(`/user/edit-wallet-name?${params.toString()}`)
            }}
            style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}
          >
            <Text fontSize={16} ml={10} fontWeight="600" color="white">
              {displayName}
            </Text>
            <Text fontSize={12} color="$color11" style={{ marginLeft: 8 }}>✏️</Text>
          </Pressable>
          <View position='absolute' left={30} top={26} zIndex={99}>
            <Image source={user1Icon.src} width={16} height={16} />
          </View>
        </XStack>
        <Button my={20} rounded={30} onPress={() => { router.push('/user/settingAccount') }} >
          <Text fontSize={16} fontWeight="bold">设置个人资料</Text>
        </Button>
      </YStack>
      <Line />
      <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
        <Text fontSize={16} fontWeight="bold">查看个人资料</Text>
        <Pressable onPress={() => { console.log(11) }}>
          <Image source={arrowRightIcon.src} width={5} height={9} />
        </Pressable>
      </XStack>
      <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
        <Text fontSize={16} fontWeight="bold">恢复短语</Text>
        <Pressable onPress={() => { console.log(11) }} style={{ alignItems: 'center', flexDirection: 'row' }}>
          <Text color="#C7545E" fontSize={14} mr={10} bg="rgba(199,84,94,0.16)" width={45} height={20} rounded={4} textAlign='center' lineHeight={20}>未备份</Text>
          <Image source={arrowRightIcon.src} width={5} height={9} />
        </Pressable>
      </XStack>
      <Pressable onPress={() => { router.push('/wallet/connectDapp') }}>
        <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
          <Text fontSize={16} fontWeight="bold">连接的 dapp</Text>
          <View style={{ alignItems: 'center', flexDirection: 'row' }}>
            <Text color="$accent11" fontSize={16} mr={10}>0</Text>
            <Image source={arrowRightIcon.src} width={5} height={9} />
          </View>
        </XStack>
      </Pressable>
      <Pressable onPress={() => { router.push('/wallet/exportPublicAddress') }}>
        <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
          <Text fontSize={16} fontWeight="bold">导出公共地址</Text>
          <View>
            <Image source={arrowRightIcon.src} width={5} height={9} />
          </View>
        </XStack>
      </Pressable>
      <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
        <Text fontSize={16} fontWeight="bold">显示私钥</Text>
        <Pressable onPress={() => { console.log(11) }}>
          <Image source={arrowRightIcon.src} width={5} height={9} />
        </Pressable>
      </XStack>
      <Pressable onPress={() => { router.push('/wallet/hideAssets') }}>
        <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
          <Text fontSize={16} fontWeight="bold">隐藏资产</Text>
          <View>
            <Image source={arrowRightIcon.src} width={5} height={9} />
          </View>
        </XStack>
      </Pressable>
      <Pressable onPress={() => { router.push('/wallet/tokenAuth') }}>
        <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
          <Text fontSize={16} fontWeight="bold">代币授权</Text>
          <View>
            <Image source={arrowRightIcon.src} width={5} height={9} />
          </View>
        </XStack>
      </Pressable>
      <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
        <Text fontSize={16} fontWeight="bold">颜色</Text>
        <Pressable onPress={() => { console.log(11) }}>
          <Image source={arrowRightIcon.src} width={5} height={9} />
        </Pressable>
      </XStack>
      <XStack px={16} mt={30} justifyContent='space-between' alignItems="center">
        <Text fontSize={16} fontWeight="bold">隐藏地址</Text>
        <View>
          <Switch size="$3"
            backgroundColor={checked ? "#3873F5" : "#3A3D44"}
            borderColor={checked ? "#3873F5" : "#3A3D44"}
            checked={checked}
            onCheckedChange={setChecked4}
            style={{
              backgroundColor: checked ? '#3873F5' : '#3A3D44',
            }}>
            <Switch.Thumb animation="bouncy" />
          </Switch>
        </View>
      </XStack>

    </YStack >
  )
}