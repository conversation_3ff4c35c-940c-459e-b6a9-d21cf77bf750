import {
  But<PERSON>,
  useToastController,
  XStack,
  YStack,
  H2,
  WalletSheet,
  Select,
} from '@my/ui'
import { Avatar, Image, Text, styled } from 'tamagui'
import { ChevronDown, ChevronRight } from '@tamagui/lucide-icons'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { View, Pressable } from 'react-native'
import { useRouter } from 'solito/navigation'

import searchIcon from '../../../assets/images/search.png'
import copyIcon from '../../../assets/images/copy.png'
import settingIcon from '../../../assets/images/setting.png'
import mainConnetIcon from '../../../assets/images/main-connect.png'
import ethIcon from '../../../assets/images/wallet/eth.png'
import bitcoinIcon from '../../../assets/images/wallet/bitcoin.png'
import bnb_smartIcon from '../../../assets/images/wallet/bnb_smart.png'
import solanaIcon from '../../../assets/images/wallet/solana.png'

import { useWalletStore } from 'app/stores/walletStore'
import { useI18nStore, useTranslation } from 'app/i18n'
import { FooterNavBar } from '../home/<USER>'
import storage from 'app/utils/storage'
import { TIXIAN_URL } from 'app/utils/constants'

const ActiveText = styled(Text, {
  color: '#4575FF',
  marginBottom: 2,
})

const Underline = styled(View, {
  position: 'absolute',
  bottom: -2,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: '#4575FF',
})

export function UserScreen() {
  const toast = useToastController()
  const router = useRouter()
  const { t } = useTranslation()

  const walletStore = useWalletStore()

  const [currentTab, setCurrentTab] = useState(0)

  const [isOpen, setIsOpen] = useState(false)

  const [selectedWalletId, setSelectedWalletId] = useState(0)
  const [currentAccount, setCurrentAccount] = useState<any>({})

  // NFT相关状态
  const [selectedNetwork, setSelectedNetwork] = useState('all')

  // 网络选项
  const networkOptions = [
    { value: 'all', label: 'All networks' },
    { value: 'ethereum', label: 'Ethereum' },
    { value: 'polygon', label: 'Polygon' },
    { value: 'bsc', label: 'BNB Smart Chain' },
    { value: 'solana', label: 'Solana' },
  ]

  useEffect(() => {
    walletStore.init()
  }, [])
  useEffect(() => {
    if (walletStore.currentAccount && walletStore.currentAccount.accountId) {
      // 从 store 中获取当前账户，并设置显示名称
      const account = walletStore.currentAccount

      // 如果账户有自定义名称，使用自定义名称；否则使用默认格式
      let accountName = account.name
      if (!accountName) {
        // 查找账户在钱包列表中的索引来生成默认名称
        let accountIndex = 1
        for (const wallet of walletStore.walletList) {
          const foundIndex = wallet.accounts.findIndex((acc: any) => acc.accountId === account.accountId)
          if (foundIndex !== -1) {
            accountIndex = foundIndex + 1
            break
          }
        }
        accountName = (t('home.addressLabel') || '地址{number}').replace('{number}', String(accountIndex))
      }

      setCurrentAccount({
        ...account,
        accountName,
      })
      setSelectedWalletId(account.accountId as any)
    }
  }, [walletStore.currentAccount, walletStore.walletList, t, useI18nStore.getState().translations])



  const handleAction = useCallback(
    async (action: string, data = {}) => {
      if (action === 'search') {
        router.push('/wallet/search')
      }
      if (action === 'copy') {
        try {
          await navigator.clipboard.writeText(currentAccount.eth.address)
          toast.show(t('success.addressCopied') || '地址已复制到剪贴板', { duration: 2000 })
        } catch (err) {
          toast.show(t('home.copyFailed') || '复制失败，请手动复制', { duration: 2000 })
        }
      }
      if (action === 'setting') {
        router.push('/wallet/setting')
      }
      if (action === 'exchange') {
        router.push('/wallet/convert')
      }
      if (action === 'buy') {
        router.push('/wallet/buy')
      }
      if (action === 'buyRise') {
        storage.setItem('buyRiseAccount', JSON.stringify(data))
        router.push('/wallet/buyRise')
      }
      if (action === 'tixian') {
        window.open(TIXIAN_URL, '_blank')
      }
      if (action === 'learnDefi') {
        // window.open('https://www.coinbase.com/zh-sg/learn/wallet/how-to-get-started-in-defi', '_blank')
        window.location.href = 'https://www.coinbase.com/zh-sg/learn/wallet/how-to-get-started-in-defi'
      }
    },
    [currentAccount]
  )
  // 获取当前地址有余额的链

  const balanceFilterList = useMemo(() => {
    const keys = Object.keys(walletStore.currentAccount)
    let _accountList: any[] = []
    keys.forEach(key => {
      const _item = walletStore.currentAccount[key]
      if (Number(_item?.balance) > 0) {
        const _account = walletStore.currentAccount[key]
        if (_account.accountType === 'btc') {
          _account.name = 'Bitcoin'
          _account.logo = bitcoinIcon
        }
        if (_account.accountType === 'eth') {
          _account.name = 'Ethereum'
          _account.logo = ethIcon
        }
        if (_account.accountType === 'bsc') {
          _account.name = 'BNB Smart'
          _account.logo = bnb_smartIcon
        }
        if (_account.accountType === 'solana') {
          _account.name = 'Solana'
          _account.logo = solanaIcon
        }
        _accountList.push(_account)
      }
    })
    return _accountList
  }, [walletStore.currentAccount])

  console.log(balanceFilterList)

  return (
    <YStack height="100vh" bg="#0A0B0D" width={'100%'} maxW={640} margin="auto" overflow="hidden">
      <YStack flex={1} gap="$3" p="$4" overflow="scroll" pb={100}>
        <XStack alignItems="center" space="$2" justifyContent="space-between">
          <XStack
            alignItems="center"
            space="$2"
            onPress={() => {
              setIsOpen(true)
            }}
          >
            <Avatar circular size={24} mr={6}>
              <Avatar.Image
                src={`https://api.dicebear.com/7.x/identicon/svg?seed=${currentAccount.accountId}`}
                accessibilityLabel={currentAccount.accountId}

              />
              <Avatar.Fallback backgroundColor="$blue10" />
            </Avatar>
            <Text color="#8B8F9A" fontSize={14}>
              {currentAccount.accountName}
            </Text>
            <ChevronDown color="#8B8F9A" />
          </XStack>
          <XStack alignItems="center" gap="$3">
            <YStack onPress={() => handleAction('search')}>
              <Image source={searchIcon.src} style={{ width: 20, height: 20 }} />
            </YStack>
            <YStack onPress={() => handleAction('copy')}>
              <Image source={copyIcon.src} style={{ width: 20, height: 20, marginHorizontal: 8 }} />
            </YStack>
            <YStack onPress={() => handleAction('setting')}>
              <Image source={settingIcon.src} width={18} style={{ width: 18, height: 18 }} />
            </YStack>
          </XStack>
        </XStack>
        <XStack gap="$2" alignItems="center" justifyContent="space-between">
          <H2 textAlign="left" color="#fff">
            $ {walletStore.getCurrentAccountBalance().toFixed(4)}
          </H2>
          {/* <Pressable
            onPress={handleRefreshBalance}
            disabled={isRefreshing}
            style={{
              backgroundColor: '#282B32',
              paddingHorizontal: 12,
              paddingVertical: 6,
              borderRadius: 16,
              flexDirection: 'row',
              alignItems: 'center',
              opacity: isRefreshing ? 0.6 : 1
            }}
          >
            {isRefreshing ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <Text color="#fff" fontSize={12}>
                刷新余额
              </Text>
            )}
          </Pressable> */}
        </XStack>
        <XStack alignItems="center" gap="$2" justifyContent="space-between">
          <Button
            size="$6"
            width={109}
            height={36}
            fontSize={14}
            fontWeight={500}
            style={{
              color: '#333',
              background: 'linear-gradient( 90deg, #2576FE 0%, #46DFE7 100%)',
            }}
            onPress={() => handleAction('buy')}
          >
            买入
          </Button>
          <Button
            size="$6"
            width={109}
            height={36}
            fontSize={14}
            fontWeight={500}
            style={{ color: '#fff', background: '#282B32' }}
            onPress={() => handleAction('exchange')}
          >
            兑换
          </Button>
          <Button
            size="$6"
            width={109}
            height={36}
            fontSize={14}
            fontWeight={500}
            style={{ color: '#8B8F9A', background: '#15161A' }}
            onPress={() => handleAction('tixian')}
          >
            提现
          </Button>
        </XStack>
        <XStack height={1} bg="#212224"></XStack>
        <XStack gap="$5" height={26}>
          <Pressable onPress={() => setCurrentTab(0)}>
            <View style={{ position: 'relative' }}>
              {currentTab === 0 ? (
                <ActiveText>加密货币</ActiveText>
              ) : (
                <Text color="#fff">加密货币</Text>
              )}
              {currentTab === 0 && <Underline />}
            </View>
          </Pressable>
          <Pressable onPress={() => setCurrentTab(1)}>
            <View style={{ position: 'relative' }}>
              {currentTab === 1 ? <ActiveText>NFT</ActiveText> : <Text color="#fff">NFT</Text>}
              {currentTab === 1 && <Underline />}
            </View>
          </Pressable>
          <Pressable onPress={() => setCurrentTab(2)}>
            <View style={{ position: 'relative' }}>
              {currentTab === 2 ? <ActiveText>DeFi</ActiveText> : <Text color="#fff">DeFi</Text>}
              {currentTab === 2 && <Underline />}
            </View>
          </Pressable>
        </XStack>
        {/* 加密货币标签页 */}
        {currentTab === 0 && (
          <>
            {balanceFilterList.length > 0 ? (
              balanceFilterList.map(item => {
                return (
                  <XStack mt={30} items="center" justifyContent="space-between">
                    <XStack items="center">
                      <Image source={item.logo.src} width={32} height={32} mr={6} />
                      <Text color="white" fontSize={14} fontWeight="bold">
                        {item.name}
                      </Text>
                    </XStack>
                    <XStack justifyContent="flex-end" onPress={() => handleAction('buyRise', item)}>
                      <View>
                        <Text fontSize={12} fontWeight="bold">
                          {item.balance ? parseFloat(item.balance).toFixed(4) : '0.0000'}
                        </Text>
                        <Text fontSize={12} color="#8B8F9A">
                          可用
                        </Text>
                      </View>
                      <ChevronRight size={20} color="$white6" />
                    </XStack>
                  </XStack>
                )
              })
            ) : (
              <YStack
                cursor="pointer"
                margin="auto"
                width={300}
                flex={1}
                alignContent="center"
                alignItems="center"
                mt="$15"
              >
                <Image source={mainConnetIcon.src} style={{ width: 174, height: 91 }} />
                <Text fontSize={16} color="#fff" fontWeight="bold" mt="$4" text="center">
                  添加加密货币以开始使用
                </Text>
                <Text fontSize={14} color="#8B8F9A" mt="$2" text="center">
                  您可以使用您的 Coinbase 账户或其他钱包添加资产。
                </Text>
              </YStack>
            )}
          </>
        )}

        {/* NFT标签页 */}
        {currentTab === 1 && (
          <YStack mt={20}>
            <Select value={selectedNetwork} onValueChange={setSelectedNetwork}>
              <Select.Trigger
                width={120}
                height={36}
                backgroundColor="#282B32"
                borderColor="#3A3D44"
                borderWidth={2}
                borderRadius={12}
                paddingHorizontal={12}
              >
                <Select.Value placeholder="选择网络" color="white" fontSize={14} />
                <Select.Icon>
                  <ChevronDown size={16} color="white" />
                </Select.Icon>
              </Select.Trigger>
              <Select.Content>
                <Select.ScrollUpButton />
                <Select.Viewport>
                  {networkOptions.map((option, index) => (
                    <Select.Item key={option.value} index={index} value={option.value}>
                      <Select.ItemText color="white">{option.label}</Select.ItemText>
                    </Select.Item>
                  ))}
                </Select.Viewport>
                <Select.ScrollDownButton />
              </Select.Content>
            </Select>
          </YStack>
        )}

        {/* DeFi标签页 */}
        {currentTab === 2 && (
          <YStack
            cursor="pointer"
            margin="auto"
            width={300}
            flex={1}
            alignContent="center"
            alignItems="center"
            mt="$15"
          >
            <Image source={mainConnetIcon.src} style={{ width: 174, height: 91 }} />
            <Text fontSize={16} color="#fff" fontWeight="bold" mt="$4" text="center">
              开始通过 DeFi 赚取收益
            </Text>
            <Text fontSize={14} color="#8B8F9A" mt="$2" text="center">
              了解如何使用去中心化应用赚取加密货币。
            </Text>
            <Button
              mt={100}
              width="100%"
              backgroundColor="#282B32"
              borderRadius={30}
              onPress={() => handleAction('learnDefi')}
            >
              <Text color="white" fontSize={16} fontWeight="bold">
                了解 DeFi
              </Text>
            </Button>
          </YStack>
        )}
      </YStack>
      <FooterNavBar />
      <WalletSheet
        open={isOpen}
        onOpenChange={setIsOpen}
        wallets={walletStore.walletList}
        selectedId={selectedWalletId}
        onSelect={(wallet, index) => {
          if (wallet === 'addWallet') {
            router.push('/wallet/manager')
          } else {
            // 使用 store 的方法设置当前账户
            walletStore.setCurrentAccount(wallet)
            setSelectedWalletId(wallet.accountId)

            // 设置显示名称
            const accountName = wallet.name || (t('home.addressLabel') || '地址{number}').replace('{number}', String(Number(index) + 1))
            setCurrentAccount({
              ...wallet,
              accountName,
            })
          }
        }}
      />
    </YStack>
  )
}
